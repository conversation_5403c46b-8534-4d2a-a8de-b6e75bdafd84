# 进销存管理系统需求文档

## 文档结构说明

本项目采用标准软件工程文档分类体系，文档编号规则如下：

### 文档分类编号规则

- **PRD (Product Requirements Document)** - 产品需求文档
- **FRD (Functional Requirements Document)** - 功能需求文档
- **SDD (System Design Document)** - 系统设计文档
- **DDD (Detailed Design Document)** - 详细设计文档
- **DBD (Database Design Document)** - 数据库设计文档
- **SSD (Security System Design)** - 安全设计文档
- **UID (User Interface Design)** - 用户界面设计文档
- **IDD (Implementation & Deployment Document)** - 实施部署文档
- **ADD (Audit Design Document)** - 审计设计文档
- **TPD (Test Plan Document)** - 测试计划文档

### 文档目录

```
doc/
├── README.md                           # 文档说明
├── 01-product-requirements/            # 产品需求文档
│   └── PRD-001-项目概述.md
├── 02-functional-requirements/         # 功能需求文档
│   └── FRD-001-功能需求规格说明书.md
├── 03-system-design/                   # 系统设计文档
│   └── SDD-001-系统架构设计.md
├── 04-detailed-design/                 # 🔧 详细设计文档
│   ├── DDD-001-详细设计.md             # 📋 详细设计总览和导航
│   ├── DDD-002-用户管理模块.md         # 👥 用户管理
│   ├── DDD-003-基础数据管理模块.md     # 🏗️ 基础数据管理
│   ├── DDD-004-采购管理模块.md         # 📦 采购管理
│   ├── DDD-005-销售管理模块.md         # 💰 销售管理
│   ├── DDD-006-库存管理模块.md         # 📊 库存管理
│   ├── DDD-007-财务管理模块.md         # 💳 财务管理
│   ├── DDD-008-报表分析模块.md         # 📈 报表分析
│   └── DDD-009-系统管理模块.md         # ⚙️ 系统管理
├── 05-database-design/                 # 数据库设计文档
│   └── DBD-001-数据库设计.md
├── 06-security-design/                 # 安全设计文档
│   └── SSD-001-安全设计.md
├── 07-ui-design/                       # 用户界面设计文档
│   └── UID-001-界面设计规范.md
├── 08-implementation/                  # 实施部署文档
│   └── IDD-001-实施部署指南.md
├── 09-audit-design/                    # 审计设计文档
│   └── ADD-001-审计设计.md
└── 10-test-plan/                       # 测试计划文档
    └── TPD-001-测试计划.md
```

### 版本控制

- 每个文档都有独立的版本号
- 文档修改时需要更新版本历史
- 重大变更需要在文档头部记录变更日志

### 文档状态

- **草稿 (Draft)** - 初始编写阶段
- **评审 (Review)** - 待评审阶段  
- **已批准 (Approved)** - 正式批准版本
- **已废弃 (Deprecated)** - 已过期文档

## 项目概述

进销存管理系统是一个面向中小企业的库存管理解决方案，采用现代化的微服务架构，主要包含采购管理、销售管理、库存管理、财务管理等核心功能模块。

### 🚀 技术栈

**后端技术栈：**
- Java 21 (LTS)
- Spring Boot 3.4.7
- Spring Cloud 2024.0.1 (Moorgate)
- Maven 3.9.x (多模块管理)
- Nacos 3.0.2 (注册中心和配置中心)
- Apache ShenYu ******* (API网关)
- Apache RocketMQ 5.3.1 (消息中间件)
- MyBatis-Plus 3.5.12
- Spring Security 6.x
- PostgreSQL 17
- Redis 7.x

**前端技术栈：**
- Vue 3 + TypeScript
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router 4
- Vite 构建工具
- Axios HTTP客户端

**基础设施：**
- Docker 容器化
- Nginx 反向代理
- Jenkins CI/CD
- ELK 日志分析
- Prometheus + Grafana 监控

## 文档特色

### 🎯 完整性
- **11个核心文档**：从需求分析到实施部署的全生命周期覆盖
- **标准化编号**：采用软件工程标准的文档分类体系
- **版本控制**：每个文档都有独立的版本管理
- **技术栈更新**：已全面更新为Java21+Spring Boot 3.4.7+Vue3技术栈

### 📊 可视化
- **Mermaid图表**：包含80+个流程图、架构图、ER图等
- **业务流程**：完整的采购、销售、库存管理流程
- **技术架构**：Java微服务架构、Maven多模块、Spring Cloud架构
- **Vue3组件**：现代化的前端组件设计和状态管理

### 🔒 企业级
- **Spring Security**：基于Spring Security 6.x的安全防护体系
- **审计合规**：完整的审计日志和合规检查
- **运维支持**：详细的Java应用部署指南和运维手册
- **PostgreSQL 17**：最新数据库特性和MyBatis-Plus集成

### 💡 实用性
- **Java代码示例**：包含Spring Boot、MyBatis-Plus具体实现
- **Vue3组件**：完整的Vue3+TypeScript+Element Plus组件示例
- **配置文件**：提供完整的Spring Boot、Docker配置示例
- **最佳实践**：遵循Java和Vue3生态最佳实践

## 使用指南

### 项目经理
- 重点阅读：PRD-001、FRD-001、TPD-001
- 关注：项目范围、功能需求、测试计划

### 架构师
- 重点阅读：SDD-001、DDD-001、SSD-001
- 关注：Spring Cloud微服务架构、Maven多模块设计、Spring Security安全架构

### Java开发工程师
- 重点阅读：DDD-001、DBD-001、SSD-001
- 关注：Spring Boot详细设计、MyBatis-Plus数据访问、Spring Security实现

### 前端开发工程师
- 重点阅读：UID-001、DDD-001
- 关注：Vue3组件设计、Element Plus使用、Pinia状态管理

### 运维工程师
- 重点阅读：IDD-001、SSD-001、ADD-001
- 关注：部署指南、安全配置、审计监控

### 测试工程师
- 重点阅读：TPD-001、SSD-001、ADD-001
- 关注：测试计划、安全测试、合规测试

## 联系信息

- 产品经理：[姓名]
- 技术负责人：[姓名]
- 项目开始时间：2025年6月
- 预计完成时间：2025年12月

## 更新日志

| 日期 | 版本 | 更新内容 |
|------|------|----------|
| 2025-06-25 | v1.0 | 完成所有核心文档的初始版本 |
| 2025-06-25 | v2.0 | 全面更新为Java21+Spring Boot 3.4.7+Vue3技术栈 |
| 2025-06-25 | v2.1 | 集成Apache ShenYu网关，升级MyBatis-Plus至3.5.12 |
| 2025-06-25 | v2.2 | 升级ShenYu至*******，补全完整设计文档 |
| 2025-06-25 | v3.0 | 完整实现FRD-001功能需求，补全所有业务模块设计 |
| 2025-06-25 | v3.1 | 重新整理详细设计文档结构，按业务模块清晰分离 |
| 2025-06-25 | v3.2 | 重构DDD-001文档，清理冗余内容，优化总览导航 |
| 2025-06-25 | v3.3 | 更新消息中间件为RocketMQ，添加类图、交互图、时序图 |
| 2025-06-25 | v3.4 | 修复所有模块的Mermaid类图语法，确保正常渲染 |
| 2025-06-25 | v3.5 | 彻底修复Mermaid类图语法，简化方法签名，验证渲染 |
| 2025-06-25 | v4.0 | 重大架构调整：Maven多模块+Nacos+微服务架构 |
| 2025-06-25 | v4.1 | 补全用户管理和系统管理模块，完善8模块架构 |
| 2025-06-25 | v4.2 | 技术栈版本升级：Spring Cloud 2024.0.1 + RocketMQ 5.3.1 + Nacos 3.0.2 |
| 2025-06-25 | v4.3 | 数据库设计修复：统一Schema设计，删除重复文档 |
| 2025-06-25 | v5.0 | 新增零售管理模块：POS销售、会员管理、促销活动 |

### v2.0 技术栈更新详情

**后端更新：**
- ✅ 更新为Java 21 + Spring Boot 3.4.7
- ✅ 集成MyBatis-Plus 3.5.5 + PostgreSQL 17
- ✅ 采用Spring Cloud 2024.0.0微服务架构
- ✅ 使用Spring Security 6.x安全框架
- ✅ Maven多模块项目结构设计

**前端更新：**
- ✅ 更新为Vue 3 + TypeScript
- ✅ 使用Element Plus UI组件库
- ✅ 集成Pinia状态管理
- ✅ Vite构建工具配置
- ✅ 完整的Vue3组件设计示例

**部署更新：**
- ✅ Java应用Docker容器化
- ✅ Spring Boot配置文件示例
- ✅ 微服务部署架构
- ✅ PostgreSQL 17特性应用

### v2.1 网关和ORM升级详情

**网关升级：**
- ✅ 集成Apache ShenYu 2.6.1高性能网关
- ✅ 插件化架构：认证、限流、熔断、监控
- ✅ 动态配置和可视化管理
- ✅ WebFlux响应式编程支持
- ✅ 多协议支持和负载均衡

**MyBatis-Plus升级：**
- ✅ 升级至MyBatis-Plus 3.5.12最新版本
- ✅ 增强的分页查询和JOIN优化
- ✅ 数据权限控制和动态表名
- ✅ 自定义ID生成器和雪花算法
- ✅ 防全表更新和SQL性能规范

### v2.2 完整设计文档补全

**ShenYu网关升级：**
- ✅ 升级至Apache ShenYu *******最新版本
- ✅ 完整的插件系统设计和自定义插件开发
- ✅ 详细的网关配置指南和部署文档
- ✅ JWT认证、限流、缓存、监控插件配置

**微服务治理完善：**
- ✅ 服务注册发现和负载均衡策略
- ✅ 熔断降级和重试机制设计
- ✅ 分布式链路追踪和监控指标
- ✅ 配置中心和动态配置管理

**业务模块补全：**
- ✅ 完整的领域模型设计（DDD）
- ✅ 订单状态机和业务流程设计
- ✅ 库存管理和事务处理机制
- ✅ 事件驱动架构和异步处理

**运维监控增强：**
- ✅ 结构化日志和日志聚合
- ✅ 业务监控指标和系统监控
- ✅ 分布式链路追踪集成
- ✅ 配置管理和动态刷新

### v3.0 完整业务功能实现

**功能需求完整实现：**
- ✅ 完全对应FRD-001功能需求规格说明书
- ✅ 基础数据管理：商品、客户、供应商、仓库管理
- ✅ 采购管理：采购订单、采购入库、采购退货
- ✅ 销售管理：销售订单、销售出库、销售退货
- ✅ 库存管理：库存查询、库存盘点、库存预警、库存调拨
- ✅ 财务管理：应收应付、成本核算、财务对账
- ✅ 报表分析：销售报表、采购报表、库存报表、财务报表

**详细设计文档补全：**
- ✅ DDD-001：核心业务模块详细设计
- ✅ DDD-002：销售库存财务模块设计
- ✅ DDD-003：库存财务报表模块设计
- ✅ DDD-004：财务管理服务设计
- ✅ DDD-005：报表分析模块设计

**数据库设计完善：**
- ✅ 完整的业务表结构设计
- ✅ 财务管理相关表设计
- ✅ 库存管理扩展表设计
- ✅ 销售出库和库存盘点表设计
- ✅ 完整的索引和约束设计

**业务流程完整性：**
- ✅ 采购到入库完整流程
- ✅ 销售到出库完整流程
- ✅ 库存管理完整流程
- ✅ 财务管理完整流程
- ✅ 报表分析完整功能

### v3.1 详细设计文档重构

**文档结构优化：**
- ✅ 按业务模块清晰分离详细设计文档
- ✅ DDD-001作为总览和导航文档
- ✅ 6个独立的业务模块设计文档
- ✅ 每个模块文档内容完整独立
- ✅ 模块间关系清晰明确

**模块化设计文档：**
- ✅ DDD-002：基础数据管理模块（商品、客户、供应商、仓库）
- ✅ DDD-003：采购管理模块（采购订单、入库、退货）
- ✅ DDD-004：销售管理模块（销售订单、出库、退货）
- ✅ DDD-005：库存管理模块（查询、盘点、预警、调拨）
- ✅ DDD-006：财务管理模块（应收应付、成本核算、对账）
- ✅ DDD-007：报表分析模块（销售、采购、库存、财务报表）

**文档质量提升：**
- ✅ 每个模块文档结构统一
- ✅ 完整的实体设计和服务实现
- ✅ 清晰的业务方法和逻辑
- ✅ 详细的代码示例和注释
- ✅ 模块间依赖关系图表

### v3.2 DDD-001文档重构

**总览文档优化：**
- ✅ 清理DDD-001中的冗余业务代码
- ✅ 重新设计为纯总览和导航文档
- ✅ 突出模块化设计文档导航
- ✅ 优化系统架构概述
- ✅ 简化技术栈说明

**文档内容重构：**
- ✅ 移除已分离到各模块的具体实现代码
- ✅ 保留核心架构设计和设计原则
- ✅ 增强模块概述和职责说明
- ✅ 添加开发指南和团队分工建议
- ✅ 优化文档使用说明

**导航体验提升：**
- ✅ 清晰的模块关系图
- ✅ 完整的文档导航表
- ✅ 明确的开发顺序建议
- ✅ 详细的团队分工指导

### v3.3 技术架构升级和设计图表完善

**技术栈更新：**
- ✅ 消息中间件从RabbitMQ升级为Apache Kafka 3.6.0
- ✅ 增强事件驱动架构设计
- ✅ 完善微服务间异步通信机制
- ✅ 优化系统架构图和部署架构

**设计图表完善：**
- ✅ 添加核心领域类图设计
- ✅ 添加业务流程时序图
- ✅ 添加模块交互图设计
- ✅ 添加事件驱动架构图
- ✅ 添加微服务部署架构图

**每个模块新增图表：**
- ✅ **基础数据管理**：商品管理类图、客户管理时序图
- ✅ **采购管理**：采购流程类图、订单创建时序图、入库确认时序图
- ✅ **销售管理**：销售流程类图、订单创建时序图、出库确认时序图
- ✅ **库存管理**：库存核心类图、库存调整时序图、预警检查时序图
- ✅ **财务管理**：财务核心类图、应收创建时序图、付款记录时序图
- ✅ **报表分析**：报表服务类图、报表生成时序图、定时报表时序图

**事件驱动设计：**
- ✅ 完整的Kafka事件发布订阅机制
- ✅ 领域事件定义和处理器实现
- ✅ 异步事件处理和错误处理
- ✅ 事件驱动的业务流程协调
- ✅ 跨模块的事件通信设计

### v3.4 Mermaid类图语法修复

**类图渲染问题修复：**
- ✅ 修复所有模块中的Mermaid类图语法错误
- ✅ 统一方法返回类型标注格式
- ✅ 修复泛型类型表示方法
- ✅ 优化类关系连接线语法
- ✅ 确保所有类图能正常渲染显示

**修复内容：**
- ✅ **DDD-001**：核心领域类图语法修复
- ✅ **DDD-002**：商品管理类图语法修复
- ✅ **DDD-003**：采购管理类图语法修复
- ✅ **DDD-004**：销售管理类图语法修复
- ✅ **DDD-005**：库存管理类图语法修复
- ✅ **DDD-006**：财务管理类图语法修复
- ✅ **DDD-007**：报表分析类图语法修复

**语法改进：**
- ✅ 方法返回类型明确标注（如：`void`、`boolean`、`BigDecimal`）
- ✅ 泛型类型简化表示（如：`List~Type~` 改为 `List`）
- ✅ 关系连接线标准化（如：`belongs to` 改为 `belongs_to`）
- ✅ 类图结构层次清晰化
- ✅ 添加类图渲染测试文档

### v3.5 Mermaid类图语法彻底修复

**核心问题解决：**
- ✅ 移除所有方法参数和返回类型标注
- ✅ 简化方法签名为纯方法名
- ✅ 移除关系连接线上的标签文本
- ✅ 统一类图语法格式
- ✅ 通过render-mermaid工具验证渲染

**修复策略：**
- ✅ **方法简化**：`+updatePrice(costPrice, salePrice) void` → `+updatePrice()`
- ✅ **关系简化**：`Customer ||--o{ SalesOrder : places` → `Customer ||--o{ SalesOrder`
- ✅ **类型简化**：移除复杂的泛型类型标注
- ✅ **语法标准化**：遵循最基础的Mermaid类图语法

**验证结果：**
- ✅ 基础类图：正常渲染 ✓
- ✅ 继承关系：正常渲染 ✓
- ✅ 复杂关系：正常渲染 ✓
- ✅ 所有模块类图：语法统一 ✓

**最终效果：**
- ✅ 所有7个模块的类图都能正常渲染
- ✅ 类图结构清晰，关系明确
- ✅ 兼容各种Mermaid渲染器
- ✅ 文档可读性和可维护性提升

### v4.0 系统架构重大升级

**技术栈全面升级：**
- ✅ **Maven多模块管理**：统一依赖版本，模块化开发
- ✅ **Nacos注册中心**：服务注册发现和配置管理
- ✅ **Spring Cloud生态**：完整的微服务解决方案
- ✅ **微服务架构**：按业务边界清晰拆分服务
- ✅ **ShenYu网关**：统一API网关和路由管理

**Maven项目结构：**
- ✅ **erp-common**：公共模块（core、security、redis、kafka、web）
- ✅ **erp-services**：6个业务微服务模块
- ✅ **erp-api**：API接口定义模块
- ✅ **erp-gateway**：ShenYu网关模块
- ✅ **父POM管理**：统一依赖版本和构建配置

**Nacos配置管理：**
- ✅ **命名空间隔离**：erp-system业务命名空间
- ✅ **配置分组管理**：COMMON_GROUP、DEFAULT_GROUP、GATEWAY_GROUP
- ✅ **动态配置刷新**：@RefreshScope支持配置热更新
- ✅ **环境配置分离**：开发、测试、生产环境配置隔离

**微服务设计：**
- ✅ **服务注册发现**：基于Nacos的服务治理
- ✅ **Feign客户端**：声明式服务间调用
- ✅ **配置管理**：集中化配置管理和动态刷新
- ✅ **分布式事务**：Seata分布式事务处理
- ✅ **事件驱动**：Kafka异步事件处理

**部署架构优化：**
- ✅ **Nacos集群**：3节点高可用注册中心
- ✅ **微服务集群**：每个服务支持多实例部署
- ✅ **负载均衡**：Nginx + ShenYu网关集群
- ✅ **中间件集群**：PostgreSQL主从、Redis集群、Kafka集群
- ✅ **环境配置**：开发、测试、生产环境部署指南

**开发指南完善：**
- ✅ **微服务开发顺序**：5个阶段的开发计划
- ✅ **团队分工建议**：7个专业团队的职责划分
- ✅ **环境配置指南**：详细的环境搭建说明
- ✅ **代码示例**：完整的微服务开发示例

### v4.1 业务模块完善

**模块架构完善：**
- ✅ **补全用户管理模块**：用户注册登录、角色权限、组织架构
- ✅ **补全系统管理模块**：系统配置、数据备份、日志管理、监控
- ✅ **8个完整模块**：与功能需求文档完全对应
- ✅ **微服务架构调整**：8个独立的微服务
- ✅ **Maven项目结构**：完整的多模块项目结构

**用户管理模块 (DDD-002)：**
- ✅ **用户实体设计**：完整的用户信息管理
- ✅ **角色权限体系**：RBAC权限控制模型
- ✅ **组织架构管理**：部门层级和用户分配
- ✅ **认证授权服务**：JWT Token和Redis缓存
- ✅ **服务名称**：`erp-user-service`

**系统管理模块 (DDD-009)：**
- ✅ **系统配置管理**：参数配置和字典管理
- ✅ **数据备份恢复**：定时备份和恢复策略
- ✅ **操作日志管理**：用户操作和系统日志
- ✅ **系统监控**：性能监控和健康检查
- ✅ **服务名称**：`erp-system-service`

**架构调整：**
- ✅ **微服务数量**：从6个增加到8个微服务
- ✅ **团队分工**：从7个增加到9个专业团队
- ✅ **开发阶段**：从5个增加到6个开发阶段
- ✅ **Nacos配置**：增加用户和系统服务配置
- ✅ **部署架构**：支持8个微服务的集群部署

**文档完善：**
- ✅ **DDD-002**：用户管理模块详细设计（300行）
- ✅ **DDD-009**：系统管理模块详细设计（300行）
- ✅ **实体设计**：完整的JPA实体和业务方法
- ✅ **服务实现**：详细的业务逻辑代码
- ✅ **权限模型**：RBAC角色权限控制

### v4.2 技术栈版本升级

**核心框架升级：**
- ✅ **Spring Cloud 2024.0.1 (Moorgate)**：最新稳定版本，完全兼容Spring Boot 3.4.7
- ✅ **Apache Kafka 3.9.1**：最新稳定版本，性能和稳定性大幅提升
- ✅ **Nacos 3.0.2**：最新版本，支持Spring Boot 3.4.x

**版本兼容性验证：**
- ✅ **Spring Boot 3.4.7** ←→ **Spring Cloud 2024.0.1**：官方兼容
- ✅ **Spring Cloud 2024.0.1** ←→ **Nacos 3.0.2**：完全支持
- ✅ **Java 21** ←→ **Kafka 3.9.1**：原生支持
- ✅ **Maven 3.9.x** ←→ **所有组件**：构建兼容

**技术栈优势：**
- ✅ **最新稳定版本**：所有组件都是2024-2025年最新稳定版
- ✅ **长期支持**：Java 21 LTS + Spring Boot 3.4.x 长期维护
- ✅ **性能优化**：新版本带来的性能和稳定性提升
- ✅ **安全更新**：最新的安全补丁和漏洞修复
- ✅ **生态完整**：Spring Cloud 2024.0.1 提供完整的微服务生态

**升级收益：**
- ✅ **Kafka 3.9.1**：更好的性能、更低的延迟、更强的稳定性
- ✅ **Spring Cloud 2024.0.1**：改进的服务发现、配置管理、负载均衡
- ✅ **Nacos 3.0.2**：更好的集群管理、配置热更新、服务治理
- ✅ **向前兼容**：为未来的技术升级奠定基础

### v4.3 数据库设计修复

**文档整理：**
- ✅ **删除重复文档**：移除04-database-design重复目录
- ✅ **统一数据库设计**：保留05-database-design作为唯一数据库设计文档
- ✅ **版本同步**：数据库设计与详细设计文档保持一致

**数据库架构优化：**
- ✅ **Schema隔离设计**：8个微服务独立Schema
- ✅ **技术栈更新**：PostgreSQL 17 + MyBatis-Plus 3.5.12
- ✅ **配置优化**：Spring Boot 3.4.7数据源配置
- ✅ **缓存集成**：Redis 7.x缓存策略
- ✅ **消息队列**：Kafka 3.9.1事件流设计

**数据库设计特色：**

| Schema | 微服务 | 设计特点 |
|--------|--------|----------|
| **pisp_user** | 用户管理服务 | RBAC权限模型，JWT认证支持 |
| **pisp_base_data** | 基础数据服务 | 主数据管理，数据一致性保证 |
| **pisp_purchase** | 采购管理服务 | 完整采购流程，工作流支持 |
| **pisp_sales** | 销售管理服务 | 销售全流程，状态机管理 |
| **pisp_inventory** | 库存管理服务 | 实时库存，事件驱动更新 |
| **pisp_finance** | 财务管理服务 | 复式记账，精确成本核算 |
| **pisp_report** | 报表分析服务 | 数据仓库模式，灵活报表 |
| **pisp_system** | 系统管理服务 | 配置中心，系统监控 |

**性能优化策略：**
- ✅ **索引优化**：主键、唯一、复合、部分索引策略
- ✅ **分区设计**：时间分区、范围分区支持
- ✅ **缓存策略**：热点数据缓存、查询缓存、分布式锁
- ✅ **连接池优化**：HikariCP连接池调优

**数据安全保障：**
- ✅ **数据完整性**：外键约束、检查约束、触发器
- ✅ **数据安全**：敏感数据加密、访问控制、审计日志
- ✅ **备份恢复**：定期备份和恢复测试
- ✅ **监控告警**：性能监控、慢查询告警、空间告警

### v5.0 零售管理模块

**业务扩展：**
- ✅ **零售业务支持**：新增B2C零售业务模块
- ✅ **POS销售系统**：完整的POS销售流程
- ✅ **会员管理体系**：会员注册、积分、等级管理
- ✅ **促销活动引擎**：灵活的促销规则和优惠计算
- ✅ **门店管理**：多门店支持和门店运营管理

**零售vs传统销售对比：**

| 特性 | 零售业务 (B2C) | 传统销售 (B2B) |
|------|----------------|----------------|
| **客户类型** | 个人消费者 | 企业客户 |
| **交易特点** | 高频、小额、即时 | 低频、大额、流程化 |
| **支付方式** | 现金、刷卡、移动支付 | 赊销、银行转账 |
| **库存管理** | 实时扣减 | 预留后出库 |
| **价格策略** | 统一零售价、促销价 | 客户专属价格 |
| **会员体系** | 积分、等级、优惠券 | 信用额度管理 |

**核心功能模块：**

#### 🛍️ POS销售管理
- ✅ **销售流程**：商品扫码 → 价格计算 → 促销优惠 → 会员积分 → 支付处理
- ✅ **支付方式**：现金、银行卡、支付宝、微信支付、积分支付、混合支付
- ✅ **小票管理**：自动生成小票号、支持小票打印
- ✅ **实时库存**：销售完成即时扣减库存
- ✅ **收银员管理**：收银员权限控制和销售统计

#### 👥 会员管理体系
- ✅ **会员注册**：手机号注册、基本信息管理
- ✅ **会员等级**：铜牌、银牌、金牌、白金四级体系
- ✅ **积分系统**：消费积分、积分使用、积分过期管理
- ✅ **会员权益**：等级折扣、生日优惠、专属活动
- ✅ **消费统计**：消费历史、消费排名、消费分析

#### 🎁 促销活动引擎
- ✅ **促销类型**：百分比折扣、固定金额减免、买X送Y、满减优惠
- ✅ **适用范围**：指定商品、会员等级、时间段限制
- ✅ **使用限制**：使用次数限制、最低消费金额、最大优惠金额
- ✅ **自动计算**：智能促销匹配和优惠金额计算
- ✅ **活动统计**：促销效果分析和使用统计

#### 🏪 门店管理
- ✅ **门店信息**：门店基本信息、营业时间、联系方式
- ✅ **门店类型**：直营店、加盟店、专卖店等类型支持
- ✅ **POS设备**：门店POS设备数量和状态管理
- ✅ **仓库关联**：门店与仓库的关联关系
- ✅ **营业状态**：门店营业状态和运营管理

**技术架构特色：**
- ✅ **微服务架构**：独立的零售管理服务 (pisp-retail-service)
- ✅ **事件驱动**：与库存、财务服务的事件集成
- ✅ **高性能缓存**：会员信息、促销规则Redis缓存
- ✅ **实时处理**：POS销售实时处理和响应
- ✅ **数据一致性**：分布式事务保证数据一致性

**数据库设计：**
- ✅ **独立Schema**：pisp_retail独立数据库Schema
- ✅ **核心表结构**：POS销售、会员、促销、门店等8张核心表
- ✅ **索引优化**：针对高频查询的索引优化
- ✅ **分区策略**：大表分区存储和查询优化

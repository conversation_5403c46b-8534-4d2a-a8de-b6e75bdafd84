# DBD-001 PISP进销存管理系统数据库设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DBD-001 |
| 文档名称 | PISP进销存管理系统数据库设计 |
| 版本号 | v2.1 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 正式 |
| 作者 | 数据库架构师 |

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-25 | 初始版本创建 | 数据库架构师 |
| v2.0 | 2025-06-25 | 更新技术栈版本，完善8模块数据库设计 | 数据库架构师 |
| v2.1 | 2025-07-02 | 增加前置仓库存管理相关表设计 | 数据库架构师 |

## 1. 数据库概述

### 1.1 设计原则

- **规范化设计：** 遵循第三范式，减少数据冗余
- **性能优化：** 合理设计索引，优化查询性能
- **扩展性：** 支持水平和垂直扩展
- **数据完整性：** 通过约束保证数据一致性
- **安全性：** 敏感数据加密存储

### 1.2 技术选型

**数据库技术栈 (v4.2)：**
- **主数据库：** PostgreSQL 17 (最新稳定版)
- **ORM框架：** MyBatis-Plus 3.5.12
- **缓存数据库：** Redis 7.x
- **连接池：** HikariCP (Spring Boot 3.4.7默认)
- **数据库迁移：** Liquibase 4.x
- **消息队列：** Apache RocketMQ 5.3.1

**微服务架构：**
- **Java版本：** 21 (LTS)
- **Spring Boot：** 3.4.7
- **Spring Cloud：** 2024.0.1 (Moorgate)
- **服务注册：** Nacos 3.0.2

### 1.3 Liquibase数据库迁移配置

**Liquibase 4.x集成配置：**

```yaml
# application.yml
spring:
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true
    drop-first: false
    contexts: ${spring.profiles.active:dev}
    default-schema: public
    liquibase-schema: liquibase
    database-change-log-table: databasechangelog
    database-change-log-lock-table: databasechangeloglock
    test-rollback-on-update: false
    tag: v1.0.0
```

**Maven依赖配置：**

```xml
<dependency>
    <groupId>org.liquibase</groupId>
    <artifactId>liquibase-core</artifactId>
    <version>4.29.2</version>
</dependency>
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <scope>runtime</scope>
</dependency>
```

**Liquibase Maven插件：**

```xml
<plugin>
    <groupId>org.liquibase</groupId>
    <artifactId>liquibase-maven-plugin</artifactId>
    <version>4.29.2</version>
    <configuration>
        <propertyFile>src/main/resources/liquibase.properties</propertyFile>
        <changeLogFile>src/main/resources/db/changelog/db.changelog-master.xml</changeLogFile>
    </configuration>
</plugin>
```

## 2. 数据库架构

### 2.1 微服务数据库架构

```mermaid
graph TB
    subgraph "微服务层"
        US[用户管理服务<br/>pisp-user-service]
        BD[基础数据服务<br/>pisp-base-data-service]
        PS[采购管理服务<br/>pisp-purchase-service]
        SS[销售管理服务<br/>pisp-sales-service]
        IS[库存管理服务<br/>pisp-inventory-service]
        FS[财务管理服务<br/>pisp-finance-service]
        RS[报表分析服务<br/>pisp-report-service]
        SYS[系统管理服务<br/>pisp-system-service]
    end

    subgraph "数据访问层"
        MP[MyBatis-Plus 3.5.12<br/>ORM框架]
        HC[HikariCP<br/>连接池]
        SC[Spring Cache<br/>缓存抽象]
    end

    subgraph "PostgreSQL 17 - Schema隔离"
        DB1[(pisp_user<br/>用户数据)]
        DB2[(pisp_base_data<br/>基础数据)]
        DB3[(pisp_purchase<br/>采购数据)]
        DB4[(pisp_sales<br/>销售数据)]
        DB5[(pisp_inventory<br/>库存数据)]
        DB6[(pisp_finance<br/>财务数据)]
        DB7[(pisp_report<br/>报表数据)]
        DB8[(pisp_system<br/>系统数据)]
    end

    subgraph "Redis 7.x - 缓存分区"
        R1[用户会话缓存]
        R2[基础数据缓存]
        R3[业务数据缓存]
        R4[报表数据缓存]
    end

    subgraph "RocketMQ 5.3.1 - 事件流"
        K1[用户事件流]
        K2[业务事件流]
        K3[库存事件流]
        K4[财务事件流]
    end

    %% 微服务到数据访问层
    US --> MP
    BD --> MP
    PS --> MP
    SS --> MP
    IS --> MP
    FS --> MP
    RS --> MP
    SYS --> MP

    %% 数据访问层到数据库
    MP --> HC
    HC --> DB1
    HC --> DB2
    HC --> DB3
    HC --> DB4
    HC --> DB5
    HC --> DB6
    HC --> DB7
    HC --> DB8

    %% 缓存访问
    US --> SC
    BD --> SC
    PS --> SC
    SS --> SC

    SC --> R1
    SC --> R2
    SC --> R3
    SC --> R4

    %% 事件流
    US --> K1
    PS --> K2
    SS --> K2
    IS --> K3
    FS --> K4
```

### 2.2 Schema隔离策略

**PostgreSQL 17 Schema设计：**

| Schema名称 | 对应微服务 | 主要表 | 数据特点 |
|------------|------------|--------|----------|
| **pisp_user** | 用户管理服务 | users, roles, permissions, departments | 用户权限数据，读多写少 |
| **pisp_base_data** | 基础数据服务 | products, customers, suppliers, warehouses, retail_stores | 基础主数据，变更频率低 |
| **pisp_purchase** | 采购管理服务 | purchase_orders, purchase_receipts, purchase_returns | 采购业务数据，事务性强 |
| **pisp_sales** | 销售管理服务 | sales_orders, sales_shipments, sales_returns | 销售业务数据，并发量高 |
| **pisp_inventory** | 库存管理服务 | inventories, inventory_transactions, inventory_checks | 库存数据，实时性要求高 |
| **pisp_finance** | 财务管理服务 | accounts_receivable, accounts_payable, cost_calculations | 财务数据，准确性要求高 |
| **pisp_report** | 报表分析服务 | report_configs, report_data, report_cache | 报表数据，查询密集型 |
| **pisp_system** | 系统管理服务 | sys_configs, data_backups, operation_logs | 系统数据，管理性质 |
| **pisp_retail** | 零售管理服务 | retail_pos_sales, retail_members, retail_promotions | 零售数据，高频交易 |

```mermaid
graph TB
    subgraph "PostgreSQL 17 数据库实例"
        subgraph "pisp_user Schema"
            U1[users]
            U2[roles]
            U3[permissions]
            U4[departments]
        end

        subgraph "pisp_base_data Schema"
            B1[products]
            B2[customers]
            B3[suppliers]
            B4[warehouses]
            B5[retail_stores]
        end

        subgraph "pisp_purchase Schema"
            P1[purchase_orders]
            P2[purchase_receipts]
            P3[purchase_returns]
        end

        subgraph "pisp_sales Schema"
            S1[sales_orders]
            S2[sales_shipments]
            S3[sales_returns]
        end

        subgraph "pisp_inventory Schema"
            I1[inventories]
            I2[inventory_transactions]
            I3[inventory_checks]
        end

        subgraph "pisp_finance Schema"
            F1[accounts_receivable]
            F2[accounts_payable]
            F3[cost_calculations]
        end

        subgraph "pisp_report Schema"
            R1[report_configs]
            R2[report_data]
            R3[report_cache]
        end

        subgraph "pisp_system Schema"
            SY1[sys_configs]
            SY2[data_backups]
            SY3[operation_logs]
        end

        subgraph "pisp_retail Schema"
            RT1[retail_pos_sales]
            RT2[retail_members]
            RT3[retail_promotions]
        end
    end
```

## 3. 数据库配置

### 3.1 Spring Boot 3.4.7 数据源配置

**application.yml配置：**
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: ****************************************************************************************************************
    username: ${DB_USERNAME:pisp_user}
    password: ${DB_PASSWORD:pisp_password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-${spring.application.name}
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000

# MyBatis-Plus 3.5.12配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    default-executor-type: reuse
    default-statement-timeout: 25000
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: NOT_NULL
      insert-strategy: NOT_NULL
      select-strategy: NOT_EMPTY
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.pisp.*.entity

# Redis 7.x配置
spring:
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
      cluster:
        nodes: ${REDIS_CLUSTER_NODES:}
        max-redirects: 3

# RocketMQ 5.3.1配置
rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: ${spring.application.name}-producer
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    max-message-size: 4194304
    compress-message-body-threshold: 4096
  consumer:
    group: ${spring.application.name}-consumer
    consume-thread-min: 5
    consume-thread-max: 32
    consume-message-batch-max-size: 1
    pull-batch-size: 32
```

### 3.2 MyBatis-Plus 3.5.12增强配置

```java
@Configuration
@EnableTransactionManagement
@MapperScan("com.pisp.*.mapper")
public class MybatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件 (3.5.12优化)
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.POSTGRE_SQL);
        paginationInterceptor.setMaxLimit(1000L);
        paginationInterceptor.setOverflow(false);
        paginationInterceptor.setOptimizeJoin(true); // 3.5.12新增：优化JOIN查询
        paginationInterceptor.setOptimizeCountSql(true); // 优化COUNT查询
        interceptor.addInnerInterceptor(paginationInterceptor);

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        // 数据权限插件 (3.5.12增强)
        DataPermissionInterceptor dataPermissionInterceptor = new DataPermissionInterceptor();
        dataPermissionInterceptor.setDataPermissionHandler(new CustomDataPermissionHandler());
        interceptor.addInnerInterceptor(dataPermissionInterceptor);

        // 防全表更新与删除插件
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());

        // SQL性能规范插件
        interceptor.addInnerInterceptor(new IllegalSQLInnerInterceptor());

        // 动态表名插件 (3.5.12新特性)
        DynamicTableNameInnerInterceptor dynamicTableNameInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInterceptor.setTableNameHandler((sql, tableName) -> {
            // 根据业务逻辑动态替换表名
            if ("orders".equals(tableName)) {
                return getOrderTableName();
            }
            return tableName;
        });
        interceptor.addInnerInterceptor(dynamicTableNameInterceptor);

        return interceptor;
    }

    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MyMetaObjectHandler();
    }

    @Bean
    public ISqlInjector sqlInjector() {
        return new DefaultSqlInjector();
    }

    // 3.5.12新特性：自定义ID生成器
    @Bean
    public IdentifierGenerator identifierGenerator() {
        return new CustomSnowflakeIdGenerator();
    }

    // 3.5.12新特性：数据库函数注册
    @Bean
    public DatabaseIdProvider databaseIdProvider() {
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        properties.put("PostgreSQL", "postgresql");
        properties.put("MySQL", "mysql");
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }

    private String getOrderTableName() {
        // 根据当前时间或其他业务逻辑返回表名
        String yearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "orders_" + yearMonth;
    }
}

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "createBy", Long.class, getCurrentUserId());
        this.strictInsertFill(metaObject, "updateBy", Long.class, getCurrentUserId());
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
        this.strictInsertFill(metaObject, "version", Integer.class, 1);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updateBy", Long.class, getCurrentUserId());
    }

    private Long getCurrentUserId() {
        // 从SecurityContext获取当前用户ID
        return SecurityUtils.getCurrentUserId();
    }
}
```

### 3.3 MyBatis-Plus 3.5.12新特性

**1. 增强的分页查询：**
```java
// 优化JOIN查询的分页
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public IPage<ProductVO> getProductsWithCategory(Page<Product> page, ProductQueryDTO query) {
        // 3.5.12自动优化JOIN查询的COUNT语句
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(query.getName()), Product::getName, query.getName())
               .eq(query.getCategoryId() != null, Product::getCategoryId, query.getCategoryId())
               .eq(Product::getDeleted, 0);

        // 使用优化的分页查询
        IPage<Product> productPage = this.page(page, wrapper);

        // 转换为VO
        return productPage.convert(this::convertToVO);
    }
}
```

**2. 数据权限控制：**
```java
// 基于注解的数据权限
@DataPermission(
    value = @DataColumn(key = "deptName", value = "dept_id"),
    ignore = @IgnoreStrategy(key = "admin", value = "true")
)
@Select("SELECT * FROM user WHERE deleted = 0")
List<User> selectUserList();

// 自定义数据权限处理器
@Component
public class CustomDataPermissionHandler implements DataPermissionHandler {

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        UserPrincipal user = SecurityUtils.getCurrentUser();
        if (user == null || user.hasRole("ADMIN")) {
            return where;
        }

        // 根据用户部门过滤数据
        Expression deptPermission = new EqualsTo(
            new Column("dept_id"),
            new LongValue(user.getDeptId())
        );

        return where == null ? deptPermission : new AndExpression(where, deptPermission);
    }
}
```

**3. 动态表名支持：**
```java
// 动态表名配置
@Component
public class DynamicTableNameHandler implements TableNameHandler {

    @Override
    public String dynamicTableName(String sql, String tableName) {
        // 订单表按月分表
        if ("orders".equals(tableName)) {
            String suffix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            return tableName + "_" + suffix;
        }

        // 日志表按日分表
        if ("operation_log".equals(tableName)) {
            String suffix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            return tableName + "_" + suffix;
        }

        return tableName;
    }
}

// 使用动态表名
@TableName(value = "orders", autoResultMap = true)
public class Order extends BaseEntity {
    // 实体定义
}
```

**4. 自定义ID生成器：**
```java
@Component
public class CustomSnowflakeIdGenerator implements IdentifierGenerator {

    private final Snowflake snowflake;

    public CustomSnowflakeIdGenerator() {
        // 从配置中获取机器ID
        long workerId = getWorkerId();
        long datacenterId = getDatacenterId();
        this.snowflake = IdUtil.getSnowflake(workerId, datacenterId);
    }

    @Override
    public Long nextId(Object entity) {
        return snowflake.nextId();
    }

    @Override
    public String nextUUID(Object entity) {
        return IdUtil.fastSimpleUUID();
    }
}
```

**5. 增强的条件构造器：**
```java
// 3.5.12新增的条件构造方法
@Service
public class ProductSearchService {

    public List<Product> searchProducts(ProductSearchDTO searchDTO) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();

        // 新增的条件构造方法
        wrapper.likeLeft(StringUtils.hasText(searchDTO.getCode()),
                         Product::getCode, searchDTO.getCode())
               .likeRight(StringUtils.hasText(searchDTO.getName()),
                          Product::getName, searchDTO.getName())
               .between(searchDTO.getMinPrice() != null && searchDTO.getMaxPrice() != null,
                       Product::getPrice, searchDTO.getMinPrice(), searchDTO.getMaxPrice())
               .in(CollectionUtils.isNotEmpty(searchDTO.getCategoryIds()),
                   Product::getCategoryId, searchDTO.getCategoryIds())
               .func(i -> {
                   if (searchDTO.getStatus() != null) {
                       i.eq(Product::getStatus, searchDTO.getStatus());
                   }
               });

        return baseMapper.selectList(wrapper);
    }
}
```

### 3.4 PostgreSQL 17新特性应用

**JSON增强支持：**
```sql
-- 使用JSONB类型存储商品规格
CREATE TABLE products (
    id BIGSERIAL PRIMARY KEY,
    specifications JSONB,
    -- 创建GIN索引支持JSONB查询
    CONSTRAINT specifications_gin_idx GIN (specifications)
);

-- JSONB查询示例
SELECT * FROM products
WHERE specifications @> '{"color": "red"}';

-- 使用JSON路径查询
SELECT * FROM products
WHERE specifications #> '{dimensions,width}' = '"100cm"';
```

**分区表支持：**
```sql
-- 按时间分区的订单表
CREATE TABLE orders (
    id BIGSERIAL,
    order_date DATE NOT NULL,
    customer_id BIGINT,
    total_amount DECIMAL(10,2),
    PRIMARY KEY (id, order_date)
) PARTITION BY RANGE (order_date);

-- 创建分区
CREATE TABLE orders_2025_q1 PARTITION OF orders
FOR VALUES FROM ('2025-01-01') TO ('2025-04-01');

CREATE TABLE orders_2025_q2 PARTITION OF orders
FOR VALUES FROM ('2025-04-01') TO ('2025-07-01');
```

## 4. 核心数据模型

### 4.1 用户管理模块

```mermaid
erDiagram
    USERS {
        uuid id PK
        string username UK
        string email UK
        string phone UK
        string password_hash
        string salt
        string full_name
        string avatar_url
        enum status
        timestamp last_login_at
        string create_time
        string update_time
        string creator_id FK
        string updater_id FK
        string additional_info
        string remark
    }
    
    ROLES {
        uuid id PK
        string name UK
        string description
        json permissions
        boolean is_system
        string create_time
        string update_time
    }
    
    USER_ROLES {
        uuid id PK
        uuid user_id FK
        uuid role_id FK
        timestamp assigned_at
        uuid assigned_by FK
    }
    
    ORGANIZATIONS {
        uuid id PK
        string name
        string code UK
        uuid parent_id FK
        int level
        string path
        string description
        enum status
        string create_time
        string update_time
    }
    
    USER_ORGANIZATIONS {
        uuid id PK
        uuid user_id FK
        uuid organization_id FK
        boolean is_primary
        timestamp joined_at
    }
    
    USERS ||--o{ USER_ROLES : has
    ROLES ||--o{ USER_ROLES : belongs_to
    USERS ||--o{ USER_ORGANIZATIONS : belongs_to
    ORGANIZATIONS ||--o{ USER_ORGANIZATIONS : contains
    ORGANIZATIONS ||--o{ ORGANIZATIONS : parent_of
```

**用户实体类：**
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("users")
public class User extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("username")
    private String username;

    @TableField("email")
    private String email;

    @TableField("phone")
    private String phone;

    @TableField("password_hash")
    private String passwordHash;

    @TableField("full_name")
    private String fullName;

    @TableField("avatar_url")
    private String avatarUrl;

    @TableField("status")
    @EnumValue
    private UserStatus status;

    @TableField("last_login_at")
    private LocalDateTime lastLoginAt;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}

@Data
public abstract class BaseEntity {

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
}
```

### 3.2 商品管理模块

```mermaid
erDiagram
    PRODUCTS {
        uuid id PK
        string code UK
        string name
        string barcode
        uuid category_id FK
        string unit
        decimal cost_price
        decimal sale_price
        decimal weight
        string dimensions
        text description
        json specifications
        enum status
        boolean is_serialized
        string create_time
        string update_time
        string creator_id FK
    }
    
    CATEGORIES {
        uuid id PK
        string name
        string code UK
        uuid parent_id FK
        int level
        string path
        text description
        json attributes
        int sort_order
        enum status
        string create_time
        string update_time
    }
    
    PRODUCT_IMAGES {
        uuid id PK
        uuid product_id FK
        string url
        string alt_text
        boolean is_primary
        int sort_order
        string create_time
    }
    
    PRODUCT_VARIANTS {
        uuid id PK
        uuid product_id FK
        string sku UK
        string variant_name
        json attributes
        decimal price_adjustment
        decimal cost_adjustment
        enum status
        string create_time
        string update_time
    }
    
    SUPPLIERS {
        uuid id PK
        string code UK
        string name
        string contact_person
        string phone
        string email
        text address
        string tax_number
        enum status
        decimal credit_limit
        int payment_terms
        json bank_info
        string create_time
        string update_time
    }
    
    PRODUCT_SUPPLIERS {
        uuid id PK
        uuid product_id FK
        uuid supplier_id FK
        string supplier_sku
        decimal supply_price
        int min_order_qty
        int lead_time_days
        boolean is_preferred
        string create_time
        string update_time
    }
    
    PRODUCTS ||--|| CATEGORIES : belongs_to
    PRODUCTS ||--o{ PRODUCT_IMAGES : has
    PRODUCTS ||--o{ PRODUCT_VARIANTS : has
    PRODUCTS ||--o{ PRODUCT_SUPPLIERS : supplied_by
    SUPPLIERS ||--o{ PRODUCT_SUPPLIERS : supplies
    CATEGORIES ||--o{ CATEGORIES : parent_of
```

### 3.3 订单管理模块

```mermaid
erDiagram
    PURCHASE_ORDERS {
        string id PK
        string order_number UK
        string supplier_id FK
        string status
        string order_date
        string expected_date
        float total_amount
        float tax_amount
        float discount_amount
        string notes
        string approval_flow
        string creator_id FK
        string approved_by FK
        string approved_at
        string create_time
        string update_time
    }

    PURCHASE_ORDER_ITEMS {
        string id PK
        string order_id FK
        string product_id FK
        string product_variant_id FK
        float quantity
        string unit
        float unit_price
        float total_price
        float received_quantity
        string notes
        string create_time
        string update_time
    }

    SALES_ORDERS {
        string id PK
        string order_number UK
        string customer_id FK
        string status
        string order_date
        string delivery_date
        float total_amount
        float tax_amount
        float discount_amount
        string notes
        string shipping_info
        string creator_id FK
        string create_time
        string update_time
    }

    SALES_ORDER_ITEMS {
        string id PK
        string order_id FK
        string product_id FK
        string product_variant_id FK
        float quantity
        string unit
        float unit_price
        float total_price
        float shipped_quantity
        string notes
        string create_time
        string update_time
    }

    CUSTOMERS {
        string id PK
        string code UK
        string name
        string contact_person
        string phone
        string email
        string address
        string tax_number
        string status
        float credit_limit
        int payment_terms
        string shipping_preferences
        string create_time
        string update_time
    }

    PURCHASE_ORDERS ||--o{ PURCHASE_ORDER_ITEMS : contains
    CUSTOMERS ||--o{ SALES_ORDERS : has
    SALES_ORDERS ||--o{ SALES_ORDER_ITEMS : contains
```

### 3.4 库存管理模块

```mermaid
erDiagram
    WAREHOUSES {
        uuid id PK
        string code UK
        string name
        text address
        string manager
        string phone
        enum status
        json settings
        string create_time
        string update_time
    }
    
    INVENTORY {
        uuid id PK
        uuid warehouse_id FK
        uuid product_id FK
        uuid product_variant_id FK
        decimal quantity
        decimal reserved_quantity
        decimal available_quantity
        decimal min_stock
        decimal max_stock
        decimal reorder_point
        decimal last_cost
        decimal avg_cost
        timestamp last_updated
    }
    
    INVENTORY_TRANSACTIONS {
        uuid id PK
        uuid warehouse_id FK
        uuid product_id FK
        uuid product_variant_id FK
        enum transaction_type
        decimal quantity
        decimal unit_cost
        string reference_type
        uuid reference_id
        text notes
        string creator_id FK
        string create_time
    }
    
    STOCK_ADJUSTMENTS {
        uuid id PK
        string adjustment_number UK
        uuid warehouse_id FK
        enum reason
        enum status
        text description
        decimal total_value_change
        string creator_id FK
        uuid approved_by FK
        timestamp approved_at
        string create_time
        string update_time
    }
    
    STOCK_ADJUSTMENT_ITEMS {
        uuid id PK
        uuid adjustment_id FK
        uuid product_id FK
        uuid product_variant_id FK
        decimal old_quantity
        decimal new_quantity
        decimal quantity_change
        decimal unit_cost
        decimal value_change
        text notes
        string create_time
    }
    
    STOCK_TAKES {
        uuid id PK
        string take_number UK
        uuid warehouse_id FK
        enum status
        date scheduled_date
        date started_date
        date completed_date
        text description
        string creator_id FK
        string create_time
        string update_time
    }
    
    STOCK_TAKE_ITEMS {
        uuid id PK
        uuid stock_take_id FK
        uuid product_id FK
        uuid product_variant_id FK
        decimal system_quantity
        decimal counted_quantity
        decimal variance_quantity
        decimal unit_cost
        decimal variance_value
        text notes
        uuid counted_by FK
        timestamp counted_at
    }
    
    WAREHOUSES ||--o{ INVENTORY : stores
    INVENTORY ||--|| PRODUCTS : of
    WAREHOUSES ||--o{ INVENTORY_TRANSACTIONS : in
    INVENTORY_TRANSACTIONS ||--|| PRODUCTS : for
    
    STOCK_ADJUSTMENTS ||--|| WAREHOUSES : in
    STOCK_ADJUSTMENTS ||--o{ STOCK_ADJUSTMENT_ITEMS : contains
    STOCK_ADJUSTMENT_ITEMS ||--|| PRODUCTS : for
    
    STOCK_TAKES ||--|| WAREHOUSES : in
    STOCK_TAKES ||--o{ STOCK_TAKE_ITEMS : contains
    STOCK_TAKE_ITEMS ||--|| PRODUCTS : for
```

## 4. 索引设计

### 4.1 主要索引策略

**用户表索引：**
```sql
-- 主键索引
CREATE UNIQUE INDEX idx_users_id ON users(id);

-- 唯一索引
CREATE UNIQUE INDEX idx_users_username ON users(username);
CREATE UNIQUE INDEX idx_users_email ON users(email);

-- 复合索引
CREATE INDEX idx_users_status_created ON users(status, create_time);
```

**商品表索引：**
```sql
-- 主键和唯一索引
CREATE UNIQUE INDEX idx_products_id ON products(id);
CREATE UNIQUE INDEX idx_products_code ON products(code);

-- 外键索引
CREATE INDEX idx_products_category_id ON products(category_id);

-- 复合索引
CREATE INDEX idx_products_status_category ON products(status, category_id);
CREATE INDEX idx_products_name_code ON products(name, code);
```

**订单表索引：**
```sql
-- 主键和业务索引
CREATE UNIQUE INDEX idx_purchase_orders_id ON purchase_orders(id);
CREATE UNIQUE INDEX idx_purchase_orders_number ON purchase_orders(order_number);

-- 外键索引
CREATE INDEX idx_purchase_orders_supplier ON purchase_orders(supplier_id);
CREATE INDEX idx_purchase_orders_created_by ON purchase_orders(creator_id);

-- 复合索引
CREATE INDEX idx_purchase_orders_status_date ON purchase_orders(status, order_date);
CREATE INDEX idx_purchase_orders_supplier_status ON purchase_orders(supplier_id, status);
```

**库存表索引：**
```sql
-- 复合主键索引
CREATE UNIQUE INDEX idx_inventory_warehouse_product ON inventory(warehouse_id, product_id, product_variant_id);

-- 查询优化索引
CREATE INDEX idx_inventory_product ON inventory(product_id);
CREATE INDEX idx_inventory_low_stock ON inventory(warehouse_id, available_quantity, min_stock);
```

## 5. 数据约束

### 5.1 主要约束规则

**检查约束：**
```sql
-- 数量约束
ALTER TABLE inventory ADD CONSTRAINT chk_inventory_quantity 
CHECK (quantity >= 0 AND reserved_quantity >= 0 AND available_quantity >= 0);

-- 价格约束
ALTER TABLE products ADD CONSTRAINT chk_products_price 
CHECK (cost_price >= 0 AND sale_price >= 0);

-- 状态约束
ALTER TABLE users ADD CONSTRAINT chk_users_status 
CHECK (status IN ('active', 'inactive', 'suspended'));
```

**外键约束：**
```sql
-- 用户角色关联
ALTER TABLE user_roles ADD CONSTRAINT fk_user_roles_user 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 商品分类关联
ALTER TABLE products ADD CONSTRAINT fk_products_category 
FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT;

-- 订单商品关联
ALTER TABLE purchase_order_items ADD CONSTRAINT fk_poi_product 
FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT;
```

## 6. 数据分区策略

### 6.1 时间分区

**订单表按月分区：**
```sql
-- 创建分区表
CREATE TABLE purchase_orders (
    id uuid PRIMARY KEY,
    order_date date NOT NULL,
    -- 其他字段...
) PARTITION BY RANGE (order_date);

-- 创建分区
CREATE TABLE purchase_orders_2025_01 PARTITION OF purchase_orders
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE purchase_orders_2025_02 PARTITION OF purchase_orders
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
```

**库存变动表按月分区：**
```sql
CREATE TABLE inventory_transactions (
    id uuid PRIMARY KEY,
    created_at timestamp NOT NULL,
    -- 其他字段...
) PARTITION BY RANGE (create_time);
```

## 7. 数据备份策略

### 7.1 备份方案

**全量备份：**
- 频率：每周一次
- 保留期：3个月
- 存储位置：异地存储

**增量备份：**
- 频率：每日一次
- 保留期：1个月
- 基于WAL日志

**实时备份：**
- 主从复制
- 读写分离
- 故障自动切换

### 7.2 恢复策略

**Point-in-Time Recovery：**
```sql
-- 恢复到指定时间点
pg_restore --target-time='2025-06-25 10:30:00' backup_file
```

**表级恢复：**
```sql
-- 恢复单个表
pg_restore --table=products backup_file
```

## 8. 性能优化

### 8.1 查询优化

**分页查询优化：**
```sql
-- 使用游标分页
SELECT * FROM products 
WHERE id > $last_id 
ORDER BY id 
LIMIT 20;
```

**统计查询优化：**
```sql
-- 使用物化视图
CREATE MATERIALIZED VIEW mv_inventory_summary AS
SELECT 
    warehouse_id,
    COUNT(*) as product_count,
    SUM(quantity * last_cost) as total_value
FROM inventory 
GROUP BY warehouse_id;
```

### 8.2 缓存策略

**Redis缓存设计：**
```
# 用户信息缓存
user:profile:{user_id} -> JSON
TTL: 1小时

# 商品信息缓存
product:info:{product_id} -> JSON
TTL: 6小时

# 库存数量缓存
inventory:quantity:{warehouse_id}:{product_id} -> Number
TTL: 5分钟

# 热门商品缓存
products:hot -> List
TTL: 1天
```

## 9. 完整业务表设计

### 9.1 基础数据管理表

#### 9.1.1 零售门店表
```sql
CREATE TABLE retail_stores (
    id BIGSERIAL PRIMARY KEY,
    store_code VARCHAR(50) UNIQUE NOT NULL,
    store_name VARCHAR(100) NOT NULL,
    store_type VARCHAR(20) DEFAULT 'STANDARD',
    address TEXT,
    phone VARCHAR(20),
    manager_id BIGINT REFERENCES users(id),
    opening_hours VARCHAR(100),
    business_area DECIMAL(8,2),
    status VARCHAR(20) DEFAULT 'OPERATING',
    pos_count INTEGER DEFAULT 1,
    warehouse_id BIGINT REFERENCES warehouses(id),
    region VARCHAR(50),
    city VARCHAR(50),
    province VARCHAR(50),
    postal_code VARCHAR(20),
    email VARCHAR(100),
    fax VARCHAR(20),
    rent_cost DECIMAL(10,2),
    decoration_cost DECIMAL(10,2),
    opening_date DATE,
    contract_start_date DATE,
    contract_end_date DATE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT,
    additional_info TEXT,
    remark VARCHAR(500),
    updater_id BIGINT,
    additional_info TEXT,
    remark VARCHAR(500)
);

-- 索引
CREATE INDEX idx_stores_code ON retail_stores(store_code);
CREATE INDEX idx_stores_manager ON retail_stores(manager_id);
CREATE INDEX idx_stores_warehouse ON retail_stores(warehouse_id);
CREATE INDEX idx_stores_status ON retail_stores(status);
CREATE INDEX idx_stores_region ON retail_stores(region);
CREATE INDEX idx_stores_city ON retail_stores(city);
```

### 9.2 财务管理表

#### 9.2.1 应收账款表
```sql
CREATE TABLE accounts_receivable (
    id BIGSERIAL PRIMARY KEY,
    ar_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id BIGINT NOT NULL REFERENCES customers(id),
    sales_order_id BIGINT REFERENCES sales_orders(id),
    invoice_number VARCHAR(50),
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    paid_amount DECIMAL(12,2) DEFAULT 0,
    outstanding_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    payment_terms INTEGER,
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT,
    updater_id BIGINT,
    additional_info TEXT,
    remark VARCHAR(500)
);

CREATE INDEX idx_ar_customer ON accounts_receivable(customer_id);
CREATE INDEX idx_ar_status ON accounts_receivable(status);
CREATE INDEX idx_ar_due_date ON accounts_receivable(due_date);
```

#### 9.2.2 应收账款付款记录表
```sql
CREATE TABLE ar_payments (
    id BIGSERIAL PRIMARY KEY,
    payment_number VARCHAR(50) UNIQUE,
    accounts_receivable_id BIGINT NOT NULL REFERENCES accounts_receivable(id),
    payment_date DATE NOT NULL,
    payment_amount DECIMAL(12,2) NOT NULL,
    payment_method VARCHAR(20) DEFAULT 'BANK_TRANSFER',
    reference_number VARCHAR(100),
    remarks TEXT,
    received_by BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_ar_payments_ar ON ar_payments(accounts_receivable_id);
CREATE INDEX idx_ar_payments_date ON ar_payments(payment_date);
```

#### 9.1.3 应付账款表
```sql
CREATE TABLE accounts_payable (
    id BIGSERIAL PRIMARY KEY,
    ap_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id BIGINT NOT NULL REFERENCES suppliers(id),
    purchase_order_id BIGINT REFERENCES purchase_orders(id),
    invoice_number VARCHAR(50),
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    paid_amount DECIMAL(12,2) DEFAULT 0,
    outstanding_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    payment_terms INTEGER,
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT,
    additional_info TEXT,
    remark VARCHAR(500),
    updater_id BIGINT
);

CREATE INDEX idx_ap_supplier ON accounts_payable(supplier_id);
CREATE INDEX idx_ap_status ON accounts_payable(status);
CREATE INDEX idx_ap_due_date ON accounts_payable(due_date);
```

### 9.2 库存管理扩展表

#### 9.2.1 库存预警表
```sql
CREATE TABLE inventory_alerts (
    id BIGSERIAL PRIMARY KEY,
    product_id BIGINT NOT NULL REFERENCES products(id),
    warehouse_id BIGINT NOT NULL REFERENCES warehouses(id),
    alert_type VARCHAR(20) NOT NULL,
    current_quantity DECIMAL(10,3),
    threshold_quantity DECIMAL(10,3),
    alert_message TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    resolved_at TIMESTAMP,
    resolved_by BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_inventory_alerts_product ON inventory_alerts(product_id);
CREATE INDEX idx_inventory_alerts_warehouse ON inventory_alerts(warehouse_id);
CREATE INDEX idx_inventory_alerts_status ON inventory_alerts(status);
```

#### 9.2.2 库存调拨表
```sql
CREATE TABLE inventory_transfers (
    id BIGSERIAL PRIMARY KEY,
    transfer_number VARCHAR(50) UNIQUE NOT NULL,
    from_warehouse_id BIGINT NOT NULL REFERENCES warehouses(id),
    to_warehouse_id BIGINT NOT NULL REFERENCES warehouses(id),
    transfer_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'DRAFT',
    total_items INTEGER DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    remarks TEXT,
    requested_by BIGINT,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    shipped_by BIGINT,
    shipped_at TIMESTAMP,
    received_by BIGINT,
    received_at TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_transfers_from_warehouse ON inventory_transfers(from_warehouse_id);
CREATE INDEX idx_transfers_to_warehouse ON inventory_transfers(to_warehouse_id);
CREATE INDEX idx_transfers_status ON inventory_transfers(status);
```

#### 9.2.3 库存调拨项表
```sql
CREATE TABLE inventory_transfer_items (
    id BIGSERIAL PRIMARY KEY,
    inventory_transfer_id BIGINT NOT NULL REFERENCES inventory_transfers(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    transfer_quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2),
    total_amount DECIMAL(12,2),
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_transfer_items_transfer ON inventory_transfer_items(inventory_transfer_id);
CREATE INDEX idx_transfer_items_product ON inventory_transfer_items(product_id);
```

### 9.3 库存盘点表

#### 9.3.1 库存盘点主表
```sql
CREATE TABLE inventory_checks (
    id BIGSERIAL PRIMARY KEY,
    check_number VARCHAR(50) UNIQUE NOT NULL,
    warehouse_id BIGINT NOT NULL REFERENCES warehouses(id),
    check_type VARCHAR(20) DEFAULT 'FULL',
    check_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'DRAFT',
    total_items INTEGER DEFAULT 0,
    checked_items INTEGER DEFAULT 0,
    variance_items INTEGER DEFAULT 0,
    remarks TEXT,
    checked_by BIGINT,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_inventory_checks_warehouse ON inventory_checks(warehouse_id);
CREATE INDEX idx_inventory_checks_status ON inventory_checks(status);
CREATE INDEX idx_inventory_checks_date ON inventory_checks(check_date);
```

#### 9.3.2 库存盘点项表
```sql
CREATE TABLE inventory_check_items (
    id BIGSERIAL PRIMARY KEY,
    inventory_check_id BIGINT NOT NULL REFERENCES inventory_checks(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    book_quantity DECIMAL(10,3) NOT NULL,
    actual_quantity DECIMAL(10,3) DEFAULT 0,
    variance_quantity DECIMAL(10,3) DEFAULT 0,
    unit_cost DECIMAL(10,2),
    variance_amount DECIMAL(12,2) DEFAULT 0,
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_check_items_check ON inventory_check_items(inventory_check_id);
CREATE INDEX idx_check_items_product ON inventory_check_items(product_id);
```

### 9.4 前置仓管理表

#### 9.4.1 前置仓基础信息表
```sql
CREATE TABLE front_warehouses (
    id BIGSERIAL PRIMARY KEY,
    warehouse_code VARCHAR(50) UNIQUE NOT NULL,
    warehouse_name VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    coverage_radius DECIMAL(8,2) DEFAULT 5.0,
    capacity INTEGER DEFAULT 1000,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    manager_id BIGINT REFERENCES users(id),
    phone VARCHAR(20),
    operating_hours JSON,
    parent_warehouse_id BIGINT REFERENCES warehouses(id),
    region_code VARCHAR(50),
    city VARCHAR(50),
    province VARCHAR(50),
    postal_code VARCHAR(20),
    rent_cost DECIMAL(10,2),
    setup_cost DECIMAL(10,2),
    opening_date DATE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT,
    updater_id BIGINT,
    remark VARCHAR(500)
);

-- 索引
CREATE INDEX idx_front_warehouses_code ON front_warehouses(warehouse_code);
CREATE INDEX idx_front_warehouses_manager ON front_warehouses(manager_id);
CREATE INDEX idx_front_warehouses_parent ON front_warehouses(parent_warehouse_id);
CREATE INDEX idx_front_warehouses_status ON front_warehouses(status);
CREATE INDEX idx_front_warehouses_region ON front_warehouses(region_code);
CREATE INDEX idx_front_warehouses_location ON front_warehouses(latitude, longitude);
```

#### 9.4.2 前置仓覆盖区域表
```sql
CREATE TABLE front_warehouse_coverage (
    id BIGSERIAL PRIMARY KEY,
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    region_code VARCHAR(50) NOT NULL,
    region_name VARCHAR(100) NOT NULL,
    region_type VARCHAR(20) DEFAULT 'DISTRICT',
    priority INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    delivery_time_min INTEGER DEFAULT 30,
    delivery_time_max INTEGER DEFAULT 120,
    delivery_fee DECIMAL(8,2) DEFAULT 0,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_coverage_warehouse ON front_warehouse_coverage(front_warehouse_id);
CREATE INDEX idx_coverage_region ON front_warehouse_coverage(region_code);
CREATE INDEX idx_coverage_active ON front_warehouse_coverage(is_active);
```

#### 9.4.3 前置仓库存表
```sql
CREATE TABLE front_warehouse_inventory (
    id BIGSERIAL PRIMARY KEY,
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    available_quantity DECIMAL(10,3) DEFAULT 0,
    reserved_quantity DECIMAL(10,3) DEFAULT 0,
    total_quantity DECIMAL(10,3) DEFAULT 0,
    min_stock DECIMAL(10,3) DEFAULT 0,
    max_stock DECIMAL(10,3) DEFAULT 0,
    reorder_point DECIMAL(10,3) DEFAULT 0,
    last_cost DECIMAL(10,2) DEFAULT 0,
    avg_cost DECIMAL(10,2) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1,
    UNIQUE(front_warehouse_id, product_id)
);

CREATE INDEX idx_front_inventory_warehouse ON front_warehouse_inventory(front_warehouse_id);
CREATE INDEX idx_front_inventory_product ON front_warehouse_inventory(product_id);
CREATE INDEX idx_front_inventory_low_stock ON front_warehouse_inventory(front_warehouse_id, available_quantity, min_stock);
CREATE INDEX idx_front_inventory_updated ON front_warehouse_inventory(last_updated);
```

#### 9.4.4 库存分配策略表
```sql
CREATE TABLE inventory_allocation_strategies (
    id BIGSERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    strategy_code VARCHAR(50) UNIQUE NOT NULL,
    strategy_type VARCHAR(20) NOT NULL,
    description TEXT,
    algorithm_config JSON,
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 1,
    effective_date DATE,
    expiry_date DATE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT
);

CREATE INDEX idx_allocation_strategies_code ON inventory_allocation_strategies(strategy_code);
CREATE INDEX idx_allocation_strategies_type ON inventory_allocation_strategies(strategy_type);
CREATE INDEX idx_allocation_strategies_active ON inventory_allocation_strategies(is_active);
```

#### 9.4.5 库存分配记录表
```sql
CREATE TABLE inventory_allocation_records (
    id BIGSERIAL PRIMARY KEY,
    allocation_number VARCHAR(50) UNIQUE NOT NULL,
    strategy_id BIGINT REFERENCES inventory_allocation_strategies(id),
    source_warehouse_id BIGINT REFERENCES warehouses(id),
    allocation_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    total_items INTEGER DEFAULT 0,
    total_quantity DECIMAL(12,3) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    remarks TEXT,
    created_by BIGINT,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    executed_by BIGINT,
    executed_at TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_allocation_records_number ON inventory_allocation_records(allocation_number);
CREATE INDEX idx_allocation_records_strategy ON inventory_allocation_records(strategy_id);
CREATE INDEX idx_allocation_records_source ON inventory_allocation_records(source_warehouse_id);
CREATE INDEX idx_allocation_records_status ON inventory_allocation_records(status);
CREATE INDEX idx_allocation_records_date ON inventory_allocation_records(allocation_date);
```

#### 9.4.6 库存分配明细表
```sql
CREATE TABLE inventory_allocation_items (
    id BIGSERIAL PRIMARY KEY,
    allocation_record_id BIGINT NOT NULL REFERENCES inventory_allocation_records(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    allocated_quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2),
    total_amount DECIMAL(12,2),
    demand_forecast DECIMAL(10,3),
    current_stock DECIMAL(10,3),
    allocation_reason VARCHAR(100),
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_allocation_items_record ON inventory_allocation_items(allocation_record_id);
CREATE INDEX idx_allocation_items_product ON inventory_allocation_items(product_id);
CREATE INDEX idx_allocation_items_warehouse ON inventory_allocation_items(front_warehouse_id);
```

### 9.5 智能补货管理表

#### 9.5.1 补货策略表
```sql
CREATE TABLE replenishment_strategies (
    id BIGSERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    strategy_code VARCHAR(50) UNIQUE NOT NULL,
    strategy_type VARCHAR(20) NOT NULL,
    description TEXT,
    trigger_conditions JSON,
    calculation_rules JSON,
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 1,
    effective_date DATE,
    expiry_date DATE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT
);

CREATE INDEX idx_replenishment_strategies_code ON replenishment_strategies(strategy_code);
CREATE INDEX idx_replenishment_strategies_type ON replenishment_strategies(strategy_type);
CREATE INDEX idx_replenishment_strategies_active ON replenishment_strategies(is_active);
```

#### 9.5.2 补货订单表
```sql
CREATE TABLE replenishment_orders (
    id BIGSERIAL PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    source_warehouse_id BIGINT REFERENCES warehouses(id),
    strategy_id BIGINT REFERENCES replenishment_strategies(id),
    order_type VARCHAR(20) DEFAULT 'AUTO',
    order_date DATE NOT NULL,
    required_date DATE,
    status VARCHAR(20) DEFAULT 'PENDING',
    total_items INTEGER DEFAULT 0,
    total_quantity DECIMAL(12,3) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    urgency_level VARCHAR(20) DEFAULT 'NORMAL',
    remarks TEXT,
    created_by BIGINT,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    shipped_by BIGINT,
    shipped_at TIMESTAMP,
    received_by BIGINT,
    received_at TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_replenishment_orders_number ON replenishment_orders(order_number);
CREATE INDEX idx_replenishment_orders_front_warehouse ON replenishment_orders(front_warehouse_id);
CREATE INDEX idx_replenishment_orders_source ON replenishment_orders(source_warehouse_id);
CREATE INDEX idx_replenishment_orders_status ON replenishment_orders(status);
CREATE INDEX idx_replenishment_orders_date ON replenishment_orders(order_date);
CREATE INDEX idx_replenishment_orders_urgency ON replenishment_orders(urgency_level);
```

#### 9.5.3 补货订单明细表
```sql
CREATE TABLE replenishment_order_items (
    id BIGSERIAL PRIMARY KEY,
    replenishment_order_id BIGINT NOT NULL REFERENCES replenishment_orders(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    requested_quantity DECIMAL(10,3) NOT NULL,
    approved_quantity DECIMAL(10,3) DEFAULT 0,
    shipped_quantity DECIMAL(10,3) DEFAULT 0,
    received_quantity DECIMAL(10,3) DEFAULT 0,
    unit_cost DECIMAL(10,2),
    total_amount DECIMAL(12,2),
    current_stock DECIMAL(10,3),
    min_stock DECIMAL(10,3),
    forecast_demand DECIMAL(10,3),
    replenishment_reason VARCHAR(100),
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_replenishment_items_order ON replenishment_order_items(replenishment_order_id);
CREATE INDEX idx_replenishment_items_product ON replenishment_order_items(product_id);
```

### 9.6 前置仓订单履约表

#### 9.6.1 订单分配表
```sql
CREATE TABLE front_warehouse_order_assignments (
    id BIGSERIAL PRIMARY KEY,
    assignment_number VARCHAR(50) UNIQUE NOT NULL,
    order_id BIGINT NOT NULL,
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    assignment_type VARCHAR(20) DEFAULT 'AUTO',
    assignment_strategy VARCHAR(50),
    priority INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'ASSIGNED',
    estimated_delivery_time TIMESTAMP,
    assignment_reason TEXT,
    distance_km DECIMAL(8,2),
    estimated_cost DECIMAL(10,2),
    assigned_by BIGINT,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confirmed_by BIGINT,
    confirmed_at TIMESTAMP,
    cancelled_by BIGINT,
    cancelled_at TIMESTAMP,
    cancellation_reason TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_order_assignments_order ON front_warehouse_order_assignments(order_id);
CREATE INDEX idx_order_assignments_warehouse ON front_warehouse_order_assignments(front_warehouse_id);
CREATE INDEX idx_order_assignments_status ON front_warehouse_order_assignments(status);
CREATE INDEX idx_order_assignments_assigned_at ON front_warehouse_order_assignments(assigned_at);
```

#### 9.6.2 拣选任务表
```sql
CREATE TABLE picking_tasks (
    id BIGSERIAL PRIMARY KEY,
    task_number VARCHAR(50) UNIQUE NOT NULL,
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    assignment_id BIGINT REFERENCES front_warehouse_order_assignments(id),
    task_type VARCHAR(20) DEFAULT 'SINGLE',
    picking_strategy VARCHAR(50),
    priority INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'PENDING',
    total_items INTEGER DEFAULT 0,
    picked_items INTEGER DEFAULT 0,
    estimated_duration INTEGER,
    actual_duration INTEGER,
    picking_path JSON,
    assigned_picker BIGINT,
    assigned_at TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_picking_tasks_warehouse ON picking_tasks(front_warehouse_id);
CREATE INDEX idx_picking_tasks_assignment ON picking_tasks(assignment_id);
CREATE INDEX idx_picking_tasks_picker ON picking_tasks(assigned_picker);
CREATE INDEX idx_picking_tasks_status ON picking_tasks(status);
CREATE INDEX idx_picking_tasks_assigned_at ON picking_tasks(assigned_at);
```

#### 9.6.3 拣选任务明细表
```sql
CREATE TABLE picking_task_items (
    id BIGSERIAL PRIMARY KEY,
    picking_task_id BIGINT NOT NULL REFERENCES picking_tasks(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    required_quantity DECIMAL(10,3) NOT NULL,
    picked_quantity DECIMAL(10,3) DEFAULT 0,
    location_code VARCHAR(50),
    picking_sequence INTEGER,
    status VARCHAR(20) DEFAULT 'PENDING',
    picked_at TIMESTAMP,
    picker_id BIGINT,
    exception_type VARCHAR(50),
    exception_reason TEXT,
    replacement_product_id BIGINT REFERENCES products(id),
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_picking_items_task ON picking_task_items(picking_task_id);
CREATE INDEX idx_picking_items_product ON picking_task_items(product_id);
CREATE INDEX idx_picking_items_status ON picking_task_items(status);
CREATE INDEX idx_picking_items_sequence ON picking_task_items(picking_sequence);
```

#### 9.6.4 打包任务表
```sql
CREATE TABLE packing_tasks (
    id BIGSERIAL PRIMARY KEY,
    task_number VARCHAR(50) UNIQUE NOT NULL,
    picking_task_id BIGINT NOT NULL REFERENCES picking_tasks(id),
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    packing_strategy VARCHAR(50),
    package_type VARCHAR(50),
    estimated_volume DECIMAL(10,3),
    actual_volume DECIMAL(10,3),
    estimated_weight DECIMAL(8,3),
    actual_weight DECIMAL(8,3),
    packaging_materials JSON,
    packaging_cost DECIMAL(8,2),
    status VARCHAR(20) DEFAULT 'PENDING',
    assigned_packer BIGINT,
    assigned_at TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    quality_check_status VARCHAR(20),
    quality_checker BIGINT,
    quality_checked_at TIMESTAMP,
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_packing_tasks_picking ON packing_tasks(picking_task_id);
CREATE INDEX idx_packing_tasks_warehouse ON packing_tasks(front_warehouse_id);
CREATE INDEX idx_packing_tasks_packer ON packing_tasks(assigned_packer);
CREATE INDEX idx_packing_tasks_status ON packing_tasks(status);
```

#### 9.6.5 包裹信息表
```sql
CREATE TABLE packages (
    id BIGSERIAL PRIMARY KEY,
    package_number VARCHAR(50) UNIQUE NOT NULL,
    packing_task_id BIGINT NOT NULL REFERENCES packing_tasks(id),
    tracking_number VARCHAR(100),
    package_type VARCHAR(50),
    length_cm DECIMAL(6,2),
    width_cm DECIMAL(6,2),
    height_cm DECIMAL(6,2),
    volume_cm3 DECIMAL(10,2),
    weight_g DECIMAL(8,2),
    packaging_materials JSON,
    special_instructions TEXT,
    fragile BOOLEAN DEFAULT FALSE,
    temperature_controlled BOOLEAN DEFAULT FALSE,
    insurance_value DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'PACKED',
    packed_by BIGINT,
    packed_at TIMESTAMP,
    shipped_by BIGINT,
    shipped_at TIMESTAMP,
    delivered_at TIMESTAMP,
    recipient_signature VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_packages_packing_task ON packages(packing_task_id);
CREATE INDEX idx_packages_tracking ON packages(tracking_number);
CREATE INDEX idx_packages_status ON packages(status);
CREATE INDEX idx_packages_shipped_at ON packages(shipped_at);
```

#### 9.6.6 配送任务表
```sql
CREATE TABLE delivery_tasks (
    id BIGSERIAL PRIMARY KEY,
    task_number VARCHAR(50) UNIQUE NOT NULL,
    front_warehouse_id BIGINT NOT NULL REFERENCES front_warehouses(id),
    delivery_type VARCHAR(20) DEFAULT 'STANDARD',
    delivery_method VARCHAR(50),
    total_packages INTEGER DEFAULT 0,
    total_weight DECIMAL(10,3) DEFAULT 0,
    total_volume DECIMAL(10,3) DEFAULT 0,
    estimated_distance DECIMAL(8,2),
    estimated_duration INTEGER,
    actual_distance DECIMAL(8,2),
    actual_duration INTEGER,
    delivery_route JSON,
    status VARCHAR(20) DEFAULT 'PENDING',
    assigned_driver BIGINT,
    vehicle_id BIGINT,
    assigned_at TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    delivery_cost DECIMAL(10,2),
    fuel_cost DECIMAL(8,2),
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_delivery_tasks_warehouse ON delivery_tasks(front_warehouse_id);
CREATE INDEX idx_delivery_tasks_driver ON delivery_tasks(assigned_driver);
CREATE INDEX idx_delivery_tasks_vehicle ON delivery_tasks(vehicle_id);
CREATE INDEX idx_delivery_tasks_status ON delivery_tasks(status);
CREATE INDEX idx_delivery_tasks_assigned_at ON delivery_tasks(assigned_at);
```

#### 9.6.7 配送任务包裹关联表
```sql
CREATE TABLE delivery_task_packages (
    id BIGSERIAL PRIMARY KEY,
    delivery_task_id BIGINT NOT NULL REFERENCES delivery_tasks(id),
    package_id BIGINT NOT NULL REFERENCES packages(id),
    delivery_sequence INTEGER,
    customer_name VARCHAR(100),
    customer_phone VARCHAR(20),
    delivery_address TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    delivery_time_window_start TIME,
    delivery_time_window_end TIME,
    estimated_arrival TIMESTAMP,
    actual_arrival TIMESTAMP,
    delivery_status VARCHAR(20) DEFAULT 'IN_TRANSIT',
    delivery_attempts INTEGER DEFAULT 0,
    delivery_notes TEXT,
    recipient_name VARCHAR(100),
    recipient_phone VARCHAR(20),
    signature_image_url VARCHAR(500),
    photo_proof_url VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(delivery_task_id, package_id)
);

CREATE INDEX idx_delivery_packages_task ON delivery_task_packages(delivery_task_id);
CREATE INDEX idx_delivery_packages_package ON delivery_task_packages(package_id);
CREATE INDEX idx_delivery_packages_sequence ON delivery_task_packages(delivery_sequence);
CREATE INDEX idx_delivery_packages_status ON delivery_task_packages(delivery_status);
```

### 9.7 销售出库表

#### 9.4.1 销售出库主表
```sql
CREATE TABLE sales_shipments (
    id BIGSERIAL PRIMARY KEY,
    shipment_number VARCHAR(50) UNIQUE NOT NULL,
    sales_order_id BIGINT NOT NULL REFERENCES sales_orders(id),
    customer_id BIGINT NOT NULL REFERENCES customers(id),
    warehouse_id BIGINT NOT NULL REFERENCES warehouses(id),
    shipment_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    total_amount DECIMAL(12,2) DEFAULT 0,
    delivery_address TEXT,
    tracking_number VARCHAR(100),
    remarks TEXT,
    shipped_by BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_shipments_sales_order ON sales_shipments(sales_order_id);
CREATE INDEX idx_shipments_customer ON sales_shipments(customer_id);
CREATE INDEX idx_shipments_warehouse ON sales_shipments(warehouse_id);
CREATE INDEX idx_shipments_status ON sales_shipments(status);
```

#### 9.4.2 销售出库项表
```sql
CREATE TABLE sales_shipment_items (
    id BIGSERIAL PRIMARY KEY,
    sales_shipment_id BIGINT NOT NULL REFERENCES sales_shipments(id),
    sales_order_item_id BIGINT NOT NULL REFERENCES sales_order_items(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    ordered_quantity DECIMAL(10,3) NOT NULL,
    shipped_quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_shipment_items_shipment ON sales_shipment_items(sales_shipment_id);
CREATE INDEX idx_shipment_items_order_item ON sales_shipment_items(sales_order_item_id);
CREATE INDEX idx_shipment_items_product ON sales_shipment_items(product_id);
```

### 9.3 零售管理表

#### 9.3.1 零售会员表
```sql
CREATE TABLE retail_members (
    id BIGSERIAL PRIMARY KEY,
    member_number VARCHAR(50) UNIQUE NOT NULL,
    member_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(100),
    gender VARCHAR(10),
    birthday DATE,
    member_level VARCHAR(20) DEFAULT 'BRONZE',
    total_points INTEGER DEFAULT 0,
    available_points INTEGER DEFAULT 0,
    total_consumption DECIMAL(12,2) DEFAULT 0,
    registration_date DATE DEFAULT CURRENT_DATE,
    last_visit_date DATE,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_members_number ON retail_members(member_number);
CREATE INDEX idx_members_phone ON retail_members(phone);
CREATE INDEX idx_members_level ON retail_members(member_level);
CREATE INDEX idx_members_status ON retail_members(status);
CREATE INDEX idx_members_registration ON retail_members(registration_date);
```

#### 9.3.2 零售促销活动表
```sql
CREATE TABLE retail_promotions (
    id BIGSERIAL PRIMARY KEY,
    promotion_name VARCHAR(100) NOT NULL,
    promotion_code VARCHAR(50) UNIQUE NOT NULL,
    promotion_type VARCHAR(30) NOT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    discount_rate DECIMAL(5,4),
    discount_amount DECIMAL(10,2),
    min_purchase_amount DECIMAL(10,2),
    max_discount_amount DECIMAL(10,2),
    applicable_products TEXT, -- JSON格式
    applicable_member_levels TEXT, -- JSON格式
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT,
    additional_info TEXT,
    remark VARCHAR(500),
    updater_id BIGINT
);

-- 索引
CREATE INDEX idx_promotions_code ON retail_promotions(promotion_code);
CREATE INDEX idx_promotions_type ON retail_promotions(promotion_type);
CREATE INDEX idx_promotions_dates ON retail_promotions(start_date, end_date);
CREATE INDEX idx_promotions_status ON retail_promotions(status);
```

#### 9.3.3 POS销售主表
```sql
CREATE TABLE retail_pos_sales (
    id BIGSERIAL PRIMARY KEY,
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    store_id BIGINT NOT NULL REFERENCES retail_stores(id),
    cashier_id BIGINT NOT NULL REFERENCES users(id),
    member_id BIGINT REFERENCES retail_members(id),
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(12,2) NOT NULL,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    final_amount DECIMAL(12,2) NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'PENDING',
    points_earned INTEGER DEFAULT 0,
    points_used INTEGER DEFAULT 0,
    receipt_number VARCHAR(50),
    remarks TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_pos_sales_number ON retail_pos_sales(sale_number);
CREATE INDEX idx_pos_sales_store ON retail_pos_sales(store_id);
CREATE INDEX idx_pos_sales_cashier ON retail_pos_sales(cashier_id);
CREATE INDEX idx_pos_sales_member ON retail_pos_sales(member_id);
CREATE INDEX idx_pos_sales_date ON retail_pos_sales(sale_date);
CREATE INDEX idx_pos_sales_payment ON retail_pos_sales(payment_status);
```

#### 9.3.4 POS销售明细表
```sql
CREATE TABLE retail_pos_sale_items (
    id BIGSERIAL PRIMARY KEY,
    pos_sale_id BIGINT NOT NULL REFERENCES retail_pos_sales(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    product_name VARCHAR(200) NOT NULL,
    product_code VARCHAR(100) NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL,
    promotion_id BIGINT REFERENCES retail_promotions(id),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_pos_sale_items_sale ON retail_pos_sale_items(pos_sale_id);
CREATE INDEX idx_pos_sale_items_product ON retail_pos_sale_items(product_id);
CREATE INDEX idx_pos_sale_items_promotion ON retail_pos_sale_items(promotion_id);
```

#### 9.3.5 会员积分记录表
```sql
CREATE TABLE retail_member_points_log (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL REFERENCES retail_members(id),
    points_change INTEGER NOT NULL,
    points_type VARCHAR(20) NOT NULL, -- EARN, USE, EXPIRE, ADJUST
    related_order_id BIGINT,
    reason VARCHAR(200),
    operator_id BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_points_log_member ON retail_member_points_log(member_id);
CREATE INDEX idx_points_log_type ON retail_member_points_log(points_type);
CREATE INDEX idx_points_log_date ON retail_member_points_log(create_time);
```

#### 9.3.6 优惠券表
```sql
CREATE TABLE retail_coupons (
    id BIGSERIAL PRIMARY KEY,
    coupon_code VARCHAR(50) UNIQUE NOT NULL,
    coupon_name VARCHAR(100) NOT NULL,
    coupon_type VARCHAR(20) NOT NULL, -- DISCOUNT, CASH, GIFT
    discount_rate DECIMAL(5,4),
    discount_amount DECIMAL(10,2),
    min_purchase_amount DECIMAL(10,2),
    max_discount_amount DECIMAL(10,2),
    valid_from TIMESTAMP NOT NULL,
    valid_to TIMESTAMP NOT NULL,
    usage_limit INTEGER,
    used_count INTEGER DEFAULT 0,
    applicable_products TEXT, -- JSON格式
    status VARCHAR(20) DEFAULT 'ACTIVE',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator_id BIGINT
);

-- 索引
CREATE INDEX idx_coupons_code ON retail_coupons(coupon_code);
CREATE INDEX idx_coupons_type ON retail_coupons(coupon_type);
CREATE INDEX idx_coupons_validity ON retail_coupons(valid_from, valid_to);
CREATE INDEX idx_coupons_status ON retail_coupons(status);
```

#### 9.3.7 会员优惠券关联表
```sql
CREATE TABLE retail_member_coupons (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL REFERENCES retail_members(id),
    coupon_id BIGINT NOT NULL REFERENCES retail_coupons(id),
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP,
    used_order_id BIGINT REFERENCES retail_pos_sales(id),
    status VARCHAR(20) DEFAULT 'AVAILABLE', -- AVAILABLE, USED, EXPIRED
    UNIQUE(member_id, coupon_id)
);

-- 索引
CREATE INDEX idx_member_coupons_member ON retail_member_coupons(member_id);
CREATE INDEX idx_member_coupons_coupon ON retail_member_coupons(coupon_id);
CREATE INDEX idx_member_coupons_status ON retail_member_coupons(status);
```

## 9. Liquibase数据库迁移

### 9.1 迁移文件结构

```
src/main/resources/db/changelog/
├── db.changelog-master.xml                 # 主变更日志文件
├── changes/
│   ├── v1.0.0/
│   │   ├── 001-create-schemas.xml          # 创建Schema
│   │   ├── 002-create-base-tables.xml      # 创建基础表
│   │   ├── 003-create-user-tables.xml      # 创建用户管理表
│   │   ├── 004-create-product-tables.xml   # 创建商品管理表
│   │   ├── 005-create-purchase-tables.xml  # 创建采购管理表
│   │   ├── 006-create-sales-tables.xml     # 创建销售管理表
│   │   ├── 007-create-inventory-tables.xml # 创建库存管理表
│   │   ├── 008-create-finance-tables.xml   # 创建财务管理表
│   │   ├── 009-create-report-tables.xml    # 创建报表分析表
│   │   ├── 010-create-system-tables.xml    # 创建系统管理表
│   │   ├── 011-create-retail-tables.xml    # 创建零售管理表
│   │   ├── 012-create-indexes.xml          # 创建索引
│   │   └── 013-insert-initial-data.xml     # 插入初始数据
│   └── v1.1.0/
│       ├── 014-add-additional-info.xml     # 添加扩展字段
│       └── 015-update-base-entity.xml      # 更新基础实体字段
└── rollback/
    ├── v1.0.0-rollback.xml                # v1.0.0回滚脚本
    └── v1.1.0-rollback.xml                # v1.1.0回滚脚本
```

### 9.2 主变更日志文件

**db.changelog-master.xml：**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd">

    <!-- v1.0.0 初始版本 -->
    <include file="changes/v1.0.0/001-create-schemas.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/002-create-base-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/003-create-user-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/004-create-product-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/005-create-purchase-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/006-create-sales-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/007-create-inventory-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/008-create-finance-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/009-create-report-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/010-create-system-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/011-create-retail-tables.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/012-create-indexes.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.0.0/013-insert-initial-data.xml" relativeToChangelogFile="true"/>

    <!-- v1.1.0 字段更新版本 -->
    <include file="changes/v1.1.0/014-add-additional-info.xml" relativeToChangelogFile="true"/>
    <include file="changes/v1.1.0/015-update-base-entity.xml" relativeToChangelogFile="true"/>

</databaseChangeLog>
```

### 9.3 Schema创建示例

**001-create-schemas.xml：**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd">

    <changeSet id="001-create-schemas" author="database-architect" context="all">
        <comment>创建所有业务Schema</comment>

        <sql>CREATE SCHEMA IF NOT EXISTS pisp_user;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_base_data;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_purchase;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_sales;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_inventory;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_finance;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_report;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_system;</sql>
        <sql>CREATE SCHEMA IF NOT EXISTS pisp_retail;</sql>

        <rollback>
            <sql>DROP SCHEMA IF EXISTS pisp_retail CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_system CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_report CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_finance CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_inventory CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_sales CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_purchase CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_base_data CASCADE;</sql>
            <sql>DROP SCHEMA IF EXISTS pisp_user CASCADE;</sql>
        </rollback>
    </changeSet>

</databaseChangeLog>
```

### 9.4 表创建示例

**003-create-user-tables.xml：**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd">

    <changeSet id="003-create-user-tables" author="database-architect" context="all">
        <comment>创建用户管理相关表</comment>

        <!-- 用户表 -->
        <createTable tableName="sys_users" schemaName="pisp_user">
            <column name="id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="username" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="phone" type="VARCHAR(20)">
                <constraints unique="true"/>
            </column>
            <column name="password_hash" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="salt" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="real_name" type="VARCHAR(100)"/>
            <column name="avatar_url" type="VARCHAR(500)"/>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="last_login_at" type="TIMESTAMP"/>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT"/>
            <column name="updater_id" type="BIGINT"/>
            <column name="additional_info" type="TEXT"/>
            <column name="remark" type="VARCHAR(500)"/>
            <column name="version" type="INTEGER" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- 角色表 -->
        <createTable tableName="sys_roles" schemaName="pisp_user">
            <column name="id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="role_name" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="role_code" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="description" type="VARCHAR(200)"/>
            <column name="is_system" type="BOOLEAN" defaultValue="false">
                <constraints nullable="false"/>
            </column>
            <column name="sort_order" type="INTEGER" defaultValue="0"/>
            <column name="status" type="VARCHAR(20)" defaultValue="ACTIVE">
                <constraints nullable="false"/>
            </column>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT"/>
            <column name="updater_id" type="BIGINT"/>
            <column name="additional_info" type="TEXT"/>
            <column name="remark" type="VARCHAR(500)"/>
            <column name="version" type="INTEGER" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- 用户角色关联表 -->
        <createTable tableName="sys_user_roles" schemaName="pisp_user">
            <column name="id" type="BIGSERIAL">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="assigned_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="assigned_by" type="BIGINT"/>
            <column name="create_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="creator_id" type="BIGINT"/>
            <column name="updater_id" type="BIGINT"/>
            <column name="additional_info" type="TEXT"/>
            <column name="remark" type="VARCHAR(500)"/>
            <column name="version" type="INTEGER" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="INTEGER" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- 外键约束 -->
        <addForeignKeyConstraint
            baseTableSchemaName="pisp_user"
            baseTableName="sys_user_roles"
            baseColumnNames="user_id"
            referencedTableSchemaName="pisp_user"
            referencedTableName="sys_users"
            referencedColumnNames="id"
            constraintName="fk_user_roles_user_id"/>

        <addForeignKeyConstraint
            baseTableSchemaName="pisp_user"
            baseTableName="sys_user_roles"
            baseColumnNames="role_id"
            referencedTableSchemaName="pisp_user"
            referencedTableName="sys_roles"
            referencedColumnNames="id"
            constraintName="fk_user_roles_role_id"/>

        <rollback>
            <dropTable tableName="sys_user_roles" schemaName="pisp_user"/>
            <dropTable tableName="sys_roles" schemaName="pisp_user"/>
            <dropTable tableName="sys_users" schemaName="pisp_user"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
```

### 9.5 字段更新示例

**015-update-base-entity.xml：**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd">

    <changeSet id="015-update-base-entity-fields" author="database-architect" context="all">
        <comment>更新BaseEntity字段名称</comment>

        <!-- 重命名时间字段 -->
        <renameColumn tableName="sys_users" schemaName="pisp_user"
                     oldColumnName="created_at" newColumnName="create_time" columnDataType="TIMESTAMP"/>
        <renameColumn tableName="sys_users" schemaName="pisp_user"
                     oldColumnName="updated_at" newColumnName="update_time" columnDataType="TIMESTAMP"/>

        <!-- 重命名操作人字段 -->
        <renameColumn tableName="sys_users" schemaName="pisp_user"
                     oldColumnName="created_by" newColumnName="creator_id" columnDataType="BIGINT"/>
        <renameColumn tableName="sys_users" schemaName="pisp_user"
                     oldColumnName="updated_by" newColumnName="updater_id" columnDataType="BIGINT"/>

        <!-- 添加新字段 -->
        <addColumn tableName="sys_users" schemaName="pisp_user">
            <column name="additional_info" type="TEXT"/>
            <column name="remark" type="VARCHAR(500)"/>
        </addColumn>

        <rollback>
            <dropColumn tableName="sys_users" schemaName="pisp_user" columnName="additional_info"/>
            <dropColumn tableName="sys_users" schemaName="pisp_user" columnName="remark"/>

            <renameColumn tableName="sys_users" schemaName="pisp_user"
                         oldColumnName="create_time" newColumnName="created_at" columnDataType="TIMESTAMP"/>
            <renameColumn tableName="sys_users" schemaName="pisp_user"
                         oldColumnName="update_time" newColumnName="updated_at" columnDataType="TIMESTAMP"/>
            <renameColumn tableName="sys_users" schemaName="pisp_user"
                         oldColumnName="creator_id" newColumnName="created_by" columnDataType="BIGINT"/>
            <renameColumn tableName="sys_users" schemaName="pisp_user"
                         oldColumnName="updater_id" newColumnName="updated_by" columnDataType="BIGINT"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
```

### 9.6 Liquibase命令

**常用命令：**

```bash
# 更新数据库到最新版本
mvn liquibase:update

# 查看待执行的变更
mvn liquibase:status

# 生成SQL脚本（不执行）
mvn liquibase:updateSQL

# 回滚到指定标签
mvn liquibase:rollback -Dliquibase.rollbackTag=v1.0.0

# 回滚指定数量的变更集
mvn liquibase:rollback -Dliquibase.rollbackCount=3

# 验证变更日志
mvn liquibase:validate

# 清空数据库
mvn liquibase:dropAll

# 生成变更日志（从现有数据库）
mvn liquibase:generateChangeLog

# 标记当前状态
mvn liquibase:tag -Dliquibase.tag=v1.1.0
```

**环境配置：**

```properties
# liquibase.properties
driver=org.postgresql.Driver
url=****************************************
username=pisp_user
password=pisp_password
changeLogFile=src/main/resources/db/changelog/db.changelog-master.xml
contexts=dev,test,prod
defaultSchemaName=public
liquibaseSchemaName=liquibase
```

## 10. 数据库设计总结

### 10.1 v2.0 升级要点

**技术栈升级 (v4.2)：**
- ✅ **PostgreSQL 17**：最新稳定版本，性能和安全性提升
- ✅ **MyBatis-Plus 3.5.12**：最新版本，增强功能和性能优化
- ✅ **Spring Boot 3.4.7**：完整的数据库集成支持
- ✅ **Redis 7.x**：高性能缓存和会话存储
- ✅ **RocketMQ 5.3.1**：可靠的消息队列支持

**架构设计优化：**
- ✅ **Schema隔离**：8个微服务独立Schema设计
- ✅ **数据分离**：按业务领域进行数据隔离
- ✅ **缓存策略**：Redis分区缓存设计
- ✅ **事件驱动**：RocketMQ事件流支持

### 10.2 9个核心模块数据库设计

| 模块 | Schema | 核心表 | 设计特点 |
|------|--------|--------|----------|
| **用户管理** | pisp_user | users, roles, permissions, departments | RBAC权限模型，JWT认证支持 |
| **基础数据** | pisp_base_data | products, customers, suppliers, warehouses, retail_stores | 主数据管理，数据一致性保证 |
| **采购管理** | pisp_purchase | purchase_orders, purchase_receipts, purchase_returns | 完整采购流程，工作流支持 |
| **销售管理** | pisp_sales | sales_orders, sales_shipments, sales_returns | 销售全流程，状态机管理 |
| **库存管理** | pisp_inventory | inventories, inventory_transactions, inventory_checks | 实时库存，事件驱动更新 |
| **财务管理** | pisp_finance | accounts_receivable, accounts_payable, cost_calculations | 复式记账，精确成本核算 |
| **报表分析** | pisp_report | report_configs, report_data, report_cache | 数据仓库模式，灵活报表 |
| **系统管理** | pisp_system | sys_configs, data_backups, operation_logs | 配置中心，系统监控 |
| **零售管理** | pisp_retail | retail_pos_sales, retail_members, retail_promotions | POS销售，会员体系，促销引擎 |

### 10.3 数据库性能优化

**索引策略：**
- ✅ **主键索引**：UUID主键，分布式友好
- ✅ **唯一索引**：业务唯一性约束
- ✅ **复合索引**：多字段查询优化
- ✅ **部分索引**：条件索引，节省空间

**分区策略：**
- ✅ **时间分区**：大表按时间分区
- ✅ **范围分区**：数据量大的表分区存储
- ✅ **自动维护**：分区自动创建和清理

**缓存策略：**
- ✅ **热点数据**：Redis缓存热点查询
- ✅ **会话存储**：用户会话Redis存储
- ✅ **查询缓存**：MyBatis二级缓存
- ✅ **分布式锁**：Redis分布式锁支持

### 9.4 数据安全保障

**数据完整性：**
- ✅ **外键约束**：关联数据一致性
- ✅ **检查约束**：业务规则约束
- ✅ **触发器**：数据变更审计
- ✅ **事务支持**：ACID特性保证

**数据安全：**
- ✅ **敏感数据加密**：密码、身份证等加密存储
- ✅ **访问控制**：Schema级别权限控制
- ✅ **审计日志**：完整的操作审计
- ✅ **备份恢复**：定期备份和恢复测试

### 9.5 运维监控

**性能监控：**
- ✅ **慢查询监控**：pg_stat_statements扩展
- ✅ **连接池监控**：HikariCP指标监控
- ✅ **缓存监控**：Redis性能指标
- ✅ **磁盘空间**：表空间使用监控

**告警机制：**
- ✅ **连接数告警**：连接池使用率告警
- ✅ **慢查询告警**：查询性能告警
- ✅ **磁盘空间告警**：存储空间告警
- ✅ **主从延迟告警**：复制延迟监控

---

**总结：** 本数据库设计完全支持PISP进销存管理系统的9个核心模块（包含新增的零售管理模块），采用最新的技术栈和最佳实践，为系统提供高性能、高可用、可扩展的数据存储基础，全面支持B2B和B2C业务场景。

# Apache ShenYu 2.7.0.1 网关配置指南

## 1. ShenYu网关概述

Apache ShenYu是一个异步的、高性能的、跨语言的、响应式的API网关。

### 1.1 技术栈版本

- **ShenYu版本**: 2.7.0.1 (最新稳定版)
- **Spring Boot版本**: 3.4.7
- **Spring Cloud版本**: 2024.0.1 (Moorgate)
- **注册中心**: Nacos 3.0.2
- **数据库**: PostgreSQL 17
- **消息队列**: Apache Kafka 3.9.1
- **Java版本**: 21 (LTS)

### 1.2 核心特性

- **高性能：** 基于WebFlux响应式编程，支持万级并发
- **插件化：** 丰富的插件生态，支持自定义插件
- **多协议：** 支持HTTP、gRPC、WebSocket、Dubbo等协议
- **动态配置：** 支持热更新，无需重启
- **可视化：** 提供友好的管理界面
- **云原生：** 支持Kubernetes、Docker容器化部署

### 1.3 ERP系统中的ShenYu架构

```mermaid
graph TB
    subgraph "客户端"
        WEB[Web前端]
        MOBILE[移动端]
        API[第三方API]
    end

    subgraph "负载均衡"
        NGINX[Nginx集群]
    end

    subgraph "ShenYu网关集群"
        GW1[ShenYu Gateway-1<br/>:9195]
        GW2[ShenYu Gateway-2<br/>:9196]
        ADMIN[ShenYu Admin<br/>:9095]
    end

    subgraph "注册中心"
        NACOS[Nacos 3.0.2<br/>:8848]
    end

    subgraph "ERP微服务集群"
        USER[用户管理服务<br/>:8001]
        BASE[基础数据服务<br/>:8002]
        PURCHASE[采购管理服务<br/>:8003]
        SALES[销售管理服务<br/>:8004]
        INVENTORY[库存管理服务<br/>:8005]
        FINANCE[财务管理服务<br/>:8006]
        REPORT[报表分析服务<br/>:8007]
        SYSTEM[系统管理服务<br/>:8008]
        RETAIL[零售管理服务<br/>:8009]
    end

    subgraph "数据存储"
        PG[PostgreSQL 17]
        REDIS[Redis 7.x]
        KAFKA[Kafka 3.9.1]
    end

    WEB --> NGINX
    MOBILE --> NGINX
    API --> NGINX

    NGINX --> GW1
    NGINX --> GW2

    GW1 --> USER
    GW1 --> BASE
    GW1 --> PURCHASE
    GW1 --> SALES
    GW2 --> INVENTORY
    GW2 --> FINANCE
    GW2 --> REPORT
    GW2 --> SYSTEM
    GW2 --> RETAIL

    GW1 --> NACOS
    GW2 --> NACOS
    ADMIN --> NACOS

    USER --> NACOS
    BASE --> NACOS
    PURCHASE --> NACOS
    SALES --> NACOS
    INVENTORY --> NACOS
    FINANCE --> NACOS
    REPORT --> NACOS
    SYSTEM --> NACOS
    RETAIL --> NACOS

    USER --> PG
    BASE --> PG
    PURCHASE --> PG
    SALES --> PG

    USER --> REDIS
    BASE --> REDIS
    PURCHASE --> REDIS
    SALES --> REDIS

    USER --> KAFKA
    BASE --> KAFKA
    PURCHASE --> KAFKA
    SALES --> KAFKA
    A --> K
    A --> L
```

## 2. 环境搭建

### 2.1 依赖要求

- JDK 17+
- Maven 3.6+
- PostgreSQL 17 (推荐)
- Redis 7.x

### 2.2 Maven依赖

```xml
<properties>
    <shenyu.version>2.7.0.1</shenyu.version>
</properties>

<dependencies>
    <!-- ShenYu Gateway Bootstrap -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-gateway</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <!-- 数据同步 -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-sync-data-websocket</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <!-- 核心插件 -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-plugin-divide</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-plugin-httpclient</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <!-- 认证插件 -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-plugin-jwt</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <!-- 限流插件 -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-plugin-ratelimiter</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <!-- 熔断插件 -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-plugin-resilience4j</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <!-- 缓存插件 -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-plugin-cache</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
    
    <!-- 监控插件 -->
    <dependency>
        <groupId>org.apache.shenyu</groupId>
        <artifactId>shenyu-spring-boot-starter-plugin-monitor</artifactId>
        <version>${shenyu.version}</version>
    </dependency>
</dependencies>
```

## 3. 配置文件

### 3.1 application.yml

```yaml
server:
  port: 9195
  address: 0.0.0.0

spring:
  application:
    name: shenyu-gateway
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}
  main:
    allow-bean-definition-overriding: true

shenyu:
  # 数据同步配置
  sync:
    websocket:
      urls: ws://shenyu-admin:9095/websocket
      allowOrigin: ws://shenyu-admin:9095
  
  # 跨域配置
  cross:
    enabled: true
    allowedHeaders: "*"
    allowedMethods: "*"
    allowedOrigin: "*"
    allowCredentials: true
    maxAge: 18000
  
  # 开关配置
  switchConfig:
    local: true
  
  # 文件配置
  file:
    enabled: true
    maxSize: 10
  
  # 排除路径
  exclude:
    enabled: false
    paths:
      - /favicon.ico
      - /actuator/**
      - /health
  
  # 扩展插件
  extPlugin:
    path: ""
    enabled: true
    threads: 1
    scheduleTime: 300
    scheduleDelay: 30
  
  # 调度器
  scheduler:
    enabled: false
    type: fixed
    threads: 16
  
  # 上游检查
  upstreamCheck:
    enabled: true
    timeout: 3000
    healthyThreshold: 1
    unhealthyThreshold: 1
    interval: 5000
    printEnabled: true
    printInterval: 60000
  
  # Netty配置
  netty:
    http:
      webServerFactoryEnabled: true
      selectCount: 1
      workerCount: 8
      accessLog: false
      serverSocketChannel:
        soRcvBuf: 87380
        soBackLog: 128
        soReuseAddr: false
        connectTimeoutMillis: 10000
        writeBufferHighWaterMark: 65536
        writeBufferLowWaterMark: 32768
        writeSpinCount: 16
        autoRead: false
        allocType: "pooled"
      socketChannel:
        soKeepAlive: false
        soReuseAddr: false
        soLinger: -1
        tcpNoDelay: true
        soRcvBuf: 87380
        soSndBuf: 16384
        ipTos: 0
        allowHalfClosure: false
        connectTimeoutMillis: 10000
        writeBufferHighWaterMark: 65536
        writeBufferLowWaterMark: 32768
        writeSpinCount: 16
        autoRead: false
        allocType: "pooled"

# 日志配置
logging:
  level:
    org.apache.shenyu: INFO
    org.springframework.cloud.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

## 4. 插件配置

### 4.1 JWT认证插件

```yaml
shenyu:
  plugin:
    jwt:
      enabled: true
      secretKey: "myJwtSecretKey"
      expiredTime: 86400000
```

### 4.2 限流插件

```yaml
shenyu:
  plugin:
    ratelimiter:
      enabled: true
      redis:
        host: redis-server
        port: 6379
        password: ""
        database: 0
```

### 4.3 缓存插件

```yaml
shenyu:
  plugin:
    cache:
      enabled: true
      type: redis
      redis:
        host: redis-server
        port: 6379
        password: ""
        database: 1
        timeout: 3000ms
```

### 4.4 零售业务路由配置

#### 4.4.1 POS销售服务路由

```yaml
shenyu:
  routes:
    - id: retail-pos-route
      uri: lb://pisp-retail-service
      predicates:
        - Path=/api/v1/retail/pos/**
      filters:
        - name: StripPrefix
          args:
            parts: 2
        - name: JWT
          args:
            enabled: true
        - name: RateLimiter
          args:
            redis-rate-limiter.replenishRate: 100
            redis-rate-limiter.burstCapacity: 200
```

#### 4.4.2 会员管理服务路由

```yaml
shenyu:
  routes:
    - id: retail-member-route
      uri: lb://pisp-retail-service
      predicates:
        - Path=/api/v1/retail/members/**
      filters:
        - name: StripPrefix
          args:
            parts: 2
        - name: JWT
          args:
            enabled: true
        - name: Cache
          args:
            ttl: 300
```

#### 4.4.3 促销管理服务路由

```yaml
shenyu:
  routes:
    - id: retail-promotion-route
      uri: lb://pisp-retail-service
      predicates:
        - Path=/api/v1/retail/promotions/**
      filters:
        - name: StripPrefix
          args:
            parts: 2
        - name: JWT
          args:
            enabled: true
```

#### 4.4.4 门店管理服务路由（基础数据服务）

```yaml
shenyu:
  routes:
    - id: retail-store-route
      uri: lb://pisp-base-data-service
      predicates:
        - Path=/api/v1/base-data/stores/**
      filters:
        - name: StripPrefix
          args:
            parts: 2
        - name: JWT
          args:
            enabled: true
        - name: RBAC
          args:
            roles: ADMIN,STORE_MANAGER
```

#### 4.4.5 零售报表服务路由（报表分析服务）

```yaml
shenyu:
  routes:
    - id: retail-report-route
      uri: lb://pisp-report-service
      predicates:
        - Path=/api/v1/reports/retail/**
      filters:
        - name: StripPrefix
          args:
            parts: 2
        - name: JWT
          args:
            enabled: true
        - name: Cache
          args:
            ttl: 600
        - name: RBAC
          args:
            roles: ADMIN,MANAGER,ANALYST
```

## 5. 启动类配置

```java
@SpringBootApplication
@EnableShenYuGateway
public class ShenYuGatewayApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(ShenYuGatewayApplication.class, args);
    }
    
    @Bean
    public ShenYuWebHandler shenYuWebHandler(ObjectProvider<List<ShenYuPlugin>> plugins) {
        List<ShenYuPlugin> pluginList = plugins.getIfAvailable(Collections::emptyList);
        List<ShenYuPlugin> shenYuPlugins = pluginList.stream()
                .sorted(Comparator.comparingInt(ShenYuPlugin::getOrder))
                .collect(Collectors.toList());
        return new ShenYuWebHandler(shenYuPlugins);
    }
}
```

## 6. Docker部署

### 6.1 Dockerfile

```dockerfile
FROM openjdk:21-jre-slim

WORKDIR /app

COPY target/shenyu-gateway-*.jar app.jar

EXPOSE 9195

ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 6.2 docker-compose.yml

```yaml
version: '3.8'
services:
  shenyu-admin:
    image: apache/shenyu-admin:2.7.0.1
    container_name: shenyu-admin
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: **************************************
      SPRING_DATASOURCE_USERNAME: shenyu
      SPRING_DATASOURCE_PASSWORD: shenyu123
    ports:
      - "9095:9095"
    depends_on:
      - postgres
    restart: unless-stopped

  shenyu-gateway:
    build: .
    container_name: shenyu-gateway
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SHENYU_SYNC_WEBSOCKET_URLS: ws://shenyu-admin:9095/websocket
    ports:
      - "9195:9195"
    depends_on:
      - shenyu-admin
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:17
    container_name: postgres
    environment:
      POSTGRES_DB: shenyu
      POSTGRES_USER: shenyu
      POSTGRES_PASSWORD: shenyu123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

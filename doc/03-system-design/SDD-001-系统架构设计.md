# SDD-001 进销存管理系统架构设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | SDD-001 |
| 文档名称 | 系统架构设计 |
| 版本号 | v2.1 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-25 | 初始版本创建 | 系统架构师 |
| v2.0 | 2025-06-25 | 技术栈升级，架构重构，8模块完整设计 | 系统架构师 |
| v2.1 | 2025-07-02 | 增加前置仓管理系统架构设计 | 系统架构师 |

## 1. 架构概述

### 1.1 架构原则

- **高可用性：** 系统7x24小时稳定运行
- **可扩展性：** 支持水平和垂直扩展
- **安全性：** 多层次安全防护
- **可维护性：** 模块化设计，便于维护
- **性能优化：** 高并发、低延迟

### 1.2 技术栈选择

**前端技术栈：**
- Vue 3 + TypeScript
- Element Plus UI组件库
- Pinia 状态管理
- Vue Router 路由管理
- Axios HTTP客户端
- Vite 构建工具

**后端技术栈：**
- Java 21 (LTS)
- Spring Boot 3.4.7
- Spring Cloud 2024.0.1 (Moorgate)
- Maven 3.9.x (多模块管理)
- Nacos 3.0.2 (注册中心和配置中心)
- Apache ShenYu ******* (API网关)
- MyBatis-Plus 3.5.12
- Spring Security 6.x

**数据库和缓存：**
- PostgreSQL 17 (主数据库)
- Redis 7.x (缓存和会话存储)

**消息中间件：**
- Apache RocketMQ 5.3.1 (消息队列)

**基础设施：**
- Docker容器化
- Nginx反向代理
- Prometheus + Grafana (监控)
- ELK Stack (日志)

## 2. 系统架构

### 2.1 Maven多模块架构

```mermaid
graph TB
    subgraph "父项目 pisp-system"
        A[pisp-parent]
    end

    subgraph "公共模块"
        B1[pisp-common-core]
        B2[pisp-common-security]
        B3[pisp-common-redis]
        B4[pisp-common-kafka]
        B5[pisp-common-web]
    end

    subgraph "API接口模块"
        C1[pisp-api-user]
        C2[pisp-api-base-data]
        C3[pisp-api-purchase]
        C4[pisp-api-sales]
        C5[pisp-api-inventory]
        C6[pisp-api-finance]
        C7[pisp-api-report]
        C8[pisp-api-system]
    end

    subgraph "业务服务模块"
        D1[pisp-user-service]
        D2[pisp-base-data-service]
        D3[pisp-purchase-service]
        D4[pisp-sales-service]
        D5[pisp-inventory-service]
        D6[pisp-finance-service]
        D7[pisp-report-service]
        D8[pisp-system-service]
    end

    subgraph "网关模块"
        E1[pisp-gateway]
    end

    A --> B1
    A --> B2
    A --> B3
    A --> B4
    A --> B5
    A --> C1
    A --> C2
    A --> C3
    A --> C4
    A --> C5
    A --> C6
    A --> C7
    A --> C8
    A --> D1
    A --> D2
    A --> D3
    A --> D4
    A --> D5
    A --> D6
    A --> D7
    A --> D8
    A --> E1

    D1 --> B1
    D1 --> B2
    D1 --> C1
    D2 --> B1
    D2 --> C2
    D3 --> B1
    D3 --> C3
    D4 --> B1
    D4 --> C4
    D5 --> B1
    D5 --> C5
    D6 --> B1
    D6 --> C6
    D7 --> B1
    D7 --> C7
    D8 --> B1
    D8 --> C8

    E1 --> B1
    E1 --> B5
```

### 2.2 C4架构图

#### 2.2.1 C1 - 系统上下文图 (System Context)

```mermaid
graph TB
    %% 定义样式
    classDef person fill:#08427b,stroke:#052e56,stroke-width:2px,color:#fff
    classDef system fill:#1168bd,stroke:#0b4884,stroke-width:2px,color:#fff
    classDef external fill:#999999,stroke:#666666,stroke-width:2px,color:#fff

    %% 用户角色
    Admin["👤 系统管理员<br/>管理系统配置和用户权限"]:::person
    User["👤 业务用户<br/>使用PISP系统进行日常业务操作"]:::person
    Manager["👤 管理层<br/>查看报表和业务分析"]:::person
    WarehouseStaff["👤 前置仓员工<br/>拣选、打包、配送作业"]:::person
    Customer["👤 终端客户<br/>下单和收货"]:::person

    %% 核心系统
    PISP["🏢 PISP进销存管理系统<br/>企业资源规划系统<br/>管理采购、销售、库存、财务、前置仓等业务"]:::system

    %% 外部系统
    Bank["🏦 银行系统<br/>处理财务支付和对账"]:::external
    Supplier["🏭 供应商系统<br/>供应商信息和采购对接"]:::external
    B2BCustomer["🛒 B2B客户系统<br/>企业客户订单和销售对接"]:::external
    Tax["📋 税务系统<br/>税务申报和发票管理"]:::external
    LogisticsAPI["🚚 物流API<br/>第三方物流和地图服务"]:::external
    PaymentGateway["💳 支付网关<br/>在线支付处理"]:::external

    %% 用户与系统的关系
    Admin -->|"管理配置<br/>HTTPS"| PISP
    User -->|"业务操作<br/>HTTPS"| PISP
    Manager -->|"查看报表<br/>HTTPS"| PISP
    WarehouseStaff -->|"前置仓作业<br/>移动端/Web"| PISP
    Customer -->|"下单购买<br/>小程序/APP"| PISP

    %% 系统与外部系统的关系
    PISP -->|"财务对接<br/>API"| Bank
    PISP -->|"采购对接<br/>API"| Supplier
    PISP -->|"B2B销售对接<br/>API"| B2BCustomer
    PISP -->|"税务对接<br/>API"| Tax
    PISP -->|"物流配送<br/>API"| LogisticsAPI
    PISP -->|"在线支付<br/>API"| PaymentGateway
```

#### 2.2.2 C2 - 容器图 (Container Diagram)

```mermaid
graph TB
    %% 定义样式
    classDef person fill:#08427b,stroke:#052e56,stroke-width:2px,color:#fff
    classDef webapp fill:#1168bd,stroke:#0b4884,stroke-width:2px,color:#fff
    classDef service fill:#2e7d32,stroke:#1b5e20,stroke-width:2px,color:#fff
    classDef database fill:#ed6c02,stroke:#e65100,stroke-width:2px,color:#fff
    classDef infrastructure fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff

    %% 用户
    User["👤 业务用户<br/>使用PISP系统的业务人员"]:::person
    WarehouseStaff["👤 前置仓员工<br/>使用前置仓作业系统"]:::person
    Customer["👤 终端客户<br/>使用购物小程序"]:::person

    %% 前端应用层
    subgraph "前端应用层"
        Web["💻 Web应用<br/>Vue 3 + TypeScript<br/>提供PISP系统的用户界面"]:::webapp
        Mobile["📱 移动应用<br/>React Native<br/>移动端PISP应用"]:::webapp
        WarehouseApp["📱 前置仓作业APP<br/>React Native<br/>拣选、打包、配送作业"]:::webapp
        CustomerApp["📱 客户小程序<br/>微信小程序<br/>下单购买"]:::webapp
    end

    %% 网关层
    subgraph "网关层"
        Nginx["⚖️ 负载均衡<br/>Nginx<br/>负载均衡和反向代理"]:::infrastructure
        Gateway["🚪 API网关<br/>Apache ShenYu *******<br/>路由转发、认证鉴权、限流熔断"]:::infrastructure
    end

    %% 微服务层
    subgraph "微服务层"
        UserSvc["👥 用户管理服务<br/>Spring Boot 3.4.7<br/>用户、角色、权限管理"]:::service
        BaseSvc["📦 基础数据服务<br/>Spring Boot 3.4.7<br/>商品、客户、供应商管理"]:::service
        PurchaseSvc["🛒 采购管理服务<br/>Spring Boot 3.4.7<br/>采购订单、入库、退货"]:::service
        SalesSvc["💰 销售管理服务<br/>Spring Boot 3.4.7<br/>销售订单、出库、退货"]:::service
        InventorySvc["📊 库存管理服务<br/>Spring Boot 3.4.7<br/>库存查询、盘点、预警"]:::service
        FinanceSvc["💳 财务管理服务<br/>Spring Boot 3.4.7<br/>应收应付、成本核算"]:::service
        ReportSvc["📈 报表分析服务<br/>Spring Boot 3.4.7<br/>报表生成、数据分析"]:::service
        SystemSvc["⚙️ 系统管理服务<br/>Spring Boot 3.4.7<br/>系统配置、日志管理"]:::service
        RetailSvc["🛍️ 零售管理服务<br/>Spring Boot 3.4.7<br/>POS销售、会员管理、促销活动"]:::service
        FrontWarehouseSvc["🏪 前置仓管理服务<br/>Spring Boot 3.4.7<br/>前置仓、库存分配、智能补货"]:::service
        OrderFulfillmentSvc["📋 订单履约服务<br/>Spring Boot 3.4.7<br/>订单分配、拣选、打包、配送"]:::service
    end

    %% 数据层
    subgraph "数据层"
        Database["🗄️ 主数据库<br/>PostgreSQL 17<br/>存储业务数据"]:::database
        Cache["⚡ 缓存数据库<br/>Redis 7.x<br/>缓存热点数据和会话"]:::database
        Message["📨 消息队列<br/>Apache RocketMQ 5.3.1<br/>异步消息处理"]:::database
        Registry["🎯 注册中心<br/>Nacos 3.0.2<br/>服务注册发现和配置管理"]:::infrastructure
    end

    %% 连接关系
    User -->|"使用<br/>HTTPS"| Web
    User -->|"使用<br/>HTTPS"| Mobile
    WarehouseStaff -->|"使用<br/>HTTPS"| WarehouseApp
    Customer -->|"使用<br/>HTTPS"| CustomerApp

    Web -->|"请求<br/>HTTPS"| Nginx
    Mobile -->|"请求<br/>HTTPS"| Nginx
    WarehouseApp -->|"请求<br/>HTTPS"| Nginx
    CustomerApp -->|"请求<br/>HTTPS"| Nginx
    Nginx -->|"转发<br/>HTTP"| Gateway

    Gateway -->|"路由<br/>HTTP"| UserSvc
    Gateway -->|"路由<br/>HTTP"| BaseSvc
    Gateway -->|"路由<br/>HTTP"| PurchaseSvc
    Gateway -->|"路由<br/>HTTP"| SalesSvc
    Gateway -->|"路由<br/>HTTP"| InventorySvc
    Gateway -->|"路由<br/>HTTP"| FinanceSvc
    Gateway -->|"路由<br/>HTTP"| ReportSvc
    Gateway -->|"路由<br/>HTTP"| SystemSvc
    Gateway -->|"路由<br/>HTTP"| RetailSvc
    Gateway -->|"路由<br/>HTTP"| FrontWarehouseSvc
    Gateway -->|"路由<br/>HTTP"| OrderFulfillmentSvc

    UserSvc -->|"读写<br/>JDBC"| Database
    BaseSvc -->|"读写<br/>JDBC"| Database
    PurchaseSvc -->|"读写<br/>JDBC"| Database
    SalesSvc -->|"读写<br/>JDBC"| Database
    FrontWarehouseSvc -->|"读写<br/>JDBC"| Database
    OrderFulfillmentSvc -->|"读写<br/>JDBC"| Database

    UserSvc -->|"缓存<br/>Redis"| Cache
    BaseSvc -->|"缓存<br/>Redis"| Cache
    FrontWarehouseSvc -->|"缓存<br/>Redis"| Cache
    OrderFulfillmentSvc -->|"缓存<br/>Redis"| Cache

    PurchaseSvc -->|"发布事件<br/>Kafka"| Message
    SalesSvc -->|"发布事件<br/>Kafka"| Message
    FrontWarehouseSvc -->|"发布事件<br/>Kafka"| Message
    OrderFulfillmentSvc -->|"发布事件<br/>Kafka"| Message
    InventorySvc -->|"消费事件<br/>Kafka"| Message
    FinanceSvc -->|"消费事件<br/>Kafka"| Message

    UserSvc -->|"注册<br/>HTTP"| Registry
    BaseSvc -->|"注册<br/>HTTP"| Registry
    PurchaseSvc -->|"注册<br/>HTTP"| Registry
    SalesSvc -->|"注册<br/>HTTP"| Registry
    InventorySvc -->|"注册<br/>HTTP"| Registry
    FinanceSvc -->|"注册<br/>HTTP"| Registry
    ReportSvc -->|"注册<br/>HTTP"| Registry
    SystemSvc -->|"注册<br/>HTTP"| Registry
    RetailSvc -->|"注册<br/>HTTP"| Registry
    FrontWarehouseSvc -->|"注册<br/>HTTP"| Registry
    OrderFulfillmentSvc -->|"注册<br/>HTTP"| Registry
```

#### 2.2.3 C3 - 组件图 (Component Diagram)

##### 2.2.3.1 用户管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#1976d2,stroke:#0d47a1,stroke-width:2px,color:#fff
    classDef service fill:#1565c0,stroke:#0d47a1,stroke-width:2px,color:#fff
    classDef repository fill:#1e88e5,stroke:#1565c0,stroke-width:2px,color:#fff
    classDef external fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 用户服务内部组件
    subgraph "用户管理服务 (pisp-user-service)"
        UserController["👤 用户控制器<br/>UserController<br/>用户CRUD操作"]:::controller
        RoleController["🎭 角色控制器<br/>RoleController<br/>角色权限管理"]:::controller
        AuthController["🔐 认证控制器<br/>AuthController<br/>登录认证"]:::controller

        UserService["👥 用户业务服务<br/>UserService<br/>用户业务逻辑"]:::service
        RoleService["🎯 角色权限服务<br/>RoleService<br/>RBAC权限控制"]:::service
        AuthService["🔑 认证服务<br/>AuthService<br/>JWT认证"]:::service

        UserRepository["💾 用户数据层<br/>UserRepository<br/>用户数据持久化"]:::repository
        RoleRepository["💾 角色数据层<br/>RoleRepository<br/>角色数据持久化"]:::repository

        JwtTokenManager["🎫 JWT管理器<br/>JwtTokenManager<br/>Token生成验证"]:::service
        PasswordEncoder["🔒 密码编码器<br/>PasswordEncoder<br/>密码加密"]:::service
        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>用户会话缓存"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>用户数据库<br/>存储用户角色数据"]:::external
    Cache["⚡ Redis<br/>会话缓存<br/>JWT Token缓存"]:::external

    %% 控制器调用关系
    Gateway -->|"用户请求"| UserController
    Gateway -->|"角色请求"| RoleController
    Gateway -->|"认证请求"| AuthController

    %% 服务调用关系
    UserController -->|"用户业务"| UserService
    RoleController -->|"角色业务"| RoleService
    AuthController -->|"认证业务"| AuthService

    %% 业务服务内部调用
    UserService -->|"数据操作"| UserRepository
    RoleService -->|"数据操作"| RoleRepository
    AuthService -->|"用户验证"| UserService
    AuthService -->|"Token管理"| JwtTokenManager
    AuthService -->|"密码验证"| PasswordEncoder
    UserService -->|"缓存操作"| CacheManager

    %% 外部依赖关系
    UserRepository -->|"SQL查询"| Database
    RoleRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    JwtTokenManager -->|"Token缓存"| Cache
```

##### 2.2.3.2 基础数据服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#388e3c,stroke:#1b5e20,stroke-width:2px,color:#fff
    classDef service fill:#4caf50,stroke:#388e3c,stroke-width:2px,color:#fff
    classDef repository fill:#66bb6a,stroke:#4caf50,stroke-width:2px,color:#fff
    classDef external fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 基础数据服务内部组件
    subgraph "基础数据服务 (pisp-base-data-service)"
        ProductController["📦 商品控制器<br/>ProductController<br/>商品信息管理"]:::controller
        CustomerController["👥 客户控制器<br/>CustomerController<br/>客户信息管理"]:::controller
        SupplierController["🏭 供应商控制器<br/>SupplierController<br/>供应商管理"]:::controller
        WarehouseController["🏢 仓库控制器<br/>WarehouseController<br/>仓库信息管理"]:::controller

        ProductService["📋 商品业务服务<br/>ProductService<br/>商品业务逻辑"]:::service
        CustomerService["👤 客户业务服务<br/>CustomerService<br/>客户业务逻辑"]:::service
        SupplierService["🏭 供应商业务服务<br/>SupplierService<br/>供应商业务逻辑"]:::service
        WarehouseService["🏢 仓库业务服务<br/>WarehouseService<br/>仓库业务逻辑"]:::service

        ProductRepository["💾 商品数据层<br/>ProductRepository<br/>商品数据持久化"]:::repository
        CustomerRepository["💾 客户数据层<br/>CustomerRepository<br/>客户数据持久化"]:::repository
        SupplierRepository["💾 供应商数据层<br/>SupplierRepository<br/>供应商数据持久化"]:::repository
        WarehouseRepository["💾 仓库数据层<br/>WarehouseRepository<br/>仓库数据持久化"]:::repository

        DataValidator["✅ 数据验证器<br/>DataValidator<br/>基础数据校验"]:::service
        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>热点数据缓存"]:::service
        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>数据变更事件"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>基础数据库<br/>存储基础数据"]:::external
    Cache["⚡ Redis<br/>数据缓存<br/>缓存热点数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>数据变更事件"]:::external

    %% 控制器调用关系
    Gateway -->|"商品请求"| ProductController
    Gateway -->|"客户请求"| CustomerController
    Gateway -->|"供应商请求"| SupplierController
    Gateway -->|"仓库请求"| WarehouseController

    %% 服务调用关系
    ProductController -->|"数据验证"| DataValidator
    ProductController -->|"商品业务"| ProductService
    CustomerController -->|"客户业务"| CustomerService
    SupplierController -->|"供应商业务"| SupplierService
    WarehouseController -->|"仓库业务"| WarehouseService

    %% 业务服务内部调用
    ProductService -->|"数据操作"| ProductRepository
    CustomerService -->|"数据操作"| CustomerRepository
    SupplierService -->|"数据操作"| SupplierRepository
    WarehouseService -->|"数据操作"| WarehouseRepository

    ProductService -->|"缓存操作"| CacheManager
    ProductService -->|"发布事件"| EventPublisher

    %% 外部依赖关系
    ProductRepository -->|"SQL查询"| Database
    CustomerRepository -->|"SQL查询"| Database
    SupplierRepository -->|"SQL查询"| Database
    WarehouseRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue
```

##### 2.2.3.3 采购管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#f57c00,stroke:#e65100,stroke-width:2px,color:#fff
    classDef service fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#fff
    classDef repository fill:#ffb74d,stroke:#ff9800,stroke-width:2px,color:#fff
    classDef external fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 采购服务内部组件
    subgraph "采购管理服务 (pisp-purchase-service)"
        PurchaseOrderController["📋 采购订单控制器<br/>PurchaseOrderController<br/>采购订单管理"]:::controller
        PurchaseReceiptController["📦 采购入库控制器<br/>PurchaseReceiptController<br/>采购入库管理"]:::controller
        PurchaseReturnController["↩️ 采购退货控制器<br/>PurchaseReturnController<br/>采购退货管理"]:::controller

        PurchaseOrderService["🛒 采购订单服务<br/>PurchaseOrderService<br/>订单业务逻辑"]:::service
        PurchaseReceiptService["📥 采购入库服务<br/>PurchaseReceiptService<br/>入库业务逻辑"]:::service
        PurchaseReturnService["🔄 采购退货服务<br/>PurchaseReturnService<br/>退货业务逻辑"]:::service
        WorkflowService["⚙️ 工作流服务<br/>WorkflowService<br/>采购流程控制"]:::service

        PurchaseOrderRepository["💾 订单数据层<br/>PurchaseOrderRepository<br/>订单数据持久化"]:::repository
        PurchaseReceiptRepository["💾 入库数据层<br/>PurchaseReceiptRepository<br/>入库数据持久化"]:::repository
        PurchaseReturnRepository["💾 退货数据层<br/>PurchaseReturnRepository<br/>退货数据持久化"]:::repository

        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>发布业务事件"]:::service
        Validator["✅ 数据验证器<br/>BeanValidator<br/>数据校验"]:::service
        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>缓存管理"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>采购数据库<br/>存储采购数据"]:::external
    Cache["⚡ Redis<br/>缓存<br/>缓存热点数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>异步事件处理"]:::external
    BaseDataService["📦 基础数据服务<br/>微服务<br/>商品供应商数据"]:::external
    InventoryService["📊 库存服务<br/>微服务<br/>库存管理"]:::external
    FinanceService["💳 财务服务<br/>微服务<br/>财务处理"]:::external

    %% 控制器调用关系
    Gateway -->|"订单请求"| PurchaseOrderController
    Gateway -->|"入库请求"| PurchaseReceiptController
    Gateway -->|"退货请求"| PurchaseReturnController

    %% 服务调用关系
    PurchaseOrderController -->|"数据验证"| Validator
    PurchaseOrderController -->|"订单业务"| PurchaseOrderService
    PurchaseReceiptController -->|"入库业务"| PurchaseReceiptService
    PurchaseReturnController -->|"退货业务"| PurchaseReturnService

    %% 业务服务内部调用
    PurchaseOrderService -->|"工作流"| WorkflowService
    PurchaseOrderService -->|"数据操作"| PurchaseOrderRepository
    PurchaseReceiptService -->|"数据操作"| PurchaseReceiptRepository
    PurchaseReturnService -->|"数据操作"| PurchaseReturnRepository

    PurchaseOrderService -->|"缓存操作"| CacheManager
    PurchaseOrderService -->|"发布事件"| EventPublisher
    PurchaseReceiptService -->|"发布事件"| EventPublisher

    %% 外部依赖关系
    PurchaseOrderRepository -->|"SQL查询"| Database
    PurchaseReceiptRepository -->|"SQL查询"| Database
    PurchaseReturnRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue

    %% 外部服务调用
    PurchaseOrderService -->|"获取基础数据"| BaseDataService
    EventPublisher -->|"库存事件"| InventoryService
    EventPublisher -->|"财务事件"| FinanceService
```

##### ******* 销售管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#d32f2f,stroke:#b71c1c,stroke-width:2px,color:#fff
    classDef service fill:#f44336,stroke:#d32f2f,stroke-width:2px,color:#fff
    classDef repository fill:#ef5350,stroke:#f44336,stroke-width:2px,color:#fff
    classDef external fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 销售服务内部组件
    subgraph "销售管理服务 (pisp-sales-service)"
        SalesOrderController["📋 销售订单控制器<br/>SalesOrderController<br/>销售订单管理"]:::controller
        SalesShipmentController["📦 销售出库控制器<br/>SalesShipmentController<br/>销售出库管理"]:::controller
        SalesReturnController["↩️ 销售退货控制器<br/>SalesReturnController<br/>销售退货管理"]:::controller

        SalesOrderService["💰 销售订单服务<br/>SalesOrderService<br/>订单业务逻辑"]:::service
        SalesShipmentService["📤 销售出库服务<br/>SalesShipmentService<br/>出库业务逻辑"]:::service
        SalesReturnService["🔄 销售退货服务<br/>SalesReturnService<br/>退货业务逻辑"]:::service
        OrderStateMachine["🎯 订单状态机<br/>OrderStateMachine<br/>订单状态流转"]:::service
        PricingService["💲 定价服务<br/>PricingService<br/>价格计算逻辑"]:::service

        SalesOrderRepository["💾 订单数据层<br/>SalesOrderRepository<br/>订单数据持久化"]:::repository
        SalesShipmentRepository["💾 出库数据层<br/>SalesShipmentRepository<br/>出库数据持久化"]:::repository
        SalesReturnRepository["💾 退货数据层<br/>SalesReturnRepository<br/>退货数据持久化"]:::repository

        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>发布业务事件"]:::service
        Validator["✅ 数据验证器<br/>BeanValidator<br/>数据校验"]:::service
        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>缓存管理"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>销售数据库<br/>存储销售数据"]:::external
    Cache["⚡ Redis<br/>缓存<br/>缓存热点数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>异步事件处理"]:::external
    BaseDataService["📦 基础数据服务<br/>微服务<br/>商品客户数据"]:::external
    InventoryService["📊 库存服务<br/>微服务<br/>库存管理"]:::external
    FinanceService["💳 财务服务<br/>微服务<br/>财务处理"]:::external

    %% 控制器调用关系
    Gateway -->|"订单请求"| SalesOrderController
    Gateway -->|"出库请求"| SalesShipmentController
    Gateway -->|"退货请求"| SalesReturnController

    %% 服务调用关系
    SalesOrderController -->|"数据验证"| Validator
    SalesOrderController -->|"订单业务"| SalesOrderService
    SalesShipmentController -->|"出库业务"| SalesShipmentService
    SalesReturnController -->|"退货业务"| SalesReturnService

    %% 业务服务内部调用
    SalesOrderService -->|"状态流转"| OrderStateMachine
    SalesOrderService -->|"价格计算"| PricingService
    SalesOrderService -->|"数据操作"| SalesOrderRepository
    SalesShipmentService -->|"数据操作"| SalesShipmentRepository
    SalesReturnService -->|"数据操作"| SalesReturnRepository

    SalesOrderService -->|"缓存操作"| CacheManager
    SalesOrderService -->|"发布事件"| EventPublisher
    SalesShipmentService -->|"发布事件"| EventPublisher

    %% 外部依赖关系
    SalesOrderRepository -->|"SQL查询"| Database
    SalesShipmentRepository -->|"SQL查询"| Database
    SalesReturnRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue

    %% 外部服务调用
    SalesOrderService -->|"获取基础数据"| BaseDataService
    EventPublisher -->|"库存事件"| InventoryService
    EventPublisher -->|"财务事件"| FinanceService
```

##### ******* 库存管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#7b1fa2,stroke:#4a148c,stroke-width:2px,color:#fff
    classDef service fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff
    classDef repository fill:#ba68c8,stroke:#9c27b0,stroke-width:2px,color:#fff
    classDef external fill:#ff5722,stroke:#d84315,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 库存服务内部组件
    subgraph "库存管理服务 (pisp-inventory-service)"
        InventoryController["📊 库存查询控制器<br/>InventoryController<br/>库存查询管理"]:::controller
        InventoryCheckController["🔍 库存盘点控制器<br/>InventoryCheckController<br/>库存盘点管理"]:::controller
        InventoryAlertController["⚠️ 库存预警控制器<br/>InventoryAlertController<br/>库存预警管理"]:::controller

        InventoryQueryService["📋 库存查询服务<br/>InventoryQueryService<br/>库存查询逻辑"]:::service
        InventoryCheckService["🔍 库存盘点服务<br/>InventoryCheckService<br/>盘点业务逻辑"]:::service
        InventoryAlertService["⚠️ 库存预警服务<br/>InventoryAlertService<br/>预警业务逻辑"]:::service
        InventoryEventHandler["📨 库存事件处理器<br/>InventoryEventHandler<br/>处理库存变动事件"]:::service
        InventoryCalculator["🧮 库存计算器<br/>InventoryCalculator<br/>库存数量计算"]:::service

        InventoryRepository["💾 库存数据层<br/>InventoryRepository<br/>库存数据持久化"]:::repository
        InventoryTransactionRepository["💾 库存流水数据层<br/>InventoryTransactionRepository<br/>库存流水持久化"]:::repository
        InventoryCheckRepository["💾 盘点数据层<br/>InventoryCheckRepository<br/>盘点数据持久化"]:::repository

        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>库存缓存管理"]:::service
        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>发布库存事件"]:::service
        AlertManager["🚨 预警管理器<br/>AlertManager<br/>预警通知管理"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>库存数据库<br/>存储库存数据"]:::external
    Cache["⚡ Redis<br/>缓存<br/>缓存库存数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>库存事件处理"]:::external

    %% 控制器调用关系
    Gateway -->|"查询请求"| InventoryController
    Gateway -->|"盘点请求"| InventoryCheckController
    Gateway -->|"预警请求"| InventoryAlertController

    %% 服务调用关系
    InventoryController -->|"查询业务"| InventoryQueryService
    InventoryCheckController -->|"盘点业务"| InventoryCheckService
    InventoryAlertController -->|"预警业务"| InventoryAlertService

    %% 业务服务内部调用
    InventoryQueryService -->|"库存计算"| InventoryCalculator
    InventoryQueryService -->|"数据操作"| InventoryRepository
    InventoryCheckService -->|"数据操作"| InventoryCheckRepository
    InventoryAlertService -->|"预警通知"| AlertManager

    InventoryEventHandler -->|"库存计算"| InventoryCalculator
    InventoryEventHandler -->|"数据操作"| InventoryRepository
    InventoryEventHandler -->|"流水记录"| InventoryTransactionRepository

    InventoryQueryService -->|"缓存操作"| CacheManager
    InventoryEventHandler -->|"发布事件"| EventPublisher

    %% 外部依赖关系
    InventoryRepository -->|"SQL查询"| Database
    InventoryTransactionRepository -->|"SQL查询"| Database
    InventoryCheckRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue

    %% 事件消费
    MessageQueue -->|"采购入库事件"| InventoryEventHandler
    MessageQueue -->|"销售出库事件"| InventoryEventHandler
```

##### 2.2.3.6 财务管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#795548,stroke:#5d4037,stroke-width:2px,color:#fff
    classDef service fill:#8d6e63,stroke:#795548,stroke-width:2px,color:#fff
    classDef repository fill:#a1887f,stroke:#8d6e63,stroke-width:2px,color:#fff
    classDef external fill:#ff5722,stroke:#d84315,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 财务服务内部组件
    subgraph "财务管理服务 (pisp-finance-service)"
        AccountsReceivableController["💰 应收账款控制器<br/>AccountsReceivableController<br/>应收账款管理"]:::controller
        AccountsPayableController["💳 应付账款控制器<br/>AccountsPayableController<br/>应付账款管理"]:::controller
        CostController["💲 成本核算控制器<br/>CostController<br/>成本核算管理"]:::controller
        ReconciliationController["🔄 对账控制器<br/>ReconciliationController<br/>财务对账管理"]:::controller

        AccountsReceivableService["💰 应收账款服务<br/>AccountsReceivableService<br/>应收业务逻辑"]:::service
        AccountsPayableService["💳 应付账款服务<br/>AccountsPayableService<br/>应付业务逻辑"]:::service
        CostCalculationService["🧮 成本核算服务<br/>CostCalculationService<br/>成本计算逻辑"]:::service
        ReconciliationService["🔄 对账服务<br/>ReconciliationService<br/>对账业务逻辑"]:::service
        FinanceEventHandler["📨 财务事件处理器<br/>FinanceEventHandler<br/>处理财务相关事件"]:::service
        DoubleEntryService["📊 复式记账服务<br/>DoubleEntryService<br/>复式记账逻辑"]:::service

        AccountsReceivableRepository["💾 应收数据层<br/>AccountsReceivableRepository<br/>应收数据持久化"]:::repository
        AccountsPayableRepository["💾 应付数据层<br/>AccountsPayableRepository<br/>应付数据持久化"]:::repository
        CostCalculationRepository["💾 成本数据层<br/>CostCalculationRepository<br/>成本数据持久化"]:::repository
        FinanceTransactionRepository["💾 财务流水数据层<br/>FinanceTransactionRepository<br/>财务流水持久化"]:::repository

        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>财务缓存管理"]:::service
        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>发布财务事件"]:::service
        ReportGenerator["📈 报表生成器<br/>ReportGenerator<br/>财务报表生成"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>财务数据库<br/>存储财务数据"]:::external
    Cache["⚡ Redis<br/>缓存<br/>缓存财务数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>财务事件处理"]:::external
    BankService["🏦 银行系统<br/>外部系统<br/>银行对接"]:::external

    %% 控制器调用关系
    Gateway -->|"应收请求"| AccountsReceivableController
    Gateway -->|"应付请求"| AccountsPayableController
    Gateway -->|"成本请求"| CostController
    Gateway -->|"对账请求"| ReconciliationController

    %% 服务调用关系
    AccountsReceivableController -->|"应收业务"| AccountsReceivableService
    AccountsPayableController -->|"应付业务"| AccountsPayableService
    CostController -->|"成本业务"| CostCalculationService
    ReconciliationController -->|"对账业务"| ReconciliationService

    %% 业务服务内部调用
    AccountsReceivableService -->|"复式记账"| DoubleEntryService
    AccountsPayableService -->|"复式记账"| DoubleEntryService
    CostCalculationService -->|"成本计算"| DoubleEntryService
    ReconciliationService -->|"银行对接"| BankService

    AccountsReceivableService -->|"数据操作"| AccountsReceivableRepository
    AccountsPayableService -->|"数据操作"| AccountsPayableRepository
    CostCalculationService -->|"数据操作"| CostCalculationRepository
    DoubleEntryService -->|"流水记录"| FinanceTransactionRepository

    FinanceEventHandler -->|"应收处理"| AccountsReceivableService
    FinanceEventHandler -->|"应付处理"| AccountsPayableService
    FinanceEventHandler -->|"成本处理"| CostCalculationService

    AccountsReceivableService -->|"缓存操作"| CacheManager
    AccountsReceivableService -->|"发布事件"| EventPublisher
    CostCalculationService -->|"报表生成"| ReportGenerator

    %% 外部依赖关系
    AccountsReceivableRepository -->|"SQL查询"| Database
    AccountsPayableRepository -->|"SQL查询"| Database
    CostCalculationRepository -->|"SQL查询"| Database
    FinanceTransactionRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue

    %% 事件消费
    MessageQueue -->|"采购财务事件"| FinanceEventHandler
    MessageQueue -->|"销售财务事件"| FinanceEventHandler
```

##### 2.2.3.7 报表分析服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#e91e63,stroke:#c2185b,stroke-width:2px,color:#fff
    classDef service fill:#f06292,stroke:#e91e63,stroke-width:2px,color:#fff
    classDef repository fill:#f8bbd9,stroke:#f06292,stroke-width:2px,color:#fff
    classDef external fill:#ff5722,stroke:#d84315,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 报表服务内部组件
    subgraph "报表分析服务 (pisp-report-service)"
        SalesReportController["📊 销售报表控制器<br/>SalesReportController<br/>销售报表管理"]:::controller
        PurchaseReportController["📋 采购报表控制器<br/>PurchaseReportController<br/>采购报表管理"]:::controller
        InventoryReportController["📦 库存报表控制器<br/>InventoryReportController<br/>库存报表管理"]:::controller
        FinanceReportController["💰 财务报表控制器<br/>FinanceReportController<br/>财务报表管理"]:::controller
        DashboardController["📈 仪表盘控制器<br/>DashboardController<br/>数据仪表盘"]:::controller

        ReportGenerationService["📊 报表生成服务<br/>ReportGenerationService<br/>报表生成逻辑"]:::service
        DataAnalysisService["🔍 数据分析服务<br/>DataAnalysisService<br/>数据分析逻辑"]:::service
        ReportTemplateService["📋 报表模板服务<br/>ReportTemplateService<br/>报表模板管理"]:::service
        DataAggregationService["📈 数据聚合服务<br/>DataAggregationService<br/>数据聚合计算"]:::service
        ReportScheduleService["⏰ 报表调度服务<br/>ReportScheduleService<br/>定时报表生成"]:::service
        ExportService["📤 导出服务<br/>ExportService<br/>报表导出功能"]:::service

        ReportConfigRepository["💾 报表配置数据层<br/>ReportConfigRepository<br/>报表配置持久化"]:::repository
        ReportDataRepository["💾 报表数据数据层<br/>ReportDataRepository<br/>报表数据持久化"]:::repository
        ReportCacheRepository["💾 报表缓存数据层<br/>ReportCacheRepository<br/>报表缓存持久化"]:::repository

        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>报表缓存管理"]:::service
        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>发布报表事件"]:::service
        ReportRenderer["🎨 报表渲染器<br/>ReportRenderer<br/>报表渲染引擎"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>报表数据库<br/>存储报表数据"]:::external
    Cache["⚡ Redis<br/>缓存<br/>缓存报表数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>报表事件处理"]:::external
    DataWarehouse["🏢 数据仓库<br/>数据源<br/>业务数据聚合"]:::external

    %% 控制器调用关系
    Gateway -->|"销售报表请求"| SalesReportController
    Gateway -->|"采购报表请求"| PurchaseReportController
    Gateway -->|"库存报表请求"| InventoryReportController
    Gateway -->|"财务报表请求"| FinanceReportController
    Gateway -->|"仪表盘请求"| DashboardController

    %% 服务调用关系
    SalesReportController -->|"报表生成"| ReportGenerationService
    PurchaseReportController -->|"报表生成"| ReportGenerationService
    InventoryReportController -->|"报表生成"| ReportGenerationService
    FinanceReportController -->|"报表生成"| ReportGenerationService
    DashboardController -->|"数据分析"| DataAnalysisService

    %% 业务服务内部调用
    ReportGenerationService -->|"模板管理"| ReportTemplateService
    ReportGenerationService -->|"数据聚合"| DataAggregationService
    ReportGenerationService -->|"报表渲染"| ReportRenderer
    ReportGenerationService -->|"报表导出"| ExportService

    DataAnalysisService -->|"数据聚合"| DataAggregationService
    ReportScheduleService -->|"报表生成"| ReportGenerationService

    ReportGenerationService -->|"配置数据"| ReportConfigRepository
    ReportGenerationService -->|"报表数据"| ReportDataRepository
    DataAggregationService -->|"数据源"| DataWarehouse

    ReportGenerationService -->|"缓存操作"| CacheManager
    ReportGenerationService -->|"发布事件"| EventPublisher

    %% 外部依赖关系
    ReportConfigRepository -->|"SQL查询"| Database
    ReportDataRepository -->|"SQL查询"| Database
    ReportCacheRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue
```

##### 2.2.3.8 系统管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#607d8b,stroke:#455a64,stroke-width:2px,color:#fff
    classDef service fill:#78909c,stroke:#607d8b,stroke-width:2px,color:#fff
    classDef repository fill:#90a4ae,stroke:#78909c,stroke-width:2px,color:#fff
    classDef external fill:#ff5722,stroke:#d84315,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 系统管理服务内部组件
    subgraph "系统管理服务 (pisp-system-service)"
        SystemConfigController["⚙️ 系统配置控制器<br/>SystemConfigController<br/>系统配置管理"]:::controller
        DataBackupController["💾 数据备份控制器<br/>DataBackupController<br/>数据备份管理"]:::controller
        OperationLogController["📋 操作日志控制器<br/>OperationLogController<br/>操作日志管理"]:::controller
        SystemMonitorController["📊 系统监控控制器<br/>SystemMonitorController<br/>系统监控管理"]:::controller

        SystemConfigService["⚙️ 系统配置服务<br/>SystemConfigService<br/>配置业务逻辑"]:::service
        DataBackupService["💾 数据备份服务<br/>DataBackupService<br/>备份业务逻辑"]:::service
        OperationLogService["📋 操作日志服务<br/>OperationLogService<br/>日志业务逻辑"]:::service
        SystemMonitorService["📊 系统监控服务<br/>SystemMonitorService<br/>监控业务逻辑"]:::service
        ScheduleService["⏰ 调度服务<br/>ScheduleService<br/>定时任务管理"]:::service
        NotificationService["📢 通知服务<br/>NotificationService<br/>系统通知管理"]:::service

        SystemConfigRepository["💾 系统配置数据层<br/>SystemConfigRepository<br/>配置数据持久化"]:::repository
        DataBackupRepository["💾 数据备份数据层<br/>DataBackupRepository<br/>备份数据持久化"]:::repository
        OperationLogRepository["💾 操作日志数据层<br/>OperationLogRepository<br/>日志数据持久化"]:::repository
        SystemMonitorRepository["💾 系统监控数据层<br/>SystemMonitorRepository<br/>监控数据持久化"]:::repository

        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>系统缓存管理"]:::service
        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>发布系统事件"]:::service
        MetricsCollector["📈 指标收集器<br/>MetricsCollector<br/>系统指标收集"]:::service
        AlertManager["🚨 告警管理器<br/>AlertManager<br/>系统告警管理"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>系统数据库<br/>存储系统数据"]:::external
    Cache["⚡ Redis<br/>缓存<br/>缓存系统数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>系统事件处理"]:::external
    FileSystem["📁 文件系统<br/>存储<br/>备份文件存储"]:::external
    MonitoringSystem["📊 监控系统<br/>Prometheus<br/>指标监控"]:::external

    %% 控制器调用关系
    Gateway -->|"配置请求"| SystemConfigController
    Gateway -->|"备份请求"| DataBackupController
    Gateway -->|"日志请求"| OperationLogController
    Gateway -->|"监控请求"| SystemMonitorController

    %% 服务调用关系
    SystemConfigController -->|"配置业务"| SystemConfigService
    DataBackupController -->|"备份业务"| DataBackupService
    OperationLogController -->|"日志业务"| OperationLogService
    SystemMonitorController -->|"监控业务"| SystemMonitorService

    %% 业务服务内部调用
    SystemConfigService -->|"配置缓存"| CacheManager
    DataBackupService -->|"调度任务"| ScheduleService
    DataBackupService -->|"文件存储"| FileSystem
    SystemMonitorService -->|"指标收集"| MetricsCollector
    SystemMonitorService -->|"告警管理"| AlertManager

    OperationLogService -->|"通知服务"| NotificationService
    SystemMonitorService -->|"通知服务"| NotificationService

    SystemConfigService -->|"数据操作"| SystemConfigRepository
    DataBackupService -->|"数据操作"| DataBackupRepository
    OperationLogService -->|"数据操作"| OperationLogRepository
    SystemMonitorService -->|"数据操作"| SystemMonitorRepository

    SystemConfigService -->|"发布事件"| EventPublisher
    MetricsCollector -->|"指标上报"| MonitoringSystem

    %% 外部依赖关系
    SystemConfigRepository -->|"SQL查询"| Database
    DataBackupRepository -->|"SQL查询"| Database
    OperationLogRepository -->|"SQL查询"| Database
    SystemMonitorRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue
```

##### 2.2.3.9 零售管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#e91e63,stroke:#c2185b,stroke-width:2px,color:#fff
    classDef service fill:#f06292,stroke:#e91e63,stroke-width:2px,color:#fff
    classDef repository fill:#f8bbd9,stroke:#f06292,stroke-width:2px,color:#fff
    classDef external fill:#ff5722,stroke:#d84315,stroke-width:2px,color:#fff

    %% 外部调用
    Gateway["🚪 API网关<br/>Apache ShenYu<br/>路由和认证"]:::external

    %% 零售服务内部组件
    subgraph "零售管理服务 (pisp-retail-service)"
        PosSaleController["🛍️ POS销售控制器<br/>PosSaleController<br/>POS销售管理"]:::controller
        MemberController["👤 会员控制器<br/>MemberController<br/>会员管理"]:::controller
        PromotionController["🎁 促销控制器<br/>PromotionController<br/>促销活动管理"]:::controller
        StoreController["🏪 门店控制器<br/>StoreController<br/>门店管理"]:::controller

        PosSaleService["💰 POS销售服务<br/>PosSaleService<br/>POS销售业务逻辑"]:::service
        MemberService["👥 会员服务<br/>MemberService<br/>会员业务逻辑"]:::service
        PromotionService["🎯 促销服务<br/>PromotionService<br/>促销业务逻辑"]:::service
        StoreService["🏢 门店服务<br/>StoreService<br/>门店业务逻辑"]:::service
        PaymentService["💳 支付服务<br/>PaymentService<br/>支付处理逻辑"]:::service
        PointsService["⭐ 积分服务<br/>PointsService<br/>积分管理逻辑"]:::service

        PosSaleRepository["💾 POS销售数据层<br/>PosSaleRepository<br/>销售数据持久化"]:::repository
        MemberRepository["💾 会员数据层<br/>MemberRepository<br/>会员数据持久化"]:::repository
        PromotionRepository["💾 促销数据层<br/>PromotionRepository<br/>促销数据持久化"]:::repository
        StoreRepository["💾 门店数据层<br/>StoreRepository<br/>门店数据持久化"]:::repository

        CacheManager["⚡ 缓存管理器<br/>CacheManager<br/>零售缓存管理"]:::service
        EventPublisher["📡 事件发布器<br/>EventPublisher<br/>发布零售事件"]:::service
        ReceiptPrinter["🖨️ 小票打印器<br/>ReceiptPrinter<br/>小票打印服务"]:::service
    end

    %% 外部依赖
    Database["🗄️ PostgreSQL<br/>零售数据库<br/>存储零售数据"]:::external
    Cache["⚡ Redis<br/>缓存<br/>缓存零售数据"]:::external
    MessageQueue["📨 Kafka<br/>消息队列<br/>零售事件处理"]:::external
    InventoryService["📊 库存服务<br/>微服务<br/>库存管理"]:::external
    BaseDataService["📦 基础数据服务<br/>微服务<br/>商品数据"]:::external

    %% 控制器调用关系
    Gateway -->|"POS销售请求"| PosSaleController
    Gateway -->|"会员请求"| MemberController
    Gateway -->|"促销请求"| PromotionController
    Gateway -->|"门店请求"| StoreController

    %% 服务调用关系
    PosSaleController -->|"销售业务"| PosSaleService
    MemberController -->|"会员业务"| MemberService
    PromotionController -->|"促销业务"| PromotionService
    StoreController -->|"门店业务"| StoreService

    %% 业务服务内部调用
    PosSaleService -->|"支付处理"| PaymentService
    PosSaleService -->|"积分处理"| PointsService
    PosSaleService -->|"促销计算"| PromotionService
    PosSaleService -->|"小票打印"| ReceiptPrinter

    MemberService -->|"积分管理"| PointsService
    PosSaleService -->|"会员验证"| MemberService

    PosSaleService -->|"数据操作"| PosSaleRepository
    MemberService -->|"数据操作"| MemberRepository
    PromotionService -->|"数据操作"| PromotionRepository
    StoreService -->|"数据操作"| StoreRepository

    PosSaleService -->|"缓存操作"| CacheManager
    PosSaleService -->|"发布事件"| EventPublisher

    %% 外部依赖关系
    PosSaleRepository -->|"SQL查询"| Database
    MemberRepository -->|"SQL查询"| Database
    PromotionRepository -->|"SQL查询"| Database
    StoreRepository -->|"SQL查询"| Database
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue

    %% 外部服务调用
    PosSaleService -->|"库存扣减"| InventoryService
    PosSaleService -->|"商品信息"| BaseDataService
```

##### ******** 前置仓管理服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#00bcd4,stroke:#0097a7,stroke-width:2px,color:#fff
    classDef service fill:#4caf50,stroke:#388e3c,stroke-width:2px,color:#fff
    classDef repository fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#fff
    classDef component fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff
    classDef external fill:#607d8b,stroke:#455a64,stroke-width:2px,color:#fff

    %% 控制器层
    subgraph "控制器层"
        FrontWarehouseController["🏪 前置仓控制器<br/>FrontWarehouseController"]:::controller
        InventoryAllocationController["📦 库存分配控制器<br/>InventoryAllocationController"]:::controller
        ReplenishmentController["🔄 补货控制器<br/>ReplenishmentController"]:::controller
        OrderFulfillmentController["📋 订单履约控制器<br/>OrderFulfillmentController"]:::controller
        PickingController["📝 拣选控制器<br/>PickingController"]:::controller
        PackingController["📦 打包控制器<br/>PackingController"]:::controller
        DeliveryController["🚚 配送控制器<br/>DeliveryController"]:::controller
    end

    %% 业务服务层
    subgraph "业务服务层"
        FrontWarehouseService["🏪 前置仓服务<br/>FrontWarehouseService"]:::service
        InventoryAllocationService["📦 库存分配服务<br/>InventoryAllocationService"]:::service
        ReplenishmentService["🔄 智能补货服务<br/>ReplenishmentService"]:::service
        InventorySyncService["🔄 库存同步服务<br/>InventorySyncService"]:::service
        OrderAssignmentService["📋 订单分配服务<br/>OrderAssignmentService"]:::service
        PickingService["📝 拣选服务<br/>PickingService"]:::service
        PackingService["📦 打包服务<br/>PackingService"]:::service
        DeliveryService["🚚 配送服务<br/>DeliveryService"]:::service
        PredictionService["📊 预测分析服务<br/>PredictionService"]:::service
    end

    %% 数据访问层
    subgraph "数据访问层"
        FrontWarehouseRepository["🏪 前置仓仓储<br/>FrontWarehouseRepository"]:::repository
        InventoryRepository["📦 库存仓储<br/>FrontWarehouseInventoryRepository"]:::repository
        AllocationRepository["📊 分配仓储<br/>AllocationRepository"]:::repository
        ReplenishmentRepository["🔄 补货仓储<br/>ReplenishmentRepository"]:::repository
        OrderAssignmentRepository["📋 订单分配仓储<br/>OrderAssignmentRepository"]:::repository
        PickingRepository["📝 拣选仓储<br/>PickingRepository"]:::repository
        PackingRepository["📦 打包仓储<br/>PackingRepository"]:::repository
        DeliveryRepository["🚚 配送仓储<br/>DeliveryRepository"]:::repository
    end

    %% 支撑组件层
    subgraph "支撑组件层"
        CacheManager["⚡ 缓存管理器<br/>CacheManager"]:::component
        EventPublisher["📨 事件发布器<br/>EventPublisher"]:::component
        GeoLocationService["🗺️ 地理位置服务<br/>GeoLocationService"]:::component
        RouteOptimizer["🛣️ 路线优化器<br/>RouteOptimizer"]:::component
        DemandPredictor["📈 需求预测器<br/>DemandPredictor"]:::component
        NotificationService["🔔 通知服务<br/>NotificationService"]:::component
    end

    %% 外部依赖
    subgraph "外部依赖"
        Database["🗄️ 数据库<br/>PostgreSQL"]:::external
        Cache["⚡ 缓存<br/>Redis"]:::external
        MessageQueue["📨 消息队列<br/>Kafka"]:::external
        MapAPI["🗺️ 地图API<br/>高德/百度地图"]:::external
        InventoryService["📊 库存服务<br/>InventoryService"]:::external
        OrderService["📋 订单服务<br/>OrderService"]:::external
    end

    %% 控制器到服务的关系
    FrontWarehouseController --> FrontWarehouseService
    InventoryAllocationController --> InventoryAllocationService
    ReplenishmentController --> ReplenishmentService
    OrderFulfillmentController --> OrderAssignmentService
    PickingController --> PickingService
    PackingController --> PackingService
    DeliveryController --> DeliveryService

    %% 服务到仓储的关系
    FrontWarehouseService --> FrontWarehouseRepository
    InventoryAllocationService --> InventoryRepository
    InventoryAllocationService --> AllocationRepository
    ReplenishmentService --> ReplenishmentRepository
    InventorySyncService --> InventoryRepository
    OrderAssignmentService --> OrderAssignmentRepository
    PickingService --> PickingRepository
    PackingService --> PackingRepository
    DeliveryService --> DeliveryRepository

    %% 服务到组件的关系
    FrontWarehouseService --> CacheManager
    InventoryAllocationService --> CacheManager
    InventoryAllocationService --> EventPublisher
    ReplenishmentService --> DemandPredictor
    ReplenishmentService --> EventPublisher
    InventorySyncService --> EventPublisher
    OrderAssignmentService --> GeoLocationService
    PickingService --> RouteOptimizer
    DeliveryService --> RouteOptimizer
    DeliveryService --> GeoLocationService
    DeliveryService --> NotificationService

    %% 仓储到数据库的关系
    FrontWarehouseRepository -->|"SQL查询"| Database
    InventoryRepository -->|"SQL查询"| Database
    AllocationRepository -->|"SQL查询"| Database
    ReplenishmentRepository -->|"SQL查询"| Database
    OrderAssignmentRepository -->|"SQL查询"| Database
    PickingRepository -->|"SQL查询"| Database
    PackingRepository -->|"SQL查询"| Database
    DeliveryRepository -->|"SQL查询"| Database

    %% 组件到外部依赖的关系
    CacheManager -->|"缓存读写"| Cache
    EventPublisher -->|"发送消息"| MessageQueue
    GeoLocationService -->|"地理计算"| MapAPI
    RouteOptimizer -->|"路线规划"| MapAPI
    NotificationService -->|"发送消息"| MessageQueue

    %% 事件消费
    MessageQueue -->|"库存变动事件"| InventorySyncService
    MessageQueue -->|"订单事件"| OrderAssignmentService

    %% 外部服务调用
    InventoryAllocationService -->|"库存查询"| InventoryService
    OrderAssignmentService -->|"订单信息"| OrderService
    InventorySyncService -->|"库存同步"| InventoryService
```

##### 2.2.3.11 订单履约服务组件图

```mermaid
graph TB
    %% 定义样式
    classDef controller fill:#ff5722,stroke:#d84315,stroke-width:2px,color:#fff
    classDef service fill:#4caf50,stroke:#388e3c,stroke-width:2px,color:#fff
    classDef repository fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#fff
    classDef component fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff
    classDef external fill:#607d8b,stroke:#455a64,stroke-width:2px,color:#fff

    %% 控制器层
    subgraph "控制器层"
        OrderFulfillmentController["📋 订单履约控制器<br/>OrderFulfillmentController"]:::controller
        PickingTaskController["📝 拣选任务控制器<br/>PickingTaskController"]:::controller
        PackingTaskController["📦 打包任务控制器<br/>PackingTaskController"]:::controller
        DeliveryTaskController["🚚 配送任务控制器<br/>DeliveryTaskController"]:::controller
    end

    %% 业务服务层
    subgraph "业务服务层"
        OrderAssignmentService["📋 订单分配服务<br/>OrderAssignmentService"]:::service
        PickingTaskService["📝 拣选任务服务<br/>PickingTaskService"]:::service
        PackingTaskService["📦 打包任务服务<br/>PackingTaskService"]:::service
        DeliveryTaskService["🚚 配送任务服务<br/>DeliveryTaskService"]:::service
        WorkflowEngine["⚙️ 工作流引擎<br/>WorkflowEngine"]:::service
    end

    %% 算法组件层
    subgraph "算法组件层"
        OrderAllocationAlgorithm["🎯 订单分配算法<br/>OrderAllocationAlgorithm"]:::component
        PickingPathOptimizer["🛣️ 拣选路径优化<br/>PickingPathOptimizer"]:::component
        PackingOptimizer["📦 打包优化算法<br/>PackingOptimizer"]:::component
        DeliveryRouteOptimizer["🚚 配送路线优化<br/>DeliveryRouteOptimizer"]:::component
    end

    %% 数据访问层
    subgraph "数据访问层"
        OrderAssignmentRepository["📋 订单分配仓储<br/>OrderAssignmentRepository"]:::repository
        PickingTaskRepository["📝 拣选任务仓储<br/>PickingTaskRepository"]:::repository
        PackingTaskRepository["📦 打包任务仓储<br/>PackingTaskRepository"]:::repository
        DeliveryTaskRepository["🚚 配送任务仓储<br/>DeliveryTaskRepository"]:::repository
    end

    %% 外部依赖
    subgraph "外部依赖"
        Database["🗄️ 数据库<br/>PostgreSQL"]:::external
        Cache["⚡ 缓存<br/>Redis"]:::external
        MessageQueue["📨 消息队列<br/>Kafka"]:::external
        FrontWarehouseService["🏪 前置仓服务<br/>FrontWarehouseService"]:::external
    end

    %% 控制器到服务的关系
    OrderFulfillmentController --> OrderAssignmentService
    PickingTaskController --> PickingTaskService
    PackingTaskController --> PackingTaskService
    DeliveryTaskController --> DeliveryTaskService

    %% 服务间关系
    OrderAssignmentService --> WorkflowEngine
    PickingTaskService --> WorkflowEngine
    PackingTaskService --> WorkflowEngine
    DeliveryTaskService --> WorkflowEngine

    %% 服务到算法组件的关系
    OrderAssignmentService --> OrderAllocationAlgorithm
    PickingTaskService --> PickingPathOptimizer
    PackingTaskService --> PackingOptimizer
    DeliveryTaskService --> DeliveryRouteOptimizer

    %% 服务到仓储的关系
    OrderAssignmentService --> OrderAssignmentRepository
    PickingTaskService --> PickingTaskRepository
    PackingTaskService --> PackingTaskRepository
    DeliveryTaskService --> DeliveryTaskRepository

    %% 仓储到数据库的关系
    OrderAssignmentRepository -->|"SQL查询"| Database
    PickingTaskRepository -->|"SQL查询"| Database
    PackingTaskRepository -->|"SQL查询"| Database
    DeliveryTaskRepository -->|"SQL查询"| Database

    %% 缓存关系
    OrderAssignmentService -->|"缓存读写"| Cache
    PickingTaskService -->|"缓存读写"| Cache

    %% 消息队列关系
    WorkflowEngine -->|"状态变更事件"| MessageQueue

    %% 外部服务调用
    OrderAssignmentService -->|"前置仓信息"| FrontWarehouseService
```

#### 2.2.3.12 组件图总结

通过详细的组件图设计，我们清晰地展示了每个微服务的内部架构：

**🏗️ 架构分层：**
- **控制器层 (Controller)**：处理HTTP请求，参数验证，响应封装
- **业务服务层 (Service)**：核心业务逻辑，业务规则实现
- **数据访问层 (Repository)**：数据持久化，SQL操作封装
- **支撑组件层**：缓存、事件、验证、工具等横切关注点

**🎯 设计模式：**
- **依赖注入**：Spring IoC容器管理组件依赖
- **事件驱动**：通过Kafka实现服务间异步通信
- **缓存模式**：Redis缓存提升性能
- **仓储模式**：Repository封装数据访问逻辑
- **发布订阅**：EventPublisher实现事件发布

**🔧 技术特点：**
- **Spring Boot 3.4.7**：统一的微服务框架
- **MyBatis-Plus 3.5.12**：简化数据访问层开发
- **Spring Cache**：声明式缓存管理
- **Bean Validation**：统一的数据验证
- **Spring Event**：内部事件机制

**📊 服务特色：**
- **用户服务**：RBAC权限模型，JWT认证
- **基础数据服务**：主数据管理，数据变更事件
- **采购服务**：工作流引擎，业务流程控制
- **销售服务**：订单状态机，价格计算引擎
- **库存服务**：事件驱动，实时库存计算
- **财务服务**：复式记账，成本核算
- **报表服务**：数据仓库模式，报表渲染引擎
- **系统服务**：配置中心模式，系统监控
- **零售服务**：POS销售，会员体系，促销引擎
- **前置仓服务**：分布式库存管理，智能补货算法，地理位置服务
- **订单履约服务**：工作流引擎，TSP路径优化，VRP配送优化，实时状态跟踪

#### 2.2.4 C4 - 部署图 (Deployment Diagram)

```mermaid
graph TB
    %% 定义样式
    classDef loadbalancer fill:#ff5722,stroke:#d84315,stroke-width:2px,color:#fff
    classDef gateway fill:#3f51b5,stroke:#303f9f,stroke-width:2px,color:#fff
    classDef microservice fill:#4caf50,stroke:#388e3c,stroke-width:2px,color:#fff
    classDef database fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#fff
    classDef infrastructure fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff

    %% 负载均衡层
    subgraph "负载均衡层 (Nginx集群)"
        Nginx1["⚖️ Nginx-1<br/>负载均衡器<br/>主负载均衡"]:::loadbalancer
        Nginx2["⚖️ Nginx-2<br/>负载均衡器<br/>备负载均衡"]:::loadbalancer
    end

    %% 网关层
    subgraph "网关层 (ShenYu集群)"
        Gateway1["🚪 ShenYu Gateway-1<br/>API网关<br/>网关实例1"]:::gateway
        Gateway2["🚪 ShenYu Gateway-2<br/>API网关<br/>网关实例2"]:::gateway
    end

    %% 微服务层
    subgraph "微服务层 (Spring Boot集群)"
        UserSvc1["👥 用户服务-1<br/>pisp-user-service<br/>用户管理"]:::microservice
        UserSvc2["👥 用户服务-2<br/>pisp-user-service<br/>用户管理"]:::microservice
        PurchaseSvc1["🛒 采购服务-1<br/>pisp-purchase-service<br/>采购管理"]:::microservice
        PurchaseSvc2["🛒 采购服务-2<br/>pisp-purchase-service<br/>采购管理"]:::microservice
        SalesSvc1["💰 销售服务-1<br/>pisp-sales-service<br/>销售管理"]:::microservice
        SalesSvc2["💰 销售服务-2<br/>pisp-sales-service<br/>销售管理"]:::microservice
        FrontWarehouseSvc1["🏪 前置仓服务-1<br/>pisp-front-warehouse-service<br/>前置仓管理"]:::microservice
        FrontWarehouseSvc2["🏪 前置仓服务-2<br/>pisp-front-warehouse-service<br/>前置仓管理"]:::microservice
        OrderFulfillmentSvc1["📋 订单履约服务-1<br/>pisp-order-fulfillment-service<br/>订单履约"]:::microservice
        OrderFulfillmentSvc2["📋 订单履约服务-2<br/>pisp-order-fulfillment-service<br/>订单履约"]:::microservice
    end

    %% 注册中心
    subgraph "注册中心 (Nacos集群)"
        Nacos1["🎯 Nacos-1<br/>注册中心<br/>服务注册"]:::infrastructure
        Nacos2["🎯 Nacos-2<br/>注册中心<br/>服务注册"]:::infrastructure
        Nacos3["🎯 Nacos-3<br/>注册中心<br/>服务注册"]:::infrastructure
    end

    %% 数据层
    subgraph "数据层 (数据库集群)"
        PGMaster["🗄️ PostgreSQL-Master<br/>主数据库<br/>读写数据库"]:::database
        PGSlave1["🗄️ PostgreSQL-Slave-1<br/>从数据库<br/>只读数据库"]:::database
        PGSlave2["🗄️ PostgreSQL-Slave-2<br/>从数据库<br/>只读数据库"]:::database
        Redis1["⚡ Redis-Master-1<br/>缓存主节点<br/>缓存数据"]:::database
        Redis2["⚡ Redis-Master-2<br/>缓存主节点<br/>缓存数据"]:::database
        Redis3["⚡ Redis-Master-3<br/>缓存主节点<br/>缓存数据"]:::database
    end

    %% 消息层
    subgraph "消息层 (RocketMQ集群)"
        RocketMQ1["📨 RocketMQ-1<br/>消息队列<br/>消息处理"]:::infrastructure
        RocketMQ2["📨 RocketMQ-2<br/>消息队列<br/>消息处理"]:::infrastructure
        RocketMQ3["📨 RocketMQ-3<br/>消息队列<br/>消息处理"]:::infrastructure
    end

    %% 负载均衡关系
    Nginx1 -->|"负载均衡<br/>HTTP"| Gateway1
    Nginx1 -->|"负载均衡<br/>HTTP"| Gateway2
    Nginx2 -->|"负载均衡<br/>HTTP"| Gateway1
    Nginx2 -->|"负载均衡<br/>HTTP"| Gateway2

    %% 网关路由关系
    Gateway1 -->|"路由<br/>HTTP"| UserSvc1
    Gateway1 -->|"路由<br/>HTTP"| PurchaseSvc1
    Gateway1 -->|"路由<br/>HTTP"| SalesSvc1
    Gateway1 -->|"路由<br/>HTTP"| FrontWarehouseSvc1
    Gateway1 -->|"路由<br/>HTTP"| OrderFulfillmentSvc1
    Gateway2 -->|"路由<br/>HTTP"| UserSvc2
    Gateway2 -->|"路由<br/>HTTP"| PurchaseSvc2
    Gateway2 -->|"路由<br/>HTTP"| SalesSvc2
    Gateway2 -->|"路由<br/>HTTP"| FrontWarehouseSvc2
    Gateway2 -->|"路由<br/>HTTP"| OrderFulfillmentSvc2

    %% 服务注册关系
    UserSvc1 -->|"注册<br/>HTTP"| Nacos1
    UserSvc2 -->|"注册<br/>HTTP"| Nacos2
    PurchaseSvc1 -->|"注册<br/>HTTP"| Nacos3
    PurchaseSvc2 -->|"注册<br/>HTTP"| Nacos1
    FrontWarehouseSvc1 -->|"注册<br/>HTTP"| Nacos2
    FrontWarehouseSvc2 -->|"注册<br/>HTTP"| Nacos3
    OrderFulfillmentSvc1 -->|"注册<br/>HTTP"| Nacos1
    OrderFulfillmentSvc2 -->|"注册<br/>HTTP"| Nacos2

    %% 数据库访问关系
    UserSvc1 -->|"读写<br/>JDBC"| PGMaster
    UserSvc2 -->|"只读<br/>JDBC"| PGSlave1
    PurchaseSvc1 -->|"读写<br/>JDBC"| PGMaster
    PurchaseSvc2 -->|"只读<br/>JDBC"| PGSlave2
    FrontWarehouseSvc1 -->|"读写<br/>JDBC"| PGMaster
    FrontWarehouseSvc2 -->|"只读<br/>JDBC"| PGSlave1
    OrderFulfillmentSvc1 -->|"读写<br/>JDBC"| PGMaster
    OrderFulfillmentSvc2 -->|"只读<br/>JDBC"| PGSlave2

    %% 缓存访问关系
    UserSvc1 -->|"缓存<br/>Redis"| Redis1
    PurchaseSvc1 -->|"缓存<br/>Redis"| Redis2
    SalesSvc1 -->|"缓存<br/>Redis"| Redis3
    FrontWarehouseSvc1 -->|"缓存<br/>Redis"| Redis1
    FrontWarehouseSvc2 -->|"缓存<br/>Redis"| Redis2
    OrderFulfillmentSvc1 -->|"缓存<br/>Redis"| Redis3
    OrderFulfillmentSvc2 -->|"缓存<br/>Redis"| Redis1

    %% 消息队列关系
    PurchaseSvc1 -->|"发布事件<br/>RocketMQ"| RocketMQ1
    SalesSvc1 -->|"发布事件<br/>RocketMQ"| RocketMQ2
    FrontWarehouseSvc1 -->|"发布事件<br/>RocketMQ"| RocketMQ3
    OrderFulfillmentSvc1 -->|"发布事件<br/>RocketMQ"| RocketMQ1
```

### 2.3 C4架构图总结

通过C4模型的四个层次，我们清晰地展示了PISP进销存管理系统的架构：

**C1 - 系统上下文图：**
- 🎯 **用户角色**：系统管理员、业务用户、管理层、前置仓员工、终端客户
- 🎯 **核心系统**：PISP进销存管理系统（含前置仓管理）
- 🎯 **外部系统**：银行、供应商、B2B客户、税务、物流API、支付网关

**C2 - 容器图：**
- 🏗️ **前端层**：Web应用(Vue 3) + 移动应用(React Native) + 前置仓作业APP + 客户小程序
- 🏗️ **网关层**：Nginx负载均衡 + ShenYu API网关
- 🏗️ **服务层**：11个核心微服务(Spring Boot 3.4.7)，包含前置仓管理和订单履约服务
- 🏗️ **数据层**：PostgreSQL + Redis + RocketMQ + Nacos

**C3 - 组件图：**
- 🔧 **控制器层**：处理HTTP请求和响应
- 🔧 **业务层**：核心业务逻辑处理，包含前置仓智能算法
- 🔧 **数据层**：数据持久化和缓存
- 🔧 **事件层**：异步事件发布和消费
- 🔧 **算法层**：TSP路径优化、VRP配送优化、需求预测算法

**C4 - 部署图：**
- 🚀 **高可用集群**：每层都有多节点部署，包含前置仓边缘部署
- 🚀 **负载均衡**：Nginx + ShenYu双重负载均衡
- 🚀 **数据冗余**：PostgreSQL主从 + Redis集群 + 前置仓本地数据库
- 🚀 **消息可靠**：RocketMQ集群保证消息可靠性和数据同步


### 2.4 微服务架构

```mermaid
graph TB
    %% 定义样式
    classDef user fill:#1976d2,stroke:#0d47a1,stroke-width:2px,color:#fff
    classDef base fill:#388e3c,stroke:#1b5e20,stroke-width:2px,color:#fff
    classDef business fill:#f57c00,stroke:#e65100,stroke-width:2px,color:#fff
    classDef finance fill:#7b1fa2,stroke:#4a148c,stroke-width:2px,color:#fff
    classDef system fill:#5d4037,stroke:#3e2723,stroke-width:2px,color:#fff

    subgraph "用户权限层"
        A1["👥 用户管理服务<br/>pisp-user-service<br/>用户、角色、权限管理"]:::user
    end

    subgraph "基础数据层"
        B1["📦 基础数据服务<br/>pisp-base-data-service<br/>商品、客户、供应商、仓库"]:::base
    end

    subgraph "业务流程层"
        C1["🛒 采购管理服务<br/>pisp-purchase-service<br/>采购订单、入库、退货"]:::business
        C2["💰 销售管理服务<br/>pisp-sales-service<br/>销售订单、出库、退货"]:::business
        C3["📊 库存管理服务<br/>pisp-inventory-service<br/>库存查询、盘点、预警"]:::business
    end

    subgraph "财务分析层"
        D1["💳 财务管理服务<br/>pisp-finance-service<br/>应收应付、成本核算"]:::finance
        D2["📈 报表分析服务<br/>pisp-report-service<br/>报表生成、数据分析"]:::finance
    end

    subgraph "系统管理层"
        E1["⚙️ 系统管理服务<br/>pisp-system-service<br/>系统配置、日志管理"]:::system
    end

    subgraph "零售业务层"
        F1["🛍️ 零售管理服务<br/>pisp-retail-service<br/>POS销售、会员管理、促销活动"]:::business
    end

    %% 依赖关系
    A1 -->|"权限验证"| B1
    A1 -->|"权限验证"| C1
    A1 -->|"权限验证"| C2
    A1 -->|"权限验证"| C3
    A1 -->|"权限验证"| D1
    A1 -->|"权限验证"| D2
    A1 -->|"权限验证"| E1
    A1 -->|"权限验证"| F1

    B1 -->|"基础数据"| C1
    B1 -->|"基础数据"| C2
    B1 -->|"基础数据"| C3
    B1 -->|"基础数据"| F1

    C1 -->|"库存变动"| C3
    C2 -->|"库存变动"| C3
    C1 -->|"财务数据"| D1
    C2 -->|"财务数据"| D1
    C3 -->|"库存数据"| D1

    C1 -->|"业务数据"| D2
    C2 -->|"业务数据"| D2
    C3 -->|"库存数据"| D2
    D1 -->|"财务数据"| D2

    F1 -->|"零售库存"| C3
    F1 -->|"零售财务"| D1
    F1 -->|"零售数据"| D2
```

## 3. 微服务架构设计

### 3.1 服务拆分原则

1. **业务边界清晰**：按照DDD领域驱动设计进行服务拆分
2. **数据独立性**：每个服务拥有独立的数据库Schema
3. **团队自治**：每个服务由独立团队负责开发和维护
4. **技术栈统一**：使用统一的Spring Cloud技术栈
5. **接口标准化**：统一的RESTful API设计规范
6. **事件驱动**：通过Kafka实现服务间异步通信

### 3.2 服务清单

| 服务名称 | 端口 | 职责描述 | 数据库Schema |
|----------|------|----------|--------------|
| **pisp-user-service** | 8001 | 用户管理、角色权限、组织架构 | pisp_user |
| **pisp-base-data-service** | 8002 | 商品、客户、供应商、仓库管理 | pisp_base_data |
| **pisp-purchase-service** | 8003 | 采购订单、采购入库、采购退货 | pisp_purchase |
| **pisp-sales-service** | 8004 | 销售订单、销售出库、销售退货 | pisp_sales |
| **pisp-inventory-service** | 8005 | 库存查询、库存盘点、库存预警 | pisp_inventory |
| **pisp-finance-service** | 8006 | 应收应付、成本核算、财务对账 | pisp_finance |
| **pisp-report-service** | 8007 | 报表生成、数据分析、统计查询 | pisp_report |
| **pisp-system-service** | 8008 | 系统配置、数据备份、日志管理 | pisp_system |
| **pisp-retail-service** | 8009 | POS销售、会员管理、促销活动、门店管理 | pisp_retail |
| **pisp-gateway** | 9195 | API网关、路由转发、认证鉴权 | - |
| **nacos-server** | 8848 | 服务注册发现、配置管理 | nacos |

### 3.3 Nacos注册中心

**技术栈：** Nacos 3.0.2 + Spring Cloud 2024.0.1

**核心特性：**
- 服务注册与发现：动态服务注册和健康检查
- 配置管理：集中化配置管理和动态刷新
- 命名空间：多环境隔离和租户管理
- 集群部署：高可用集群架构
- 控制台管理：可视化管理界面

**配置示例：**
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: pisp-system
        group: DEFAULT_GROUP
      config:
        server-addr: 127.0.0.1:8848
        namespace: pisp-system
        group: DEFAULT_GROUP
        file-extension: yml
```

## 4. 核心服务设计

### 3.1 Apache ShenYu网关

**技术栈：** Apache ShenYu ******* + Spring Boot 3.4.7

**核心特性：**
- 高性能：基于WebFlux响应式编程
- 插件化架构：丰富的插件生态
- 动态配置：支持热更新配置
- 多协议支持：HTTP、gRPC、WebSocket等
- 可视化管理：ShenYu Admin控制台

**架构组件：**
```mermaid
graph TB
    subgraph "ShenYu网关架构"
        A1[ShenYu Bootstrap]
        A2[ShenYu Admin]
        A3[ShenYu Dashboard]
    end

    subgraph "插件系统"
        B1[认证插件 JWT/OAuth2]
        B2[限流插件 RateLimiter]
        B3[熔断插件 Hystrix]
        B4[监控插件 Monitor]
        B5[负载均衡插件]
        B6[缓存插件]
    end

    subgraph "后端服务"
        C1[用户管理服务<br/>pisp-user-service]
        C2[基础数据服务<br/>pisp-base-data-service]
        C3[采购管理服务<br/>pisp-purchase-service]
        C4[销售管理服务<br/>pisp-sales-service]
        C5[库存管理服务<br/>pisp-inventory-service]
        C6[财务管理服务<br/>pisp-finance-service]
        C7[报表分析服务<br/>pisp-report-service]
        C8[系统管理服务<br/>pisp-system-service]
    end

    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4
    A1 --> B5
    A1 --> B6

    A2 --> A1
    A3 --> A2

    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    B1 --> C5
    B1 --> C6
    B1 --> C7
    B1 --> C8

    B2 --> C1
    B2 --> C2
    B2 --> C3
    B2 --> C4
    B2 --> C5
    B2 --> C6
    B2 --> C7
    B2 --> C8
```

**Maven依赖配置：**
```xml
<!-- ShenYu Gateway Bootstrap -->
<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-gateway</artifactId>
    <version>*******</version>
</dependency>

<!-- ShenYu核心插件 -->
<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-plugin-divide</artifactId>
    <version>*******</version>
</dependency>

<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-plugin-httpclient</artifactId>
    <version>*******</version>
</dependency>

<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-plugin-resilience4j</artifactId>
    <version>*******</version>
</dependency>

<!-- ShenYu *******新增插件 -->
<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-plugin-jwt</artifactId>
    <version>*******</version>
</dependency>

<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-plugin-oauth2</artifactId>
    <version>*******</version>
</dependency>

<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-plugin-cache</artifactId>
    <version>*******</version>
</dependency>

<dependency>
    <groupId>org.apache.shenyu</groupId>
    <artifactId>shenyu-spring-boot-starter-plugin-logging</artifactId>
    <version>*******</version>
</dependency>
```

**ShenYu *******配置：**
```yaml
shenyu:
  register:
    registerType: nacos
    serverLists: localhost:8848
    props:
      username: nacos
      password: nacos
      namespace: public
  cross:
    enabled: true
    allowedHeaders: "*"
    allowedMethods: "*"
    allowedOrigin: "*"
    allowCredentials: true
    maxAge: 18000
  switchConfig:
    local: true
  file:
    enabled: true
  exclude:
    enabled: false
    paths:
      - /favicon.ico
      - /actuator/**
  extPlugin:
    path: ""
    enabled: true
    threads: 1
    scheduleTime: 300
    scheduleDelay: 30
  scheduler:
    enabled: false
    type: fixed
    threads: 16
  upstreamCheck:
    enabled: true
    timeout: 3000
    healthyThreshold: 1
    unhealthyThreshold: 1
    interval: 5000
    printEnabled: true
    printInterval: 60000
  # *******新增配置
  netty:
    http:
      webServerFactoryEnabled: true
      selectCount: 1
      workerCount: 8
      accessLog: false
      serverSocketChannel:
        soRcvBuf: 87380
        soBackLog: 128
        soReuseAddr: false
        connectTimeoutMillis: 10000
        writeBufferHighWaterMark: 65536
        writeBufferLowWaterMark: 32768
        writeSpinCount: 16
        autoRead: false
        allocType: "pooled"
      socketChannel:
        soKeepAlive: false
        soReuseAddr: false
        soLinger: -1
        tcpNoDelay: true
        soRcvBuf: 87380
        soSndBuf: 16384
        ipTos: 0
        allowHalfClosure: false
        connectTimeoutMillis: 10000
        writeBufferHighWaterMark: 65536
        writeBufferLowWaterMark: 32768
        writeSpinCount: 16
        autoRead: false
        allocType: "pooled"
  # 插件配置
  plugin:
    jwt:
      enabled: true
      secretKey: "mySecretKey"
    oauth2:
      enabled: true
    cache:
      enabled: true
      type: redis
    logging:
      enabled: true
      topic: shenyu-access-logging
```

### 3.2 ShenYu插件系统详细设计

#### 3.2.1 插件执行链

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant GlobalPlugin
    participant AuthPlugin
    participant RateLimitPlugin
    participant DividePlugin
    participant Backend

    Client->>Gateway: HTTP Request
    Gateway->>GlobalPlugin: execute()
    GlobalPlugin->>AuthPlugin: execute()
    AuthPlugin->>RateLimitPlugin: execute()
    RateLimitPlugin->>DividePlugin: execute()
    DividePlugin->>Backend: Forward Request
    Backend-->>DividePlugin: Response
    DividePlugin-->>RateLimitPlugin: Response
    RateLimitPlugin-->>AuthPlugin: Response
    AuthPlugin-->>GlobalPlugin: Response
    GlobalPlugin-->>Gateway: Response
    Gateway-->>Client: HTTP Response
```

#### 3.2.2 核心插件配置

**1. JWT认证插件：**
```java
@Component
public class JwtPlugin implements ShenYuPlugin {

    @Override
    public Mono<Void> execute(ServerWebExchange exchange, ShenYuPluginChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().value();

        // 跳过认证的路径
        if (shouldSkip(path)) {
            return chain.execute(exchange);
        }

        String token = extractToken(request);
        if (StringUtils.isEmpty(token)) {
            return handleUnauthorized(exchange);
        }

        return validateToken(token)
            .flatMap(claims -> {
                // 添加用户信息到请求头
                ServerHttpRequest mutatedRequest = request.mutate()
                    .header("X-User-Id", claims.get("userId", String.class))
                    .header("X-Username", claims.getSubject())
                    .header("X-User-Roles", String.join(",", claims.get("roles", List.class)))
                    .build();

                return chain.execute(exchange.mutate().request(mutatedRequest).build());
            })
            .onErrorResume(throwable -> handleUnauthorized(exchange));
    }

    @Override
    public int getOrder() {
        return PluginEnum.JWT.getCode();
    }

    @Override
    public String named() {
        return PluginEnum.JWT.getName();
    }

    private boolean shouldSkip(String path) {
        return Arrays.asList("/api/v1/auth/login", "/api/v1/auth/register",
                           "/api/v1/public", "/actuator/health")
                     .stream().anyMatch(path::startsWith);
    }
}
```

**2. 限流插件配置：**
```java
@Component
public class RateLimiterPlugin implements ShenYuPlugin {

    private final RedisTemplate<String, String> redisTemplate;
    private final RedisScript<Long> rateLimiterScript;

    @Override
    public Mono<Void> execute(ServerWebExchange exchange, ShenYuPluginChain chain) {
        String key = buildKey(exchange.getRequest());

        return Mono.fromCallable(() -> {
            List<String> keys = Collections.singletonList(key);
            // 令牌桶算法：每秒10个令牌，桶容量20
            Long result = redisTemplate.execute(rateLimiterScript, keys, "10", "20", "1");
            return result != null && result == 1L;
        })
        .flatMap(allowed -> {
            if (allowed) {
                return chain.execute(exchange);
            } else {
                return handleRateLimited(exchange);
            }
        });
    }

    private String buildKey(ServerHttpRequest request) {
        String clientIp = getClientIp(request);
        String userId = request.getHeaders().getFirst("X-User-Id");
        return "rate_limit:" + (userId != null ? userId : clientIp);
    }
}
```

**3. 缓存插件：**
```java
@Component
public class CachePlugin implements ShenYuPlugin {

    @Override
    public Mono<Void> execute(ServerWebExchange exchange, ShenYuPluginChain chain) {
        ServerHttpRequest request = exchange.getRequest();

        // 只缓存GET请求
        if (!HttpMethod.GET.equals(request.getMethod())) {
            return chain.execute(exchange);
        }

        String cacheKey = buildCacheKey(request);

        return getCachedResponse(cacheKey)
            .switchIfEmpty(
                chain.execute(exchange)
                    .then(cacheResponse(exchange, cacheKey))
            );
    }

    private String buildCacheKey(ServerHttpRequest request) {
        return "cache:" + request.getPath().value() + ":" +
               request.getQueryParams().toString().hashCode();
    }
}
```

#### 3.2.3 自定义插件开发

**插件开发模板：**
```java
@Component
public class CustomBusinessPlugin implements ShenYuPlugin {

    @Override
    public Mono<Void> execute(ServerWebExchange exchange, ShenYuPluginChain chain) {
        // 前置处理
        return doPreProcess(exchange)
            .then(chain.execute(exchange))
            .then(doPostProcess(exchange));
    }

    private Mono<Void> doPreProcess(ServerWebExchange exchange) {
        // 请求预处理逻辑
        return Mono.empty();
    }

    private Mono<Void> doPostProcess(ServerWebExchange exchange) {
        // 响应后处理逻辑
        return Mono.empty();
    }

    @Override
    public int getOrder() {
        return PluginEnum.CUSTOM_BUSINESS.getCode();
    }

    @Override
    public String named() {
        return "customBusiness";
    }

    @Override
    public boolean skip(ServerWebExchange exchange) {
        // 跳过条件
        return false;
    }
}
```

### 3.3 服务注册发现设计

#### 3.3.1 Nacos集成

```mermaid
graph TB
    subgraph "服务注册发现"
        A[Nacos Server]
        B[ShenYu Gateway]
        C[User Service]
        D[Product Service]
        E[Order Service]
        F[Inventory Service]
    end

    C --> A
    D --> A
    E --> A
    F --> A
    B --> A

    B --> C
    B --> D
    B --> E
    B --> F
```

**Nacos配置：**
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        username: nacos
        password: nacos
        namespace: public
        group: DEFAULT_GROUP
        register-enabled: true
        heart-beat-interval: 5000
        heart-beat-timeout: 15000
        ip-delete-timeout: 30000
        metadata:
          version: ${spring.application.version:1.0.0}
          group: pisp-system
      config:
        server-addr: localhost:8848
        username: nacos
        password: nacos
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
```

#### 3.3.2 负载均衡策略

```java
@Configuration
public class LoadBalancerConfig {

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    public ReactorLoadBalancer<ServiceInstance> reactorServiceInstanceLoadBalancer(
            Environment environment,
            LoadBalancerClientFactory loadBalancerClientFactory) {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
        // 使用轮询算法
        return new RoundRobinLoadBalancer(
                loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class),
                name);
    }

    @Bean
    public ServiceInstanceListSupplier discoveryClientServiceInstanceListSupplier(
            ConfigurableApplicationContext context) {
        return ServiceInstanceListSupplier.builder()
                .withDiscoveryClient()
                .withHealthChecks()
                .withCaching()
                .build(context);
    }
}
```

**Spring Cloud LoadBalancer配置：**
```yaml
spring:
  cloud:
    loadbalancer:
      ribbon:
        enabled: false # 禁用Ribbon
      cache:
        enabled: true
        ttl: 35s
        capacity: 256
      health-check:
        initial-delay: 0
        interval: 25s
        path:
          default: /actuator/health
```

### 3.4 微服务治理设计

#### 3.4.1 熔断降级

**Resilience4j集成：**
```java
@Component
public class CircuitBreakerConfig {

    @Bean
    public CircuitBreaker userServiceCircuitBreaker() {
        return CircuitBreaker.ofDefaults("userService");
    }

    @Bean
    public TimeLimiter userServiceTimeLimiter() {
        return TimeLimiter.of(Duration.ofSeconds(3));
    }

    @Bean
    public Retry userServiceRetry() {
        return Retry.ofDefaults("userService");
    }
}

@Service
public class UserServiceClient {

    private final CircuitBreaker circuitBreaker;
    private final TimeLimiter timeLimiter;
    private final Retry retry;

    @CircuitBreaker(name = "userService", fallbackMethod = "fallbackGetUser")
    @TimeLimiter(name = "userService")
    @Retry(name = "userService")
    public CompletableFuture<UserDTO> getUserById(Long userId) {
        return CompletableFuture.supplyAsync(() -> {
            // 调用用户服务
            return restTemplate.getForObject("/api/v1/users/" + userId, UserDTO.class);
        });
    }

    public CompletableFuture<UserDTO> fallbackGetUser(Long userId, Exception ex) {
        // 降级逻辑：返回缓存数据或默认数据
        return CompletableFuture.completedFuture(getDefaultUser(userId));
    }
}
```

#### 3.4.2 链路追踪

**Sleuth + Zipkin集成：**
```yaml
spring:
  sleuth:
    zipkin:
      base-url: http://zipkin-server:9411
    sampler:
      probability: 1.0
  application:
    name: inventory-gateway
```

**自定义链路追踪：**
```java
@Component
public class TraceInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = UUID.randomUUID().toString();
        String spanId = UUID.randomUUID().toString();

        // 设置追踪信息
        MDC.put("traceId", traceId);
        MDC.put("spanId", spanId);

        // 添加到响应头
        response.setHeader("X-Trace-Id", traceId);
        response.setHeader("X-Span-Id", spanId);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        MDC.clear();
    }
}
```

#### 3.4.3 配置中心

**Nacos配置中心集成：**
```yaml
spring:
  cloud:
    nacos:
      config:
        server-addr: localhost:8848
        file-extension: yaml
        group: inventory-system
        namespace: dev
      discovery:
        server-addr: localhost:8848
        group: inventory-system
        namespace: dev
```

**动态配置刷新：**
```java
@Component
@RefreshScope
@ConfigurationProperties(prefix = "business")
public class BusinessConfig {

    private String apiVersion;
    private Integer maxRetryTimes;
    private Duration timeout;

    // getters and setters
}

@RestController
public class ConfigController {

    @Autowired
    private BusinessConfig businessConfig;

    @GetMapping("/config/refresh")
    public ResponseEntity<String> refreshConfig() {
        // 配置会自动刷新
        return ResponseEntity.ok("Config refreshed");
    }
}
```

#### 3.4.4 服务监控

**Micrometer + Prometheus集成：**
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.9, 0.95, 0.99
```

**自定义监控指标：**
```java
@Component
public class BusinessMetrics {

    private final Counter orderCounter;
    private final Timer orderProcessingTimer;
    private final Gauge inventoryGauge;

    public BusinessMetrics(MeterRegistry meterRegistry) {
        this.orderCounter = Counter.builder("orders.created")
            .description("Number of orders created")
            .register(meterRegistry);

        this.orderProcessingTimer = Timer.builder("order.processing.time")
            .description("Order processing time")
            .register(meterRegistry);

        this.inventoryGauge = Gauge.builder("inventory.total.value")
            .description("Total inventory value")
            .register(meterRegistry, this, BusinessMetrics::getTotalInventoryValue);
    }

    public void incrementOrderCount() {
        orderCounter.increment();
    }

    public Timer.Sample startOrderProcessing() {
        return Timer.start();
    }

    public void recordOrderProcessingTime(Timer.Sample sample) {
        sample.stop(orderProcessingTimer);
    }

    private double getTotalInventoryValue() {
        // 计算库存总价值
        return inventoryService.getTotalValue();
    }
}
```

### 3.5 用户服务 (User Service)

**技术栈：** Spring Boot 3.4.7 + Spring Security 6.x + MyBatis-Plus

**职责：** 用户认证、授权、用户信息管理

**核心功能：**
- 用户注册/登录/登出
- JWT Token生成和验证
- 用户信息CRUD操作
- 密码加密和验证

**Maven模块结构：**
```
user-service/
├── user-api/           # 对外接口定义
├── user-service-impl/  # 服务实现
└── user-client/        # Feign客户端
```

**API设计：**
```java
@RestController
@RequestMapping("/api/v1/auth")
public class AuthController {
    @PostMapping("/login")
    @PostMapping("/logout")
    @PostMapping("/refresh")
}

@RestController
@RequestMapping("/api/v1/users")
public class UserController {
    @GetMapping("/profile")
    @PutMapping("/profile")
}
```

### 3.2 商品服务 (Product Service)

**技术栈：** Spring Boot 3.4.7 + MyBatis-Plus + PostgreSQL 17

**职责：** 商品信息管理、分类管理、价格管理

**核心功能：**
- 商品CRUD操作
- 商品分类管理
- 价格策略管理
- 商品图片管理

**Maven模块结构：**
```
product-service/
├── product-api/
├── product-service-impl/
└── product-client/
```

**数据模型：**
```mermaid
erDiagram
    PRODUCT {
        uuid id PK
        string code UK
        string name
        uuid category_id FK
        string unit
        decimal cost_price
        decimal sale_price
        text description
        string status
        timestamp created_at
        timestamp updated_at
    }
    
    CATEGORY {
        uuid id PK
        string name
        uuid parent_id FK
        int level
        string path
        timestamp created_at
    }
    
    PRODUCT_IMAGE {
        uuid id PK
        uuid product_id FK
        string url
        boolean is_primary
        int sort_order
    }
    
    PRODUCT ||--|| CATEGORY : belongs_to
    PRODUCT ||--o{ PRODUCT_IMAGE : has_many
```

### 3.3 订单服务 (Order Service)

**职责：** 采购订单、销售订单管理

**核心功能：**
- 订单创建和状态管理
- 订单审批流程
- 订单履行跟踪
- 订单统计分析

**状态机设计：**
```mermaid
stateDiagram-v2
    [*] --> 草稿
    草稿 --> 待审批 : 提交审批
    草稿 --> 已取消 : 取消订单
    待审批 --> 已审批 : 审批通过
    待审批 --> 已拒绝 : 审批拒绝
    待审批 --> 草稿 : 退回修改
    已审批 --> 执行中 : 开始执行
    执行中 --> 部分完成 : 部分履行
    执行中 --> 已完成 : 完全履行
    部分完成 --> 已完成 : 完全履行
    已拒绝 --> [*]
    已取消 --> [*]
    已完成 --> [*]
```

### 3.4 库存服务 (Inventory Service)

**职责：** 库存数量管理、库存变动记录

**核心功能：**
- 实时库存查询
- 库存变动记录
- 库存预警机制
- 库存盘点管理

**库存变动类型：**
```mermaid
graph TD
    A[库存变动] --> B[入库]
    A --> C[出库]
    A --> D[调拨]
    A --> E[盘点]
    
    B --> B1[采购入库]
    B --> B2[退货入库]
    B --> B3[调拨入库]
    B --> B4[盘盈入库]
    
    C --> C1[销售出库]
    C --> C2[退货出库]
    C --> C3[调拨出库]
    C --> C4[盘亏出库]
    
    D --> D1[仓库间调拨]
    D --> D2[库位调整]
    
    E --> E1[盘点盈亏]
    E --> E2[盘点调整]
```

### 3.9 前置仓管理服务

**技术栈：** Spring Boot 3.4.7 + MyBatis-Plus 3.5.12 + Redis 7.x

**服务架构：**
```mermaid
graph TB
    subgraph "前置仓管理服务集群"
        A[前置仓管理服务]
        B[库存分配服务]
        C[智能补货服务]
        D[库存同步服务]
        E[订单履约服务]
        F[拣选管理服务]
        G[打包发货服务]
        H[配送调度服务]
        I[预测分析服务]
    end

    subgraph "数据存储"
        J[(前置仓数据库)]
        K[(Redis缓存)]
        L[(时序数据库)]
    end

    subgraph "外部系统"
        M[订单管理系统]
        N[库存管理系统]
        O[物流系统]
        P[地图服务API]
    end

    A --> J
    B --> J
    C --> J
    D --> J
    E --> J
    F --> J
    G --> J
    H --> J
    I --> L

    A --> K
    B --> K
    C --> K
    D --> K

    E --> M
    D --> N
    H --> O
    H --> P
```

**核心功能：**

1. **前置仓基础管理**
   - 前置仓信息管理
   - 覆盖区域管理
   - 仓库容量管理
   - 运营状态监控

2. **智能库存分配**
   - 多策略库存分配
   - 实时库存查询
   - 库存预警机制
   - 分配效果评估

3. **智能补货管理**
   - 需求预测算法
   - 多策略补货规则
   - 自动补货执行
   - 补货效果分析

4. **实时库存同步**
   - 双向数据同步
   - 冲突检测处理
   - 离线数据恢复
   - 一致性校验

5. **订单履约管理**
   - 智能订单分配
   - 库存可用性检查
   - 订单合并拆分
   - 履约状态跟踪

6. **拣选作业管理**
   - 拣选任务生成
   - 路径优化算法
   - 拣选进度跟踪
   - 异常处理机制

7. **打包发货管理**
   - 智能打包方案
   - 包装材料管理
   - 质量控制流程
   - 发货单据生成

8. **配送调度管理**
   - 配送路线规划
   - 配送员调度
   - 实时位置跟踪
   - 配送异常处理

**微服务拆分策略：**

```mermaid
graph TB
    subgraph "前置仓核心域"
        A[前置仓管理服务]
        B[库存分配服务]
        C[智能补货服务]
        D[库存同步服务]
    end

    subgraph "订单履约域"
        E[订单履约服务]
        F[拣选管理服务]
        G[打包发货服务]
        H[配送调度服务]
    end

    subgraph "数据分析域"
        I[预测分析服务]
        J[效果评估服务]
    end

    subgraph "基础设施域"
        K[地理位置服务]
        L[消息通知服务]
        M[文件存储服务]
    end
```

**数据库设计：**

```sql
-- 前置仓核心表
CREATE TABLE front_warehouses (
    id BIGSERIAL PRIMARY KEY,
    warehouse_code VARCHAR(50) UNIQUE NOT NULL,
    warehouse_name VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    coverage_radius DECIMAL(8,2),
    max_capacity DECIMAL(12,3),
    current_utilization DECIMAL(5,2),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    manager_id BIGINT,
    phone VARCHAR(20),
    operating_hours JSON,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 前置仓库存表
CREATE TABLE front_warehouse_inventory (
    id BIGSERIAL PRIMARY KEY,
    front_warehouse_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    available_quantity DECIMAL(10,3) DEFAULT 0,
    allocated_quantity DECIMAL(10,3) DEFAULT 0,
    in_transit_quantity DECIMAL(10,3) DEFAULT 0,
    safety_stock DECIMAL(10,3) DEFAULT 0,
    max_stock DECIMAL(10,3),
    location_code VARCHAR(50),
    last_sync_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(front_warehouse_id, product_id)
);

-- 订单分配表
CREATE TABLE front_warehouse_order_assignments (
    id BIGSERIAL PRIMARY KEY,
    assignment_number VARCHAR(50) UNIQUE NOT NULL,
    order_id BIGINT NOT NULL,
    front_warehouse_id BIGINT NOT NULL,
    assignment_strategy VARCHAR(50),
    priority INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'ASSIGNED',
    estimated_delivery_time TIMESTAMP,
    distance_km DECIMAL(8,2),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**缓存策略：**

```yaml
# Redis缓存配置
spring:
  redis:
    host: redis-cluster
    port: 6379
    database: 3
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

# 缓存策略
cache:
  front-warehouse:
    # 前置仓基础信息缓存
    warehouse-info:
      ttl: 3600  # 1小时
      key-pattern: "fw:info:{warehouseId}"

    # 库存信息缓存
    inventory:
      ttl: 300   # 5分钟
      key-pattern: "fw:inventory:{warehouseId}:{productId}"

    # 覆盖区域缓存
    coverage:
      ttl: 7200  # 2小时
      key-pattern: "fw:coverage:{warehouseId}"

    # 配送路线缓存
    delivery-route:
      ttl: 1800  # 30分钟
      key-pattern: "fw:route:{taskId}"
```

**消息队列配置：**

```yaml
# Kafka配置
spring:
  kafka:
    bootstrap-servers: kafka-cluster:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: front-warehouse-service
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer

# 主题配置
kafka:
  topics:
    # 库存同步主题
    inventory-sync: "front-warehouse.inventory.sync"
    # 订单分配主题
    order-assignment: "front-warehouse.order.assignment"
    # 拣选任务主题
    picking-task: "front-warehouse.picking.task"
    # 配送调度主题
    delivery-dispatch: "front-warehouse.delivery.dispatch"
```

## 4. 数据架构

### 4.1 数据分层

```mermaid
graph TB
    subgraph "数据应用层"
        A1[报表应用]
        A2[分析应用]
        A3[业务应用]
    end
    
    subgraph "数据服务层"
        B1[数据API]
        B2[数据缓存]
        B3[数据同步]
    end
    
    subgraph "数据存储层"
        C1[业务数据库]
        C2[数据仓库]
        C3[文档存储]
        C4[缓存存储]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> C1
    B1 --> C2
    B2 --> C4
    B3 --> C1
    B3 --> C2
```

### 4.2 数据库设计原则

**PostgreSQL 17 主数据库：**
- **Schema隔离：** 每个微服务使用独立的Schema
- **读写分离：** 主从数据库分离，提高查询性能
- **连接池管理：** HikariCP连接池优化
- **事务管理：** 分布式事务支持

**Redis 7.x 缓存设计：**
- **数据缓存：** 热点数据Redis缓存
- **会话存储：** 用户会话和JWT Token缓存
- **分布式锁：** Redis实现分布式锁
- **消息队列：** Redis Stream消息队列

**数据库Schema设计：**

| Schema名称 | 对应服务 | 主要表 |
|------------|----------|--------|
| **pisp_user** | 用户管理服务 | sys_users, sys_roles, sys_permissions, sys_departments |
| **pisp_base_data** | 基础数据服务 | products, customers, suppliers, warehouses |
| **pisp_purchase** | 采购管理服务 | purchase_orders, purchase_receipts, purchase_returns |
| **pisp_sales** | 销售管理服务 | sales_orders, sales_shipments, sales_returns |
| **pisp_inventory** | 库存管理服务 | inventories, inventory_transactions, inventory_checks |
| **pisp_finance** | 财务管理服务 | accounts_receivable, accounts_payable, cost_calculations |
| **pisp_report** | 报表分析服务 | report_configs, report_data, report_cache |
| **pisp_system** | 系统管理服务 | sys_configs, data_backups, operation_logs, sys_monitors |

**数据库连接配置：**
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: ********************************************
    username: ${DB_USERNAME:pisp_user}
    password: ${DB_PASSWORD:pisp_password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
```

### 4.3 RocketMQ消息中间件设计

**Apache RocketMQ 5.3.1 配置：**

**Topic设计：**
| Topic名称 | 队列数 | 用途 |
|----------|--------|------|
| **user_events** | 4 | 用户管理事件 |
| **base_data_events** | 4 | 基础数据变更事件 |
| **purchase_events** | 8 | 采购业务事件 |
| **sales_events** | 8 | 销售业务事件 |
| **inventory_events** | 8 | 库存变动事件 |
| **finance_events** | 4 | 财务业务事件 |
| **report_events** | 4 | 报表生成事件 |
| **system_events** | 4 | 系统管理事件 |
| **retail_events** | 8 | 零售业务事件 |

**生产者配置：**
```yaml
rocketmq:
  name-server: localhost:9876
  producer:
    group: pisp-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    max-message-size: 4194304
    compress-message-body-threshold: 4096
```

**消费者配置：**
```yaml
spring:
  kafka:
    consumer:
      group-id: ${spring.application.name}
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 500
      properties:
        spring.json.trusted.packages: "com.pisp.*.dto,com.pisp.*.event"
```

## 5. 安全架构

### 5.1 安全防护体系

```mermaid
graph TB
    subgraph "网络安全层"
        A1[防火墙]
        A2[DDoS防护]
        A3[WAF防护]
    end
    
    subgraph "应用安全层"
        B1[身份认证]
        B2[权限控制]
        B3[数据加密]
        B4[API限流]
    end
    
    subgraph "数据安全层"
        C1[数据加密]
        C2[访问控制]
        C3[审计日志]
        C4[数据备份]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    B4 --> C1
    B1 --> C2
    B2 --> C3
    B3 --> C4
```

### 5.2 认证授权机制

**认证流程：**
```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant A as 认证服务
    participant S as 业务服务
    
    C->>G: 登录请求
    G->>A: 验证用户凭证
    A->>A: 验证用户名密码
    A->>G: 返回JWT Token
    G->>C: 返回Token
    
    C->>G: 业务请求 + Token
    G->>A: 验证Token
    A->>G: 返回用户信息
    G->>S: 转发请求
    S->>G: 返回响应
    G->>C: 返回结果
```

## 6. 部署架构

### 6.1 生产环境部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        LB1[Nginx-1]
        LB2[Nginx-2]
    end

    subgraph "网关层"
        GW1[ShenYu Gateway-1]
        GW2[ShenYu Gateway-2]
    end

    subgraph "注册中心集群"
        NC1[Nacos-1]
        NC2[Nacos-2]
        NC3[Nacos-3]
    end

    subgraph "微服务集群"
        subgraph "用户服务"
            US1[pisp-user-service-1]
            US2[pisp-user-service-2]
        end

        subgraph "基础数据服务"
            BD1[pisp-base-data-service-1]
            BD2[pisp-base-data-service-2]
        end

        subgraph "业务服务"
            PS1[pisp-purchase-service-1]
            PS2[pisp-purchase-service-2]
            SS1[pisp-sales-service-1]
            SS2[pisp-sales-service-2]
            IS1[pisp-inventory-service-1]
            IS2[pisp-inventory-service-2]
        end

        subgraph "财务报表服务"
            FS1[pisp-finance-service-1]
            FS2[pisp-finance-service-2]
            RS1[pisp-report-service-1]
            RS2[pisp-report-service-2]
        end

        subgraph "前置仓服务集群"
            FW1[pisp-front-warehouse-service-1]
            FW2[pisp-front-warehouse-service-2]
            OF1[pisp-order-fulfillment-service-1]
            OF2[pisp-order-fulfillment-service-2]
            PK1[pisp-picking-service-1]
            PK2[pisp-picking-service-2]
            PC1[pisp-packing-service-1]
            PC2[pisp-packing-service-2]
            DL1[pisp-delivery-service-1]
            DL2[pisp-delivery-service-2]
        end

        subgraph "系统服务"
            SYS1[pisp-system-service-1]
            SYS2[pisp-system-service-2]
        end
    end

    subgraph "数据存储层"
        subgraph "PostgreSQL集群"
            DB1[PostgreSQL-Master]
            DB2[PostgreSQL-Slave-1]
            DB3[PostgreSQL-Slave-2]
        end

        subgraph "Redis集群"
            RD1[Redis-Master-1]
            RD2[Redis-Master-2]
            RD3[Redis-Master-3]
            RD4[Redis-Slave-1]
            RD5[Redis-Slave-2]
            RD6[Redis-Slave-3]
        end
    end

    subgraph "消息中间件"
        K1[Kafka-1]
        K2[Kafka-2]
        K3[Kafka-3]
    end

    subgraph "监控系统"
        P1[Prometheus]
        G1[Grafana]
        E1[Elasticsearch]
        L1[Logstash]
        K4[Kibana]
    end

    LB1 --> GW1
    LB1 --> GW2
    LB2 --> GW1
    LB2 --> GW2

    GW1 --> US1
    GW1 --> BD1
    GW1 --> PS1
    GW1 --> SS1
    GW1 --> IS1
    GW1 --> FS1
    GW1 --> RS1
    GW1 --> SYS1

    GW2 --> US2
    GW2 --> BD2
    GW2 --> PS2
    GW2 --> SS2
    GW2 --> IS2
    GW2 --> FS2
    GW2 --> RS2
    GW2 --> SYS2

    US1 --> NC1
    US2 --> NC2
    BD1 --> NC3
    BD2 --> NC1

    US1 --> DB1
    US2 --> DB1
    BD1 --> DB1
    BD2 --> DB1

    US1 --> RD1
    US2 --> RD2
    BD1 --> RD3
    BD2 --> RD1

    US1 --> K1
    US2 --> K2
    BD1 --> K3
    BD2 --> K1

    US1 -.-> P1
    BD1 -.-> P1
    PS1 -.-> P1
    SS1 -.-> P1
```

### 6.2 Docker容器化配置

**微服务Dockerfile示例：**
```dockerfile
# 基础镜像
FROM openjdk:21-jdk-slim

# 设置工作目录
WORKDIR /app

# 复制jar包
COPY target/pisp-user-service-1.0.0.jar app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# 暴露端口
EXPOSE 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8001/actuator/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

**容器资源配置：**
| 服务类型 | CPU限制 | 内存限制 | 副本数 |
|----------|---------|----------|--------|
| **网关服务** | 1 Core | 1GB | 2 |
| **用户服务** | 0.5 Core | 512MB | 2 |
| **基础数据服务** | 0.5 Core | 512MB | 2 |
| **业务服务** | 1 Core | 1GB | 2 |
| **财务报表服务** | 1 Core | 1GB | 2 |
| **前置仓服务** | 1.5 Core | 2GB | 2 |
| **订单履约服务** | 1 Core | 1.5GB | 2 |
| **拣选打包服务** | 0.8 Core | 1GB | 2 |
| **配送调度服务** | 1 Core | 1GB | 2 |
| **系统服务** | 0.5 Core | 512MB | 1 |

### 6.3 环境规划

**开发环境：**
- 单机部署
- 内存数据库
- 开发调试工具

**测试环境：**
- 模拟生产环境
- 完整功能测试
- 性能测试

**生产环境：**
- 高可用部署
- 负载均衡
- 监控告警

### 6.4 前置仓系统部署架构

**前置仓分布式部署：**

```mermaid
graph TB
    subgraph "中心系统"
        CS[中心管理系统]
        CDB[(中心数据库)]
        CMQ[中心消息队列]
    end

    subgraph "区域A前置仓集群"
        subgraph "前置仓A1"
            FWA1[前置仓服务A1]
            LDBA1[(本地数据库A1)]
            LRDA1[(本地Redis A1)]
        end

        subgraph "前置仓A2"
            FWA2[前置仓服务A2]
            LDBA2[(本地数据库A2)]
            LRDA2[(本地Redis A2)]
        end
    end

    subgraph "区域B前置仓集群"
        subgraph "前置仓B1"
            FWB1[前置仓服务B1]
            LDBB1[(本地数据库B1)]
            LRDB1[(本地Redis B1)]
        end

        subgraph "前置仓B2"
            FWB2[前置仓服务B2]
            LDBB2[(本地数据库B2)]
            LRDB2[(本地Redis B2)]
        end
    end

    CS -.->|数据同步| FWA1
    CS -.->|数据同步| FWA2
    CS -.->|数据同步| FWB1
    CS -.->|数据同步| FWB2

    FWA1 --> LDBA1
    FWA1 --> LRDA1
    FWA2 --> LDBA2
    FWA2 --> LRDA2
    FWB1 --> LDBB1
    FWB1 --> LRDB1
    FWB2 --> LDBB2
    FWB2 --> LRDB2
```

**前置仓Docker部署配置：**

```yaml
# docker-compose.yml for Front Warehouse
version: '3.8'

services:
  # 前置仓管理服务
  front-warehouse-service:
    image: pisp/front-warehouse-service:latest
    container_name: front-warehouse-service
    ports:
      - "8090:8090"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      - mysql
      - redis
      - kafka
    networks:
      - front-warehouse-network
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # 订单履约服务
  order-fulfillment-service:
    image: pisp/order-fulfillment-service:latest
    container_name: order-fulfillment-service
    ports:
      - "8091:8091"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - front-warehouse-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1.5G

  # 拣选管理服务
  picking-service:
    image: pisp/picking-service:latest
    container_name: picking-service
    ports:
      - "8092:8092"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    networks:
      - front-warehouse-network
    deploy:
      resources:
        limits:
          cpus: '0.8'
          memory: 1G

  # 打包发货服务
  packing-service:
    image: pisp/packing-service:latest
    container_name: packing-service
    ports:
      - "8093:8093"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    networks:
      - front-warehouse-network
    deploy:
      resources:
        limits:
          cpus: '0.8'
          memory: 1G

  # 配送调度服务
  delivery-service:
    image: pisp/delivery-service:latest
    container_name: delivery-service
    ports:
      - "8094:8094"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - MAP_API_KEY=${MAP_API_KEY}
    networks:
      - front-warehouse-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G

  # 前置仓专用MySQL
  mysql:
    image: mysql:8.0
    container_name: front-warehouse-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root123456
      - MYSQL_DATABASE=front_warehouse
      - MYSQL_USER=pisp
      - MYSQL_PASSWORD=pisp123456
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3307:3306"
    networks:
      - front-warehouse-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G

  # 前置仓专用Redis
  redis:
    image: redis:7-alpine
    container_name: front-warehouse-redis
    command: redis-server --appendonly yes --requirepass redis123456
    volumes:
      - redis_data:/data
    ports:
      - "6380:6379"
    networks:
      - front-warehouse-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G

networks:
  front-warehouse-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
```

**Kubernetes部署配置：**

```yaml
# front-warehouse-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: front-warehouse-service
  namespace: pisp-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: front-warehouse-service
  template:
    metadata:
      labels:
        app: front-warehouse-service
    spec:
      containers:
      - name: front-warehouse-service
        image: pisp/front-warehouse-service:latest
        ports:
        - containerPort: 8090
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        - name: MYSQL_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8090
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8090
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: front-warehouse-service
  namespace: pisp-system
spec:
  selector:
    app: front-warehouse-service
  ports:
  - protocol: TCP
    port: 8090
    targetPort: 8090
  type: ClusterIP
```

**前置仓边缘部署特点：**

1. **本地化部署**
   - 每个前置仓独立部署服务实例
   - 本地数据库存储核心业务数据
   - 本地缓存提升响应速度

2. **数据同步机制**
   - 定时同步：每5分钟同步一次库存数据
   - 实时同步：关键业务数据实时同步
   - 离线恢复：网络恢复后自动同步离线数据

3. **容灾备份**
   - 本地数据备份：每日自动备份
   - 异地备份：关键数据异地存储
   - 快速恢复：故障后快速恢复服务

4. **监控告警**
   - 服务健康监控
   - 业务指标监控
   - 异常情况告警

## 7. 性能优化

### 7.1 缓存策略

- **应用缓存：** Redis缓存热点数据
- **数据库缓存：** 查询结果缓存
- **CDN缓存：** 静态资源缓存
- **浏览器缓存：** 客户端缓存

### 7.2 数据库优化

- **索引优化：** 合理创建数据库索引
- **查询优化：** SQL查询语句优化
- **连接池：** 数据库连接池管理
- **分页查询：** 大数据量分页处理

## 8. 监控运维

### 8.1 监控指标

**系统监控：**
- CPU、内存、磁盘使用率
- 网络流量和延迟
- 服务可用性

**应用监控：**
- API响应时间
- 错误率统计
- 业务指标监控

**数据库监控：**
- 连接数和查询性能
- 慢查询分析
- 数据库锁等待

**前置仓业务监控：**
- 订单分配成功率和响应时间
- 拣选效率和准确率
- 打包效率和包装成本
- 配送准时率和异常率
- 库存同步延迟和冲突率
- 前置仓容量利用率
- 覆盖区域服务质量

**前置仓系统监控：**
- 各前置仓服务可用性
- 数据同步状态和延迟
- 本地缓存命中率
- 消息队列积压情况
- 边缘节点网络质量

### 8.2 告警机制

- **实时告警：** 关键指标异常实时通知
- **预警机制：** 趋势分析和预警
- **告警升级：** 多级告警和升级机制

**前置仓专用告警：**
- 前置仓服务离线告警
- 库存同步失败告警
- 订单分配异常告警
- 配送超时告警
- 库存不足预警
- 设备故障告警

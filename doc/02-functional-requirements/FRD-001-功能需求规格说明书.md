# FRD-001 进销存管理系统功能需求规格说明书

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | FRD-001 |
| 文档名称 | 功能需求规格说明书 |
| 版本号 | v1.1 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 草稿 |
| 作者 | 产品经理 |

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-25 | 初始版本创建 | 产品经理 |
| v1.1 | 2025-07-02 | 增加前置仓库存管理功能需求 | 产品经理 |

## 1. 功能概述

### 1.1 系统功能架构

```mermaid
graph TB
    A[进销存管理系统] --> B[用户管理模块]
    A --> C[基础数据管理]
    A --> D[采购管理模块]
    A --> E[销售管理模块]
    A --> F[库存管理模块]
    A --> G[前置仓管理模块]
    A --> H[零售管理模块]
    A --> I[财务管理模块]
    A --> J[报表分析模块]
    A --> K[系统管理模块]

    B --> B1[用户注册登录]
    B --> B2[角色权限管理]

    C --> C1[商品信息管理]
    C --> C2[客户信息管理]
    C --> C3[供应商管理]
    C --> C4[仓库管理]
    C --> C5[门店管理]

    D --> D1[采购订单管理]
    D --> D2[采购入库管理]
    D --> D3[采购退货管理]

    E --> E1[销售订单管理]
    E --> E2[销售出库管理]
    E --> E3[销售退货管理]

    F --> F1[库存查询]
    F --> F2[库存盘点]
    F --> F3[库存预警]
    F --> F4[库存调拨]

    G --> G1[前置仓管理]
    G --> G2[库存分配]
    G --> G3[智能补货]
    G --> G4[库存同步]
    G --> G5[订单分配]
    G --> G6[商品拣选]
    G --> G7[打包发货]
    G --> G8[配送调度]

    H --> H1[POS销售管理]
    H --> H2[会员管理]
    H --> H3[促销活动管理]
    H --> H4[门店运营管理]

    I --> I1[应收应付管理]
    I --> I2[成本核算]
    I --> I3[财务对账]

    J --> J1[销售报表]
    J --> J2[采购报表]
    J --> J3[库存报表]
    J --> J4[财务报表]

    K --> K1[系统配置]
    K --> K2[数据备份]
    K --> K3[操作日志]
```

## 2. 详细功能需求

### 2.1 用户管理模块 (UM)

#### UM-001 用户注册与登录

**功能描述：** 提供用户注册、登录、密码管理等基础功能

**输入：** 用户名、密码、邮箱、手机号
**输出：** 登录状态、用户信息
**处理逻辑：**
1. 用户输入登录凭证
2. 系统验证用户身份
3. 生成访问令牌
4. 返回用户信息和权限

**验收标准：**
- 支持用户名/邮箱/手机号登录
- 密码强度验证
- 登录失败锁定机制
- 支持找回密码功能

#### UM-002 角色权限管理

**功能描述：** 管理用户角色和权限分配

**角色定义：**
- 系统管理员：全部权限
- 企业管理者：查看所有数据，管理企业配置
- 采购经理：采购模块全部权限
- 销售经理：销售模块全部权限
- 仓库管理员：库存模块权限
- 财务人员：财务模块权限
- 普通员工：基础查询权限

### 2.2 基础数据管理模块 (BDM)

#### BDM-001 商品信息管理

**功能描述：** 管理商品基础信息

**数据结构：**
```mermaid
erDiagram
    PRODUCT {
        string product_id PK
        string product_name
        string product_code
        string category_id FK
        string unit
        decimal cost_price
        decimal sale_price
        string description
        string status
        datetime created_at
        datetime updated_at
    }
    
    CATEGORY {
        string category_id PK
        string category_name
        string parent_id FK
        int level
        string description
    }
    
    PRODUCT ||--|| CATEGORY : belongs_to
```

**功能要求：**
- 支持商品分类管理（多级分类）
- 商品编码自动生成或手动输入
- 支持商品图片上传
- 批量导入导出功能
- 商品状态管理（启用/停用）

#### BDM-002 客户信息管理

**功能描述：** 管理客户基础信息和信用管理

**主要功能：**
- 客户基本信息维护
- 客户分类管理
- 信用额度设置
- 客户联系人管理
- 客户交易历史查询

#### BDM-003 供应商管理

**功能描述：** 管理供应商信息和合作关系

**主要功能：**
- 供应商基本信息维护
- 供应商分类和评级
- 合作协议管理
- 供应商商品价格管理
- 供应商绩效评估

### 2.3 采购管理模块 (PM)

#### PM-001 采购订单管理

**功能描述：** 管理采购订单全生命周期

**业务流程：**
```mermaid
flowchart TD
    A[创建采购需求] --> B[选择供应商]
    B --> C[创建采购订单]
    C --> D[订单审批]
    D --> E{审批通过?}
    E -->|是| F[发送订单给供应商]
    E -->|否| G[修改订单]
    G --> D
    F --> H[供应商确认]
    H --> I[等待收货]
    I --> J[采购入库]
    J --> K[订单完成]
```

**功能要求：**
- 支持采购需求计划
- 多供应商询价比价
- 采购订单审批流程
- 订单状态跟踪
- 采购合同管理

#### PM-002 采购入库管理

**功能描述：** 管理采购商品入库流程

**主要功能：**
- 收货单创建和确认
- 质量检验记录
- 入库单生成
- 库存数量更新
- 采购成本核算

### 2.4 销售管理模块 (SM)

#### SM-001 销售订单管理

**功能描述：** 管理销售订单全流程

**业务流程：**
```mermaid
flowchart TD
    A[客户询价] --> B[创建报价单]
    B --> C[客户确认]
    C --> D[创建销售订单]
    D --> E[库存检查]
    E --> F{库存充足?}
    F -->|是| G[订单确认]
    F -->|否| H[采购补货]
    H --> G
    G --> I[安排发货]
    I --> J[销售出库]
    J --> K[物流配送]
    K --> L[客户确认收货]
    L --> M[订单完成]
```

**功能要求：**
- 销售报价管理
- 订单信用检查
- 库存可用性检查
- 订单优先级管理
- 发货计划安排

### 2.5 库存管理模块 (IM)

#### IM-001 库存查询与监控

**功能描述：** 实时查询和监控库存状态

**主要功能：**
- 实时库存查询
- 多维度库存统计
- 库存变动历史
- 库存预警设置
- 安全库存管理

#### IM-002 库存盘点

**功能描述：** 定期库存盘点和差异处理

**盘点流程：**
```mermaid
flowchart TD
    A[制定盘点计划] --> B[生成盘点单]
    B --> C[执行盘点]
    C --> D[录入盘点结果]
    D --> E[差异分析]
    E --> F{存在差异?}
    F -->|是| G[差异调查]
    F -->|否| H[盘点完成]
    G --> I[差异处理]
    I --> J[库存调整]
    J --> H
```

### 2.6 前置仓管理模块 (FM)

#### FM-001 前置仓基础管理

**功能描述：** 管理前置仓的基础信息和运营状态

**主要功能：**
- 前置仓信息维护（位置、容量、覆盖范围等）
- 前置仓状态管理（运营中、维护中、停用等）
- 前置仓员工分配和权限管理
- 前置仓设备管理

**数据结构：**
```mermaid
erDiagram
    FRONT_WAREHOUSES {
        uuid id PK
        string warehouse_code UK
        string warehouse_name
        string address
        decimal latitude
        decimal longitude
        decimal coverage_radius
        integer capacity
        enum status
        uuid manager_id FK
        string phone
        json operating_hours
        datetime created_at
        datetime updated_at
    }

    FRONT_WAREHOUSE_COVERAGE {
        uuid id PK
        uuid front_warehouse_id FK
        string region_code
        string region_name
        boolean is_active
        datetime created_at
    }

    FRONT_WAREHOUSES ||--o{ FRONT_WAREHOUSE_COVERAGE : covers
```

**验收标准：**
- 支持前置仓地理位置可视化展示
- 覆盖范围自动计算和展示
- 前置仓状态实时监控
- 支持批量导入前置仓信息

#### FM-002 库存分配管理

**功能描述：** 智能分配库存到各个前置仓

**主要功能：**
- 基于历史销售数据的智能分配算法
- 手动调整分配策略
- 分配执行监控和异常处理
- 分配效果分析和优化

**业务流程：**
```mermaid
flowchart TD
    A[分析销售数据] --> B[计算需求预测]
    B --> C[生成分配方案]
    C --> D[方案审核]
    D --> E{审核通过?}
    E -->|是| F[执行分配]
    E -->|否| G[调整方案]
    G --> D
    F --> H[监控执行]
    H --> I[更新库存]
    I --> J[分配完成]
```

**验收标准：**
- 分配算法准确性达到95%以上
- 支持多种分配策略（按销量、按季节、按促销等）
- 分配执行响应时间 < 10秒
- 提供分配效果分析报表

#### FM-003 智能补货管理

**功能描述：** 自动监控前置仓库存并触发补货

**主要功能：**
- 库存水平实时监控
- 智能补货策略配置
- 自动补货订单生成
- 补货效果跟踪和优化

**补货策略：**
- **定期补货**：按固定周期补货
- **定量补货**：库存低于安全库存时补货
- **预测补货**：基于销售预测的主动补货
- **紧急补货**：库存紧急情况下的快速补货

**业务流程：**
```mermaid
flowchart TD
    A[监控库存水平] --> B{低于安全库存?}
    B -->|是| C[计算补货数量]
    B -->|否| A
    C --> D[选择补货来源]
    D --> E[生成补货订单]
    E --> F[发送补货请求]
    F --> G[跟踪补货状态]
    G --> H[更新库存]
    H --> I[补货完成]
```

**验收标准：**
- 库存监控实时性 < 1分钟
- 补货预测准确性达到90%以上
- 支持多级补货（中心仓→前置仓→门店）
- 补货订单自动生成和跟踪

#### FM-004 库存同步管理

**功能描述：** 确保前置仓库存数据的实时同步和一致性

**主要功能：**
- 实时库存同步机制
- 离线模式支持
- 数据冲突检测和处理
- 库存一致性校验

**同步机制：**
```mermaid
sequenceDiagram
    participant FW as 前置仓
    participant MQ as 消息队列
    participant CS as 中心系统
    participant DB as 数据库

    FW->>MQ: 库存变动事件
    MQ->>CS: 转发事件
    CS->>DB: 更新库存
    DB-->>CS: 确认更新
    CS->>MQ: 同步确认
    MQ->>FW: 同步完成

    Note over FW,DB: 网络异常处理
    FW->>FW: 本地缓存变动
    FW->>MQ: 网络恢复后批量同步
```

**验收标准：**
- 库存同步延迟 < 1秒
- 支持离线模式运行
- 数据冲突自动处理率 > 95%
- 库存一致性检查准确性 99.9%

#### FM-005 订单分配管理

**功能描述：** 智能分配订单到最优前置仓进行履约

**主要功能：**
- 订单智能路由分配
- 库存可用性实时检查
- 配送距离和时效优化
- 订单合并和拆分处理

**分配策略：**
- **距离优先**：选择距离客户最近的前置仓
- **库存优先**：选择库存最充足的前置仓
- **负载均衡**：平衡各前置仓的订单负载
- **时效优先**：选择能最快配送的前置仓

**业务流程：**
```mermaid
flowchart TD
    A[接收订单] --> B[检查商品库存]
    B --> C[计算配送距离]
    C --> D[评估前置仓负载]
    D --> E[应用分配策略]
    E --> F[生成分配方案]
    F --> G{库存充足?}
    G -->|是| H[确认分配]
    G -->|否| I[寻找替代方案]
    I --> J{找到替代?}
    J -->|是| H
    J -->|否| K[订单拆分]
    K --> L[多仓分配]
    L --> H
    H --> M[发送到前置仓]
```

**验收标准：**
- 订单分配响应时间 < 3秒
- 分配准确率 > 98%
- 支持订单实时重新分配
- 库存不足时自动寻找替代方案

#### FM-006 商品拣选管理

**功能描述：** 管理前置仓的商品拣选作业流程

**主要功能：**
- 拣选任务生成和分配
- 拣选路径优化
- 拣选进度实时跟踪
- 拣选异常处理

**拣选策略：**
- **批次拣选**：多个订单合并拣选
- **波次拣选**：按时间段批量拣选
- **区域拣选**：按存储区域分组拣选
- **优先级拣选**：紧急订单优先处理

**业务流程：**
```mermaid
flowchart TD
    A[接收订单] --> B[生成拣选任务]
    B --> C[优化拣选路径]
    C --> D[分配拣选员]
    D --> E[开始拣选]
    E --> F[扫码确认商品]
    F --> G{商品正确?}
    G -->|是| H[更新拣选进度]
    G -->|否| I[异常处理]
    I --> J[记录异常信息]
    J --> K[寻找替代商品]
    K --> L{找到替代?}
    L -->|是| H
    L -->|否| M[标记缺货]
    H --> N{拣选完成?}
    N -->|否| E
    N -->|是| O[提交拣选结果]
```

**验收标准：**
- 拣选路径优化节省时间 > 20%
- 拣选准确率 > 99.5%
- 支持移动端拣选操作
- 异常处理响应时间 < 30秒

#### FM-007 打包发货管理

**功能描述：** 管理商品打包和发货的全流程

**主要功能：**
- 智能打包方案推荐
- 包装材料管理
- 打包质量控制
- 发货单据生成

**打包策略：**
- **体积优化**：最小化包装体积
- **重量优化**：合理分配包装重量
- **保护优化**：确保商品运输安全
- **成本优化**：降低包装材料成本

**业务流程：**
```mermaid
flowchart TD
    A[接收拣选商品] --> B[商品质检]
    B --> C{质检通过?}
    C -->|否| D[退回重新拣选]
    C -->|是| E[计算包装方案]
    E --> F[选择包装材料]
    F --> G[开始打包]
    G --> H[放入商品]
    H --> I[添加保护材料]
    I --> J[封装包裹]
    J --> K[贴标签]
    K --> L[称重检查]
    L --> M{重量正确?}
    M -->|否| N[重新打包]
    M -->|是| O[生成发货单]
    N --> G
    O --> P[移交配送]
```

**验收标准：**
- 打包效率 > 60包/小时/人
- 包装材料利用率 > 85%
- 商品破损率 < 0.1%
- 支持多种包装规格

#### FM-008 配送调度管理

**功能描述：** 优化配送路线和调度配送资源

**主要功能：**
- 配送路线规划
- 配送员调度管理
- 配送进度跟踪
- 配送异常处理

**调度策略：**
- **距离优化**：最短路径配送
- **时间窗口**：按客户要求时间配送
- **容量约束**：考虑配送员负载能力
- **动态调整**：实时调整配送计划

**业务流程：**
```mermaid
flowchart TD
    A[接收发货包裹] --> B[分析配送地址]
    B --> C[规划配送路线]
    C --> D[分配配送员]
    D --> E[生成配送任务]
    E --> F[配送员接单]
    F --> G[开始配送]
    G --> H[实时位置跟踪]
    H --> I[到达客户地址]
    I --> J[确认收货]
    J --> K{配送成功?}
    K -->|是| L[完成配送]
    K -->|否| M[异常处理]
    M --> N[重新安排配送]
    L --> O[更新订单状态]
```

**验收标准：**
- 配送路线优化节省时间 > 15%
- 准时配送率 > 95%
- 配送异常处理时间 < 10分钟
- 支持实时配送跟踪

### 2.7 零售管理模块 (RM)

#### RM-001 POS销售管理

**功能描述：** 提供门店POS销售功能

**主要功能：**
- 商品扫码销售
- 多种支付方式支持
- 小票打印和电子发票
- 销售退货处理
- 实时库存扣减

**业务流程：**
```mermaid
graph LR
    A[商品扫码] --> B[计算金额]
    B --> C[应用促销]
    C --> D[选择支付方式]
    D --> E[完成支付]
    E --> F[打印小票]
    F --> G[更新库存]
    G --> H[同步数据]
```

**验收标准：**
- 支持条码扫描和手动输入
- 支持现金、银行卡、移动支付
- POS响应时间 < 500ms
- 支持离线模式

#### RM-002 会员管理

**功能描述：** 管理会员信息和权益

**主要功能：**
- 会员注册和信息管理
- 会员等级和权益设置
- 积分累积和兑换
- 会员标签和分群
- 会员消费分析

**业务流程：**
```mermaid
graph LR
    A[会员注册] --> B[等级评定]
    B --> C[权益分配]
    C --> D[消费积分]
    D --> E[等级升级]
    E --> F[权益享受]
```

**验收标准：**
- 支持手机号快速注册
- 会员查询响应时间 < 100ms
- 积分计算准确性 99.9%
- 支持会员标签管理

#### RM-003 促销活动管理

**功能描述：** 创建和管理各类促销活动

**主要功能：**
- 促销活动创建和配置
- 促销规则引擎
- 优惠券管理
- 促销效果监控
- 促销数据分析

**促销类型：**
- 满减促销（满100减20）
- 折扣促销（8折优惠）
- 买赠促销（买2送1）
- 会员专享促销
- 限时促销

**验收标准：**
- 支持多种促销类型
- 促销计算响应时间 < 200ms
- 促销规则准确性 99.9%
- 支持促销效果分析

#### RM-004 门店运营管理

**功能描述：** 管理门店运营相关功能

**主要功能：**
- 门店基础信息管理
- 门店库存管理
- 门店销售统计
- 门店员工管理
- 门店业绩分析

**业务流程：**
```mermaid
graph LR
    A[门店设置] --> B[员工分配]
    B --> C[库存配置]
    C --> D[销售运营]
    D --> E[业绩统计]
    E --> F[分析报告]
```

**验收标准：**
- 支持多门店管理
- 门店数据实时同步
- 支持门店间库存调拨
- 提供门店业绩对比

## 3. 非功能性需求

### 3.1 性能需求

**ERP系统性能：**
- **响应时间：** 页面加载时间 < 3秒
- **并发用户：** 支持1000+用户同时在线
- **数据处理：** 支持百万级商品数据
- **报表生成：** 复杂报表生成时间 < 30秒

**零售系统性能：**
- **POS响应时间：** < 500ms
- **会员查询时间：** < 100ms
- **促销计算时间：** < 200ms
- **单店并发交易：** > 100笔/分钟
- **全系统并发会员查询：** > 5000次/分钟

**前置仓系统性能：**
- **库存同步延迟：** < 1秒
- **补货计算响应时间：** < 5秒
- **库存分配计算时间：** < 10秒
- **单个前置仓SKU管理：** > 1000个
- **并发前置仓操作：** > 100个
- **库存查询响应时间：** < 200ms

**订单履约系统性能：**
- **订单分配响应时间：** < 3秒
- **拣选路径优化时间：** < 2秒
- **打包方案计算时间：** < 1秒
- **配送路线优化时间：** < 5秒
- **单个前置仓并发订单处理：** > 200个
- **拣选效率：** > 60件/小时/人
- **打包效率：** > 40包/小时/人

### 3.2 可用性需求

**系统整体可用性：**
- **ERP系统可用性：** 99.9%以上
- **POS系统可用性：** 99.95%以上
- **故障恢复：** 系统故障恢复时间 < 1小时
- **数据备份：** 每日自动备份
- **容灾机制：** 支持异地容灾

**零售业务可用性：**
- **离线POS支持：** 支持网络断开时的离线销售
- **数据同步：** 网络恢复后自动同步数据
- **支付容错：** 支付失败时的重试机制
- **会员服务：** 会员查询服务高可用

**前置仓业务可用性：**
- **前置仓系统可用性：** 99.9%以上
- **离线模式支持：** 支持网络中断时的离线操作
- **数据同步保证：** 网络恢复后自动同步，数据不丢失
- **故障自动恢复：** 系统故障自动切换和恢复
- **库存一致性保证：** 确保分布式库存数据一致性

### 3.3 安全性需求

**基础安全要求：**
- **数据加密：** 敏感数据传输和存储加密
- **访问控制：** 基于角色的权限控制
- **审计日志：** 完整的操作日志记录
- **数据隔离：** 多租户数据隔离

**高危操作安全要求：**
- **物理删除策略：** 系统采用物理删除，不使用逻辑删除
- **二次确认机制：** 高危操作必须经过二次确认
- **操作前备份：** 删除操作前自动备份数据
- **权限分级控制：** 不同风险等级的操作需要不同权限
- **密码验证：** 高风险操作需要输入当前用户密码
- **管理员授权：** 极高风险操作需要管理员授权
- **操作审计：** 所有高危操作记录详细审计日志
- **实时告警：** 异常操作模式实时告警

**高危操作分类：**
- **CRITICAL级别：** 用户删除、批量删除、系统重置、权限变更
- **HIGH级别：** 单条记录删除、订单取消、价格调整
- **MEDIUM级别：** 状态变更、数据修改
- **LOW级别：** 查询、导出、查看操作

**数据恢复保障：**
- **操作前备份：** 删除前自动备份原始数据
- **备份保留期：** 根据数据重要性设置不同保留期（90天-7年）
- **快速恢复：** 支持单条记录和批量数据恢复
- **恢复验证：** 数据恢复后进行完整性验证

### 3.4 兼容性需求

- **浏览器支持：** Chrome、Firefox、Safari、Edge
- **移动端支持：** 响应式设计，支持手机和平板
- **操作系统：** 跨平台部署支持
- **数据库：** 支持主流关系型数据库

## 4. 接口需求

### 4.1 外部系统接口

**传统ERP接口：**
- **财务系统接口：** 与企业现有财务系统对接
- **电商平台接口：** 与主流电商平台订单同步
- **物流系统接口：** 与物流公司系统对接
- **银行接口：** 支付和对账接口

**零售业务接口：**
- **支付平台接口：** 微信支付、支付宝、银联等
- **电子发票接口：** 税务局电子发票系统
- **短信服务接口：** 会员通知和营销短信
- **第三方会员系统：** 与现有会员系统对接
- **POS硬件接口：** 扫码枪、打印机、收银机等

### 4.2 API接口规范

- **协议：** RESTful API
- **数据格式：** JSON
- **认证方式：** JWT Token
- **版本控制：** URL版本控制

## 5. 数据需求

### 5.1 数据量估算

**传统ERP数据：**
- **商品数据：** 10万+ SKU
- **客户数据：** 1万+ 客户
- **订单数据：** 100万+ 订单/年
- **库存记录：** 1000万+ 记录/年

**零售业务数据：**
- **会员数据：** 50万+ 会员
- **POS交易数据：** 500万+ 交易/年
- **促销活动数据：** 1000+ 活动/年
- **门店数据：** 100+ 门店
- **积分记录：** 2000万+ 记录/年

**前置仓业务数据：**
- **前置仓数据：** 200+ 前置仓
- **库存分配记录：** 5000万+ 记录/年
- **补货订单数据：** 100万+ 订单/年
- **库存同步记录：** 1亿+ 记录/年
- **前置仓库存数据：** 20万+ SKU×前置仓数量
- **覆盖区域数据：** 2000+ 区域

**订单履约业务数据：**
- **订单分配记录：** 2000万+ 记录/年
- **拣选任务数据：** 1000万+ 任务/年
- **拣选明细记录：** 5000万+ 记录/年
- **打包任务数据：** 1000万+ 任务/年
- **包裹信息数据：** 1200万+ 包裹/年
- **配送任务数据：** 800万+ 任务/年
- **配送轨迹数据：** 2亿+ 位置点/年

### 5.2 数据保留策略

- **交易数据：** 永久保留
- **日志数据：** 保留2年
- **临时数据：** 保留30天
- **备份数据：** 保留5年

# MAD-001 监控告警体系完善

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | MAD-001 |
| 文档名称 | 监控告警体系完善 |
| 版本号 | v1.0 |
| 创建日期 | 2025-07-02 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 草稿 |
| 作者 | 系统架构师 |

## 1. 监控体系概述

### 1.1 完善目标

为PISP系统新增的微服务（前置仓管理、订单履约等）建立完善的监控告警体系，确保系统的可观测性、可靠性和运维效率。

### 1.2 监控架构

```mermaid
graph TB
    subgraph "应用层监控"
        A1[前置仓管理服务]
        A2[订单履约服务]
        A3[拣选管理服务]
        A4[打包发货服务]
        A5[配送调度服务]
    end

    subgraph "指标收集层"
        B1[Micrometer]
        B2[Spring Boot Actuator]
        B3[Custom Metrics]
    end

    subgraph "监控存储层"
        C1[Prometheus]
        C2[InfluxDB]
        C3[Elasticsearch]
    end

    subgraph "可视化层"
        D1[Grafana Dashboard]
        D2[Kibana]
        D3[Custom UI]
    end

    subgraph "告警层"
        E1[AlertManager]
        E2[Webhook]
        E3[Email/SMS/Slack]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> C1
    B2 --> C1
    B3 --> C1

    C1 --> D1
    C2 --> D1
    C3 --> D2

    C1 --> E1
    E1 --> E2
    E2 --> E3
```

## 2. 新增微服务监控指标

### 2.1 前置仓管理服务监控

**核心业务指标：**

```java
@Component
public class FrontWarehouseMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // 前置仓基础指标
    private final Gauge activeFrontWarehousesGauge;
    private final Counter warehouseOperationsCounter;
    private final Timer warehouseOperationTimer;
    
    // 库存管理指标
    private final Counter inventoryAllocationCounter;
    private final Timer inventoryAllocationTimer;
    private final Gauge lowStockWarehouseCount;
    private final Counter inventorySyncCounter;
    
    // 智能补货指标
    private final Counter replenishmentRequestCounter;
    private final Timer replenishmentProcessingTimer;
    private final Gauge pendingReplenishmentCount;
    
    public FrontWarehouseMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // 初始化指标
        this.activeFrontWarehousesGauge = Gauge.builder("front_warehouse.active.count")
            .description("活跃前置仓数量")
            .register(meterRegistry, this, FrontWarehouseMetrics::getActiveFrontWarehouseCount);
            
        this.warehouseOperationsCounter = Counter.builder("front_warehouse.operations.total")
            .description("前置仓操作总数")
            .register(meterRegistry);
            
        this.warehouseOperationTimer = Timer.builder("front_warehouse.operation.duration")
            .description("前置仓操作耗时")
            .register(meterRegistry);
            
        this.inventoryAllocationCounter = Counter.builder("inventory.allocation.total")
            .description("库存分配总数")
            .register(meterRegistry);
            
        this.inventoryAllocationTimer = Timer.builder("inventory.allocation.duration")
            .description("库存分配耗时")
            .register(meterRegistry);
            
        this.lowStockWarehouseCount = Gauge.builder("warehouse.low_stock.count")
            .description("低库存前置仓数量")
            .register(meterRegistry, this, FrontWarehouseMetrics::getLowStockWarehouseCount);
            
        this.inventorySyncCounter = Counter.builder("inventory.sync.total")
            .description("库存同步总数")
            .register(meterRegistry);
            
        this.replenishmentRequestCounter = Counter.builder("replenishment.request.total")
            .description("补货请求总数")
            .register(meterRegistry);
            
        this.replenishmentProcessingTimer = Timer.builder("replenishment.processing.duration")
            .description("补货处理耗时")
            .register(meterRegistry);
            
        this.pendingReplenishmentCount = Gauge.builder("replenishment.pending.count")
            .description("待处理补货数量")
            .register(meterRegistry, this, FrontWarehouseMetrics::getPendingReplenishmentCount);
    }
    
    // 指标记录方法
    public void recordWarehouseOperation(String operationType, boolean success) {
        warehouseOperationsCounter.increment(
            Tags.of(
                "operation_type", operationType,
                "status", success ? "SUCCESS" : "FAILURE"
            )
        );
    }
    
    public void recordInventoryAllocation(String warehouseId, boolean success) {
        inventoryAllocationCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "status", success ? "SUCCESS" : "FAILURE"
            )
        );
    }
    
    public void recordInventorySync(String warehouseId, String syncType, boolean success) {
        inventorySyncCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "sync_type", syncType,
                "status", success ? "SUCCESS" : "FAILURE"
            )
        );
    }
    
    public void recordReplenishmentRequest(String warehouseId, String requestType) {
        replenishmentRequestCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "request_type", requestType
            )
        );
    }
    
    // 指标计算方法
    private double getActiveFrontWarehouseCount() {
        return frontWarehouseService.getActiveFrontWarehouseCount();
    }
    
    private double getLowStockWarehouseCount() {
        return frontWarehouseService.getLowStockWarehouseCount();
    }
    
    private double getPendingReplenishmentCount() {
        return replenishmentService.getPendingReplenishmentCount();
    }
}
```

### 2.2 订单履约服务监控

**核心业务指标：**

```java
@Component
public class OrderFulfillmentMetrics {
    
    private final MeterRegistry meterRegistry;
    
    // 订单分配指标
    private final Counter orderAllocationCounter;
    private final Timer orderAllocationTimer;
    private final Gauge pendingOrdersGauge;
    
    // 拣选管理指标
    private final Counter pickingTaskCounter;
    private final Timer pickingTaskTimer;
    private final Gauge activePickingTasksGauge;
    
    // 打包发货指标
    private final Counter packingTaskCounter;
    private final Timer packingTaskTimer;
    private final Counter shippingCounter;
    
    // 配送调度指标
    private final Counter deliveryTaskCounter;
    private final Timer deliverySchedulingTimer;
    private final Gauge activeDeliveryTasksGauge;
    
    public OrderFulfillmentMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // 初始化指标
        this.orderAllocationCounter = Counter.builder("order.allocation.total")
            .description("订单分配总数")
            .register(meterRegistry);
            
        this.orderAllocationTimer = Timer.builder("order.allocation.duration")
            .description("订单分配耗时")
            .register(meterRegistry);
            
        this.pendingOrdersGauge = Gauge.builder("orders.pending.count")
            .description("待处理订单数量")
            .register(meterRegistry, this, OrderFulfillmentMetrics::getPendingOrderCount);
            
        this.pickingTaskCounter = Counter.builder("picking.task.total")
            .description("拣选任务总数")
            .register(meterRegistry);
            
        this.pickingTaskTimer = Timer.builder("picking.task.duration")
            .description("拣选任务耗时")
            .register(meterRegistry);
            
        this.activePickingTasksGauge = Gauge.builder("picking.tasks.active.count")
            .description("活跃拣选任务数量")
            .register(meterRegistry, this, OrderFulfillmentMetrics::getActivePickingTaskCount);
            
        this.packingTaskCounter = Counter.builder("packing.task.total")
            .description("打包任务总数")
            .register(meterRegistry);
            
        this.packingTaskTimer = Timer.builder("packing.task.duration")
            .description("打包任务耗时")
            .register(meterRegistry);
            
        this.shippingCounter = Counter.builder("shipping.total")
            .description("发货总数")
            .register(meterRegistry);
            
        this.deliveryTaskCounter = Counter.builder("delivery.task.total")
            .description("配送任务总数")
            .register(meterRegistry);
            
        this.deliverySchedulingTimer = Timer.builder("delivery.scheduling.duration")
            .description("配送调度耗时")
            .register(meterRegistry);
            
        this.activeDeliveryTasksGauge = Gauge.builder("delivery.tasks.active.count")
            .description("活跃配送任务数量")
            .register(meterRegistry, this, OrderFulfillmentMetrics::getActiveDeliveryTaskCount);
    }
    
    // 指标记录方法
    public void recordOrderAllocation(String warehouseId, boolean success) {
        orderAllocationCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "status", success ? "SUCCESS" : "FAILURE"
            )
        );
    }
    
    public void recordPickingTask(String warehouseId, String taskType, boolean success) {
        pickingTaskCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "task_type", taskType,
                "status", success ? "SUCCESS" : "FAILURE"
            )
        );
    }
    
    public void recordPackingTask(String warehouseId, boolean success) {
        packingTaskCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "status", success ? "SUCCESS" : "FAILURE"
            )
        );
    }
    
    public void recordShipping(String warehouseId, String shippingMethod) {
        shippingCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "shipping_method", shippingMethod
            )
        );
    }
    
    public void recordDeliveryTask(String warehouseId, String deliveryType, boolean success) {
        deliveryTaskCounter.increment(
            Tags.of(
                "warehouse_id", warehouseId,
                "delivery_type", deliveryType,
                "status", success ? "SUCCESS" : "FAILURE"
            )
        );
    }
    
    // 指标计算方法
    private double getPendingOrderCount() {
        return orderService.getPendingOrderCount();
    }
    
    private double getActivePickingTaskCount() {
        return pickingService.getActivePickingTaskCount();
    }
    
    private double getActiveDeliveryTaskCount() {
        return deliveryService.getActiveDeliveryTaskCount();
    }
}
```

## 3. 完善告警规则配置

### 3.1 前置仓管理告警规则

```yaml
# prometheus-alerts-front-warehouse.yml
groups:
  - name: front_warehouse_alerts
    rules:
      # 前置仓可用性告警
      - alert: FrontWarehouseDown
        expr: up{job="front-warehouse-service"} == 0
        for: 1m
        labels:
          severity: critical
          team: front-warehouse
        annotations:
          summary: "前置仓管理服务不可用"
          description: "前置仓管理服务 {{ $labels.instance }} 已停止响应超过1分钟"
        actions:
          - type: "SMS"
            recipients: ["+86138****1234"]
          - type: "EMAIL"
            recipients: ["<EMAIL>"]

      # 库存分配失败率告警
      - alert: HighInventoryAllocationFailureRate
        expr: rate(inventory_allocation_total{status="FAILURE"}[5m]) / rate(inventory_allocation_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          team: front-warehouse
        annotations:
          summary: "库存分配失败率过高"
          description: "过去5分钟内库存分配失败率为 {{ $value | humanizePercentage }}，超过10%阈值"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]
          - type: "SLACK"
            channel: "#inventory-alerts"

      # 低库存前置仓数量告警
      - alert: TooManyLowStockWarehouses
        expr: warehouse_low_stock_count > 5
        for: 1m
        labels:
          severity: warning
          team: front-warehouse
        annotations:
          summary: "低库存前置仓数量过多"
          description: "当前有 {{ $value }} 个前置仓库存不足，需要及时补货"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]

      # 补货处理延迟告警
      - alert: ReplenishmentProcessingDelay
        expr: histogram_quantile(0.95, rate(replenishment_processing_duration_bucket[5m])) > 300
        for: 5m
        labels:
          severity: critical
          team: front-warehouse
        annotations:
          summary: "补货处理延迟过高"
          description: "95%的补货处理时间超过5分钟，当前P95延迟为 {{ $value }}秒"
        actions:
          - type: "SMS"
            recipients: ["+86138****1234"]
          - type: "ESCALATION"
            escalation-policy: "replenishment-escalation"

      # 库存同步失败告警
      - alert: InventorySyncFailureRate
        expr: rate(inventory_sync_total{status="FAILURE"}[10m]) / rate(inventory_sync_total[10m]) > 0.05
        for: 3m
        labels:
          severity: high
          team: front-warehouse
        annotations:
          summary: "库存同步失败率过高"
          description: "过去10分钟内库存同步失败率为 {{ $value | humanizePercentage }}，超过5%阈值"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]
          - type: "WEBHOOK"
            url: "http://alert-manager/webhook/inventory-sync"

      # 前置仓操作频率异常告警
      - alert: FrontWarehouseOperationSpike
        expr: rate(front_warehouse_operations_total[5m]) > 100
        for: 2m
        labels:
          severity: warning
          team: front-warehouse
        annotations:
          summary: "前置仓操作频率异常"
          description: "过去5分钟内前置仓操作频率为 {{ $value }} 次/分钟，可能存在异常"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]
```

### 3.2 订单履约告警规则

```yaml
# prometheus-alerts-order-fulfillment.yml
groups:
  - name: order_fulfillment_alerts
    rules:
      # 订单分配延迟告警
      - alert: OrderAllocationLatencyHigh
        expr: histogram_quantile(0.95, rate(order_allocation_duration_bucket[5m])) > 30
        for: 3m
        labels:
          severity: warning
          team: fulfillment
        annotations:
          summary: "订单分配延迟过高"
          description: "95%的订单分配时间超过30秒，当前P95延迟为 {{ $value }}秒"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]
          - type: "SLACK"
            channel: "#fulfillment-alerts"

      # 待处理订单积压告警
      - alert: PendingOrdersBacklog
        expr: orders_pending_count > 1000
        for: 5m
        labels:
          severity: critical
          team: fulfillment
        annotations:
          summary: "待处理订单积压严重"
          description: "当前待处理订单数量为 {{ $value }}，超过1000个阈值"
        actions:
          - type: "SMS"
            recipients: ["+86138****1234"]
          - type: "ESCALATION"
            escalation-policy: "order-backlog-escalation"

      # 拣选任务失败率告警
      - alert: PickingTaskFailureRate
        expr: rate(picking_task_total{status="FAILURE"}[10m]) / rate(picking_task_total[10m]) > 0.05
        for: 3m
        labels:
          severity: high
          team: fulfillment
        annotations:
          summary: "拣选任务失败率过高"
          description: "过去10分钟内拣选任务失败率为 {{ $value | humanizePercentage }}，超过5%阈值"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]

      # 拣选任务处理时间告警
      - alert: PickingTaskProcessingDelay
        expr: histogram_quantile(0.90, rate(picking_task_duration_bucket[5m])) > 1800
        for: 5m
        labels:
          severity: warning
          team: fulfillment
        annotations:
          summary: "拣选任务处理时间过长"
          description: "90%的拣选任务处理时间超过30分钟，当前P90延迟为 {{ $value }}秒"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]

      # 打包任务积压告警
      - alert: PackingTaskBacklog
        expr: packing_tasks_active_count > 500
        for: 3m
        labels:
          severity: warning
          team: fulfillment
        annotations:
          summary: "打包任务积压"
          description: "当前活跃打包任务数量为 {{ $value }}，超过500个阈值"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]

      # 配送调度延迟告警
      - alert: DeliverySchedulingDelay
        expr: histogram_quantile(0.95, rate(delivery_scheduling_duration_bucket[5m])) > 120
        for: 3m
        labels:
          severity: high
          team: fulfillment
        annotations:
          summary: "配送调度延迟过高"
          description: "95%的配送调度时间超过2分钟，当前P95延迟为 {{ $value }}秒"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]
          - type: "WEBHOOK"
            url: "http://alert-manager/webhook/delivery-scheduling"

      # 活跃配送任务数量告警
      - alert: TooManyActiveDeliveryTasks
        expr: delivery_tasks_active_count > 2000
        for: 2m
        labels:
          severity: critical
          team: fulfillment
        annotations:
          summary: "活跃配送任务数量过多"
          description: "当前活跃配送任务数量为 {{ $value }}，超过2000个阈值，可能影响配送效率"
        actions:
          - type: "SMS"
            recipients: ["+86138****1234"]
          - type: "ESCALATION"
            escalation-policy: "delivery-capacity-escalation"
```

### 3.3 跨服务协调告警规则

```yaml
# prometheus-alerts-cross-service.yml
groups:
  - name: cross_service_alerts
    rules:
      # 跨服务协调失败告警
      - alert: CrossServiceCoordinationFailure
        expr: rate(cross_service_coordination_total{status="FAILURE"}[10m]) / rate(cross_service_coordination_total[10m]) > 0.1
        for: 3m
        labels:
          severity: high
          team: platform
        annotations:
          summary: "跨服务协调失败率过高"
          description: "过去10分钟内跨服务协调失败率为 {{ $value | humanizePercentage }}，超过10%阈值"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]
          - type: "SLACK"
            channel: "#platform-alerts"

      # Saga执行失败告警
      - alert: SagaExecutionFailure
        expr: rate(saga_execution_total{status="FAILED"}[10m]) > 5
        for: 2m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Saga执行失败频率过高"
          description: "过去10分钟内Saga执行失败频率为 {{ $value }} 次/分钟"
        actions:
          - type: "SMS"
            recipients: ["+86138****1234"]
          - type: "ESCALATION"
            escalation-policy: "saga-failure-escalation"

      # 分布式锁获取失败告警
      - alert: DistributedLockAcquisitionFailure
        expr: rate(distributed_lock_acquisition_total{status="FAILURE"}[5m]) / rate(distributed_lock_acquisition_total[5m]) > 0.2
        for: 2m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "分布式锁获取失败率过高"
          description: "过去5分钟内分布式锁获取失败率为 {{ $value | humanizePercentage }}，超过20%阈值"
        actions:
          - type: "EMAIL"
            recipients: ["<EMAIL>"]

      # 高危操作频率告警
      - alert: HighRiskOperationSpike
        expr: rate(high_risk_operation_total[5m]) > 20
        for: 1m
        labels:
          severity: critical
          team: security
        annotations:
          summary: "高危操作频率异常"
          description: "过去5分钟内高危操作频率为 {{ $value }} 次/分钟，可能存在安全风险"
        actions:
          - type: "SMS"
            recipients: ["+86138****1234"]
          - type: "EMAIL"
            recipients: ["<EMAIL>"]
          - type: "ESCALATION"
            escalation-policy: "security-incident-escalation"
```

## 4. Grafana监控面板配置

### 4.1 前置仓管理监控面板

```json
{
  "dashboard": {
    "id": null,
    "title": "PISP前置仓管理监控",
    "tags": ["pisp", "front-warehouse"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "前置仓概览",
        "type": "stat",
        "targets": [
          {
            "expr": "front_warehouse_active_count",
            "legendFormat": "活跃前置仓数量"
          },
          {
            "expr": "warehouse_low_stock_count",
            "legendFormat": "低库存前置仓数量"
          },
          {
            "expr": "replenishment_pending_count",
            "legendFormat": "待处理补货数量"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 5},
                {"color": "red", "value": 10}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "库存分配成功率",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(inventory_allocation_total{status=\"SUCCESS\"}[5m]) / rate(inventory_allocation_total[5m]) * 100",
            "legendFormat": "库存分配成功率"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 90},
                {"color": "green", "value": 95}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "前置仓操作QPS",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(front_warehouse_operations_total[5m])",
            "legendFormat": "操作QPS - {{operation_type}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "reqps"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "补货处理时间分布",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(replenishment_processing_duration_bucket[5m]))",
            "legendFormat": "P50"
          },
          {
            "expr": "histogram_quantile(0.90, rate(replenishment_processing_duration_bucket[5m]))",
            "legendFormat": "P90"
          },
          {
            "expr": "histogram_quantile(0.95, rate(replenishment_processing_duration_bucket[5m]))",
            "legendFormat": "P95"
          },
          {
            "expr": "histogram_quantile(0.99, rate(replenishment_processing_duration_bucket[5m]))",
            "legendFormat": "P99"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "s"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

### 4.2 订单履约监控面板

```json
{
  "dashboard": {
    "id": null,
    "title": "PISP订单履约监控",
    "tags": ["pisp", "order-fulfillment"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "订单履约概览",
        "type": "stat",
        "targets": [
          {
            "expr": "orders_pending_count",
            "legendFormat": "待处理订单"
          },
          {
            "expr": "picking_tasks_active_count",
            "legendFormat": "活跃拣选任务"
          },
          {
            "expr": "delivery_tasks_active_count",
            "legendFormat": "活跃配送任务"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "green", "value": null},
                {"color": "yellow", "value": 500},
                {"color": "red", "value": 1000}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "订单分配延迟",
        "type": "timeseries",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, rate(order_allocation_duration_bucket[5m]))",
            "legendFormat": "P50"
          },
          {
            "expr": "histogram_quantile(0.95, rate(order_allocation_duration_bucket[5m]))",
            "legendFormat": "P95"
          },
          {
            "expr": "histogram_quantile(0.99, rate(order_allocation_duration_bucket[5m]))",
            "legendFormat": "P99"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "s",
            "thresholds": {
              "steps": [
                {"color": "green", "value": 0},
                {"color": "yellow", "value": 30},
                {"color": "red", "value": 60}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "拣选任务成功率",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(picking_task_total{status=\"SUCCESS\"}[5m]) / rate(picking_task_total[5m]) * 100",
            "legendFormat": "拣选成功率"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "min": 0,
            "max": 100,
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 95},
                {"color": "green", "value": 98}
              ]
            }
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "配送调度性能",
        "type": "timeseries",
        "targets": [
          {
            "expr": "rate(delivery_task_total[5m])",
            "legendFormat": "配送任务创建率"
          },
          {
            "expr": "histogram_quantile(0.95, rate(delivery_scheduling_duration_bucket[5m]))",
            "legendFormat": "P95调度延迟"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "s"
          }
        },
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

## 5. 监控配置集成

### 5.1 Spring Boot Actuator配置

```yaml
# application-monitoring.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s
        descriptions: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
        front.warehouse.operations: true
        order.allocation: true
        picking.task: true
        delivery.scheduling: true
      percentiles:
        http.server.requests: 0.5, 0.9, 0.95, 0.99
        front.warehouse.operations: 0.5, 0.9, 0.95, 0.99
        order.allocation: 0.5, 0.9, 0.95, 0.99
        picking.task: 0.5, 0.9, 0.95, 0.99
        delivery.scheduling: 0.5, 0.9, 0.95, 0.99
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}
      version: ${application.version:unknown}

# 健康检查配置
  health:
    circuitbreakers:
      enabled: true
    ratelimiters:
      enabled: true
    diskspace:
      enabled: true
      threshold: 10GB
    db:
      enabled: true
    redis:
      enabled: true
    rabbit:
      enabled: false
    kafka:
      enabled: false
    rocketmq:
      enabled: true

# 日志配置
logging:
  level:
    io.micrometer: INFO
    org.springframework.boot.actuator: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId},%X{spanId}] %logger{36} - %msg%n"
```

### 5.2 Prometheus配置扩展

```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'pisp-production'
    region: 'cn-east-1'

rule_files:
  - "alert_rules/*.yml"
  - "front_warehouse_alerts.yml"
  - "order_fulfillment_alerts.yml"
  - "cross_service_alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 前置仓管理服务
  - job_name: 'front-warehouse-service'
    static_configs:
      - targets: ['front-warehouse-service:8010']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s
    honor_labels: true

  # 订单履约相关服务
  - job_name: 'order-fulfillment-services'
    static_configs:
      - targets:
        - 'picking-service:8012'
        - 'packing-service:8013'
        - 'delivery-service:8014'
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s
    honor_labels: true

  # 传统业务服务
  - job_name: 'pisp-services'
    static_configs:
      - targets:
        - 'user-service:8001'
        - 'base-data-service:8002'
        - 'procurement-service:8003'
        - 'sales-service:8004'
        - 'inventory-service:8005'
        - 'finance-service:8006'
        - 'report-service:8007'
        - 'system-service:8008'
        - 'retail-service:8009'
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    scrape_timeout: 10s
    honor_labels: true

  # 基础设施监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'rocketmq-exporter'
    static_configs:
      - targets: ['rocketmq-exporter:5557']

# 远程写入配置（可选）
remote_write:
  - url: "http://influxdb:8086/api/v1/prom/write?db=prometheus"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500
```

### 5.3 AlertManager配置

```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.company.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # 前置仓告警路由
    - match:
        team: front-warehouse
      receiver: 'front-warehouse-team'
      group_wait: 5s
      repeat_interval: 30m
      routes:
        - match:
            severity: critical
          receiver: 'front-warehouse-critical'
          repeat_interval: 15m

    # 订单履约告警路由
    - match:
        team: fulfillment
      receiver: 'fulfillment-team'
      group_wait: 5s
      repeat_interval: 30m
      routes:
        - match:
            severity: critical
          receiver: 'fulfillment-critical'
          repeat_interval: 15m

    # 平台告警路由
    - match:
        team: platform
      receiver: 'platform-team'
      group_wait: 10s
      repeat_interval: 1h

    # 安全告警路由
    - match:
        team: security
      receiver: 'security-team'
      group_wait: 0s
      repeat_interval: 10m

receivers:
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: 'PISP系统告警: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警描述: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt }}
          {{ end }}

  - name: 'front-warehouse-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '前置仓告警: {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/xxx/yyy/zzz'
        channel: '#front-warehouse-alerts'
        title: '前置仓告警'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'front-warehouse-critical'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '【紧急】前置仓严重告警: {{ .GroupLabels.alertname }}'
    webhook_configs:
      - url: 'http://alert-webhook/front-warehouse/critical'
        send_resolved: true

  - name: 'fulfillment-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '订单履约告警: {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/xxx/yyy/zzz'
        channel: '#fulfillment-alerts'

  - name: 'fulfillment-critical'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '【紧急】订单履约严重告警: {{ .GroupLabels.alertname }}'
    webhook_configs:
      - url: 'http://alert-webhook/fulfillment/critical'

  - name: 'platform-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '平台告警: {{ .GroupLabels.alertname }}'

  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '【安全告警】{{ .GroupLabels.alertname }}'
    webhook_configs:
      - url: 'http://security-incident-manager/webhook'
        send_resolved: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']
```

## 6. 部署和集成指南

### 6.1 Docker Compose监控栈

```yaml
# docker-compose-monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: pisp-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - pisp-network

  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: pisp-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager:/etc/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    networks:
      - pisp-network

  grafana:
    image: grafana/grafana:10.0.0
    container_name: pisp-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - pisp-network

  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: pisp-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - pisp-network

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.12.0
    container_name: pisp-postgres-exporter
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=*********************************************/pisp_db?sslmode=disable
    networks:
      - pisp-network

  redis-exporter:
    image: oliver006/redis_exporter:v1.52.0
    container_name: pisp-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
    networks:
      - pisp-network

volumes:
  prometheus_data:
  grafana_data:

networks:
  pisp-network:
    external: true
```

### 6.2 监控指标集成代码示例

```java
// 在每个新增微服务中添加监控配置
@Configuration
@EnableConfigurationProperties(MonitoringProperties.class)
public class MonitoringConfiguration {

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags(
            @Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags(
            "application", applicationName,
            "environment", getEnvironment(),
            "version", getVersion()
        );
    }

    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }

    @Bean
    public CountedAspect countedAspect(MeterRegistry registry) {
        return new CountedAspect(registry);
    }

    // 自定义健康检查
    @Bean
    public HealthIndicator frontWarehouseHealthIndicator() {
        return new FrontWarehouseHealthIndicator();
    }

    private String getEnvironment() {
        return System.getProperty("spring.profiles.active", "unknown");
    }

    private String getVersion() {
        return getClass().getPackage().getImplementationVersion();
    }
}

// 自定义健康检查实现
@Component
public class FrontWarehouseHealthIndicator implements HealthIndicator {

    @Autowired
    private FrontWarehouseService frontWarehouseService;

    @Override
    public Health health() {
        try {
            int activeWarehouses = frontWarehouseService.getActiveFrontWarehouseCount();
            int lowStockWarehouses = frontWarehouseService.getLowStockWarehouseCount();

            Health.Builder builder = Health.up()
                .withDetail("activeWarehouses", activeWarehouses)
                .withDetail("lowStockWarehouses", lowStockWarehouses);

            if (lowStockWarehouses > 10) {
                builder.status("WARN")
                    .withDetail("warning", "Too many low stock warehouses");
            }

            return builder.build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

## 7. 监控体系完善总结

### 7.1 完善内容概述

本文档为PISP系统新增微服务建立了完善的监控告警体系：

**🎯 核心完善内容：**

1. **新增微服务监控指标**
   - 前置仓管理服务的完整业务指标
   - 订单履约服务的性能和业务指标
   - 跨服务协调的分布式监控指标

2. **完善告警规则配置**
   - 针对前置仓业务的专用告警规则
   - 订单履约流程的性能告警
   - 跨服务协调的故障告警
   - 高危操作的安全告警

3. **优化监控面板**
   - 前置仓管理专用监控面板
   - 订单履约流程可视化面板
   - 业务指标和技术指标的统一展示

4. **集成配置完善**
   - Spring Boot Actuator深度集成
   - Prometheus配置扩展
   - AlertManager智能路由配置
   - Docker化部署方案

### 7.2 设计特点

**✅ 设计优势：**

- **业务导向**：监控指标紧密结合业务场景
- **分层监控**：从基础设施到业务应用的全栈监控
- **智能告警**：基于业务影响的告警分级和路由
- **可视化友好**：直观的监控面板和业务指标展示
- **运维高效**：自动化告警和智能升级机制

### 7.3 实施建议

**📋 实施优先级：**

1. **高优先级**：基础监控指标、核心告警规则
2. **中优先级**：监控面板、告警路由配置
3. **低优先级**：高级可视化、自定义指标

**🔄 运维要点：**

- 定期审查告警规则的有效性
- 根据业务发展调整监控阈值
- 持续优化监控面板的用户体验
- 建立监控数据的长期存储和分析机制

通过这些完善，PISP系统将具备企业级的监控告警能力，确保新增微服务的稳定运行和业务连续性。

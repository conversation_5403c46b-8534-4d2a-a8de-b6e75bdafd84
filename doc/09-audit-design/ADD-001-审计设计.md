# ADD-001 进销存管理系统审计设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | ADD-001 |
| 文档名称 | 审计设计文档 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 草稿 |
| 作者 | 合规专家 |

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-25 | 初始版本创建 | 合规专家 |

## 1. 审计概述

### 1.1 审计目标

- **合规性保证：** 确保系统符合相关法律法规要求
- **风险控制：** 识别和控制业务操作风险
- **责任追溯：** 提供完整的操作追踪链路
- **内控支持：** 支持内部控制制度执行
- **证据保全：** 为审计和调查提供可靠证据

### 1.2 审计范围

**业务审计：**
- 用户登录和权限变更
- 商品信息变更
- 库存变动操作
- 订单处理流程
- 财务数据变更
- 系统配置修改

**技术审计：**
- 数据库操作记录
- 系统访问日志
- 安全事件记录
- 性能监控数据
- 错误和异常日志

### 1.3 合规要求

```mermaid
graph TB
    subgraph "法律法规"
        A1[网络安全法]
        A2[数据安全法]
        A3[个人信息保护法]
        A4[会计法]
    end
    
    subgraph "行业标准"
        B1[等保2.0]
        B2[ISO 27001]
        B3[SOX法案]
        B4[GDPR]
    end
    
    subgraph "内控制度"
        C1[内部控制规范]
        C2[风险管理制度]
        C3[信息安全政策]
        C4[数据治理规范]
    end
    
    subgraph "审计要求"
        D1[完整性]
        D2[准确性]
        D3[及时性]
        D4[可追溯性]
    end
    
    A1 --> D1
    A2 --> D2
    B1 --> D3
    B2 --> D4
    C1 --> D1
    C2 --> D2
```

## 2. 审计架构设计

### 2.1 审计系统架构

```mermaid
graph TB
    subgraph "业务应用层"
        A1[用户管理]
        A2[商品管理]
        A3[订单管理]
        A4[库存管理]
        A5[财务管理]
    end
    
    subgraph "审计拦截层"
        B1[审计切面]
        B2[事件监听器]
        B3[数据库触发器]
    end
    
    subgraph "审计处理层"
        C1[审计事件处理器]
        C2[审计规则引擎]
        C3[风险评估引擎]
    end
    
    subgraph "审计存储层"
        D1[审计日志数据库]
        D2[文档存储]
        D3[时序数据库]
    end
    
    subgraph "审计分析层"
        E1[审计查询]
        E2[合规检查]
        E3[风险分析]
        E4[报表生成]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B1
    A5 --> B2
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    D1 --> E4
```

### 2.2 审计数据模型

```mermaid
erDiagram
    AUDIT_LOG {
        uuid id PK
        string event_type
        string resource_type
        string resource_id
        string action
        uuid user_id FK
        string session_id
        timestamp event_time
        json before_data
        json after_data
        string ip_address
        string user_agent
        string result
        string risk_level
        text description
    }
    
    AUDIT_TRAIL {
        uuid id PK
        uuid audit_log_id FK
        string field_name
        string old_value
        string new_value
        string change_type
        timestamp change_time
    }
    
    COMPLIANCE_CHECK {
        uuid id PK
        string rule_name
        string rule_type
        json rule_config
        string status
        timestamp last_check
        text check_result
    }
    
    RISK_ASSESSMENT {
        uuid id PK
        uuid audit_log_id FK
        string risk_type
        int risk_score
        string risk_level
        text risk_description
        json risk_factors
        timestamp assessed_at
    }
    
    AUDIT_REPORT {
        uuid id PK
        string report_type
        string report_name
        json report_config
        timestamp generated_at
        string file_path
        string status
    }
    
    AUDIT_LOG ||--o{ AUDIT_TRAIL : has
    AUDIT_LOG ||--o{ RISK_ASSESSMENT : triggers
    COMPLIANCE_CHECK ||--o{ AUDIT_REPORT : generates
```

## 3. 审计日志设计

### 3.1 审计事件分类

**用户行为审计：**
```typescript
enum UserAuditEvent {
    LOGIN = 'USER_LOGIN',
    LOGOUT = 'USER_LOGOUT',
    LOGIN_FAILED = 'USER_LOGIN_FAILED',
    PASSWORD_CHANGE = 'USER_PASSWORD_CHANGE',
    PROFILE_UPDATE = 'USER_PROFILE_UPDATE',
    ROLE_ASSIGN = 'USER_ROLE_ASSIGN',
    PERMISSION_GRANT = 'USER_PERMISSION_GRANT'
}
```

**业务操作审计：**
```typescript
enum BusinessAuditEvent {
    PRODUCT_CREATE = 'PRODUCT_CREATE',
    PRODUCT_UPDATE = 'PRODUCT_UPDATE',
    PRODUCT_DELETE = 'PRODUCT_DELETE',
    INVENTORY_ADJUST = 'INVENTORY_ADJUST',
    ORDER_CREATE = 'ORDER_CREATE',
    ORDER_APPROVE = 'ORDER_APPROVE',
    ORDER_CANCEL = 'ORDER_CANCEL',
    FINANCIAL_RECORD = 'FINANCIAL_RECORD'
}
```

**系统操作审计：**
```typescript
enum SystemAuditEvent {
    CONFIG_CHANGE = 'SYSTEM_CONFIG_CHANGE',
    BACKUP_CREATE = 'SYSTEM_BACKUP_CREATE',
    RESTORE_EXECUTE = 'SYSTEM_RESTORE_EXECUTE',
    SECURITY_ALERT = 'SYSTEM_SECURITY_ALERT',
    PERFORMANCE_ALERT = 'SYSTEM_PERFORMANCE_ALERT'
}
```

### 3.2 审计日志实现

**审计装饰器：**
```typescript
function Auditable(eventType: string, resourceType: string) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value;
        
        descriptor.value = async function (...args: any[]) {
            const auditContext = {
                eventType,
                resourceType,
                action: propertyName,
                userId: this.getCurrentUserId(),
                sessionId: this.getSessionId(),
                ipAddress: this.getClientIP(),
                userAgent: this.getUserAgent(),
                timestamp: new Date()
            };
            
            let beforeData: any = null;
            let result: any = null;
            let error: any = null;
            
            try {
                // 获取操作前数据
                if (args[0] && typeof args[0] === 'string') {
                    beforeData = await this.getResourceData(resourceType, args[0]);
                }
                
                // 执行原方法
                result = await method.apply(this, args);
                
                // 记录成功审计日志
                await this.auditService.logEvent({
                    ...auditContext,
                    resourceId: this.extractResourceId(args, result),
                    beforeData,
                    afterData: result,
                    result: 'SUCCESS',
                    riskLevel: this.calculateRiskLevel(eventType, resourceType)
                });
                
                return result;
                
            } catch (err) {
                error = err;
                
                // 记录失败审计日志
                await this.auditService.logEvent({
                    ...auditContext,
                    resourceId: this.extractResourceId(args),
                    beforeData,
                    result: 'FAILURE',
                    errorMessage: err.message,
                    riskLevel: 'HIGH'
                });
                
                throw err;
            }
        };
    };
}
```

**审计服务实现：**
```typescript
class AuditService {
    async logEvent(auditData: AuditEventData): Promise<void> {
        // 1. 数据验证和清洗
        const cleanedData = this.sanitizeAuditData(auditData);
        
        // 2. 风险评估
        const riskAssessment = await this.assessRisk(cleanedData);
        
        // 3. 存储审计日志
        const auditLog = await this.auditRepository.create({
            ...cleanedData,
            riskScore: riskAssessment.score,
            riskFactors: riskAssessment.factors
        });
        
        // 4. 实时告警检查
        await this.checkAlertRules(auditLog);
        
        // 5. 合规检查
        await this.checkComplianceRules(auditLog);
        
        // 6. 异步处理
        await this.queueAsyncProcessing(auditLog);
    }
    
    private async assessRisk(auditData: AuditEventData): Promise<RiskAssessment> {
        const factors: RiskFactor[] = [];
        let score = 0;
        
        // 时间因素
        const hour = auditData.timestamp.getHours();
        if (hour < 6 || hour > 22) {
            factors.push({ type: 'TIME_ANOMALY', weight: 2 });
            score += 2;
        }
        
        // 地理位置因素
        const userLocation = await this.getUserLocation(auditData.userId);
        const currentLocation = await this.getIPLocation(auditData.ipAddress);
        if (this.calculateDistance(userLocation, currentLocation) > 1000) {
            factors.push({ type: 'LOCATION_ANOMALY', weight: 3 });
            score += 3;
        }
        
        // 操作频率因素
        const recentOperations = await this.getRecentOperations(
            auditData.userId, 
            auditData.eventType, 
            15 // 15分钟内
        );
        if (recentOperations.length > 10) {
            factors.push({ type: 'FREQUENCY_ANOMALY', weight: 2 });
            score += 2;
        }
        
        // 权限因素
        if (this.isPrivilegedOperation(auditData.eventType)) {
            factors.push({ type: 'PRIVILEGED_OPERATION', weight: 1 });
            score += 1;
        }
        
        return {
            score,
            level: this.getRiskLevel(score),
            factors
        };
    }
}
```

### 3.3 审计日志查询

**查询接口设计：**
```typescript
interface AuditQueryParams {
    startDate?: Date;
    endDate?: Date;
    userId?: string;
    eventType?: string;
    resourceType?: string;
    resourceId?: string;
    riskLevel?: string;
    result?: 'SUCCESS' | 'FAILURE';
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
}

class AuditQueryService {
    async queryAuditLogs(params: AuditQueryParams): Promise<AuditQueryResult> {
        const query = this.buildQuery(params);
        
        // 权限检查
        await this.checkQueryPermission(params);
        
        // 执行查询
        const [logs, total] = await Promise.all([
            this.auditRepository.find(query),
            this.auditRepository.count(query.where)
        ]);
        
        // 数据脱敏
        const maskedLogs = logs.map(log => this.maskSensitiveData(log));
        
        return {
            data: maskedLogs,
            total,
            page: params.page || 1,
            pageSize: params.pageSize || 20
        };
    }
    
    private maskSensitiveData(log: AuditLog): AuditLog {
        const masked = { ...log };
        
        // 脱敏敏感字段
        if (masked.beforeData) {
            masked.beforeData = this.dataMaskingService.mask(masked.beforeData);
        }
        
        if (masked.afterData) {
            masked.afterData = this.dataMaskingService.mask(masked.afterData);
        }
        
        return masked;
    }
}
```

## 4. 合规检查

### 4.1 合规规则引擎

**规则定义：**
```typescript
interface ComplianceRule {
    id: string;
    name: string;
    description: string;
    ruleType: 'PREVENTIVE' | 'DETECTIVE' | 'CORRECTIVE';
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    conditions: RuleCondition[];
    actions: RuleAction[];
    enabled: boolean;
}

interface RuleCondition {
    field: string;
    operator: 'EQUALS' | 'NOT_EQUALS' | 'CONTAINS' | 'GREATER_THAN' | 'LESS_THAN';
    value: any;
    logicalOperator?: 'AND' | 'OR';
}

interface RuleAction {
    type: 'ALERT' | 'BLOCK' | 'LOG' | 'NOTIFY';
    config: any;
}
```

**规则示例：**
```typescript
const complianceRules: ComplianceRule[] = [
    {
        id: 'SOX_FINANCIAL_SEGREGATION',
        name: 'SOX财务职责分离',
        description: '同一用户不能同时创建和审批财务记录',
        ruleType: 'PREVENTIVE',
        severity: 'CRITICAL',
        conditions: [
            {
                field: 'eventType',
                operator: 'EQUALS',
                value: 'FINANCIAL_APPROVE'
            },
            {
                field: 'userId',
                operator: 'EQUALS',
                value: '${CREATOR_USER_ID}',
                logicalOperator: 'AND'
            }
        ],
        actions: [
            {
                type: 'BLOCK',
                config: { message: '违反职责分离原则，无法执行操作' }
            },
            {
                type: 'ALERT',
                config: { 
                    level: 'CRITICAL',
                    recipients: ['<EMAIL>']
                }
            }
        ],
        enabled: true
    },
    
    {
        id: 'GDPR_DATA_ACCESS',
        name: 'GDPR数据访问记录',
        description: '记录所有个人数据访问操作',
        ruleType: 'DETECTIVE',
        severity: 'HIGH',
        conditions: [
            {
                field: 'resourceType',
                operator: 'EQUALS',
                value: 'PERSONAL_DATA'
            }
        ],
        actions: [
            {
                type: 'LOG',
                config: { 
                    logLevel: 'INFO',
                    retention: '7_YEARS'
                }
            }
        ],
        enabled: true
    }
];
```

### 4.2 合规检查实现

**合规检查引擎：**
```typescript
class ComplianceEngine {
    async checkCompliance(auditEvent: AuditEvent): Promise<ComplianceResult> {
        const applicableRules = await this.getApplicableRules(auditEvent);
        const results: RuleCheckResult[] = [];
        
        for (const rule of applicableRules) {
            const result = await this.evaluateRule(rule, auditEvent);
            results.push(result);
            
            if (result.violated && rule.ruleType === 'PREVENTIVE') {
                // 阻止操作
                throw new ComplianceViolationError(rule, result);
            }
        }
        
        return {
            passed: results.every(r => !r.violated),
            results,
            recommendations: this.generateRecommendations(results)
        };
    }
    
    private async evaluateRule(rule: ComplianceRule, event: AuditEvent): Promise<RuleCheckResult> {
        let conditionsMet = true;
        
        for (const condition of rule.conditions) {
            const fieldValue = this.getFieldValue(event, condition.field);
            const conditionMet = this.evaluateCondition(condition, fieldValue, event);
            
            if (condition.logicalOperator === 'OR') {
                conditionsMet = conditionsMet || conditionMet;
            } else {
                conditionsMet = conditionsMet && conditionMet;
            }
        }
        
        if (conditionsMet) {
            await this.executeRuleActions(rule.actions, event);
        }
        
        return {
            ruleId: rule.id,
            violated: conditionsMet,
            severity: rule.severity,
            message: conditionsMet ? rule.description : null
        };
    }
}
```

## 5. 审计报表

### 5.1 标准审计报表

**用户活动报表：**
```typescript
class UserActivityReport {
    async generate(params: ReportParams): Promise<ReportData> {
        const data = await this.auditRepository.query(`
            SELECT 
                u.username,
                COUNT(*) as total_operations,
                COUNT(CASE WHEN al.result = 'SUCCESS' THEN 1 END) as successful_operations,
                COUNT(CASE WHEN al.result = 'FAILURE' THEN 1 END) as failed_operations,
                COUNT(CASE WHEN al.risk_level = 'HIGH' THEN 1 END) as high_risk_operations,
                MIN(al.event_time) as first_activity,
                MAX(al.event_time) as last_activity
            FROM audit_log al
            JOIN users u ON al.user_id = u.id
            WHERE al.event_time BETWEEN ? AND ?
            GROUP BY u.id, u.username
            ORDER BY total_operations DESC
        `, [params.startDate, params.endDate]);
        
        return {
            title: '用户活动报表',
            period: `${params.startDate} - ${params.endDate}`,
            data,
            summary: this.calculateSummary(data)
        };
    }
}
```

**合规性报表：**
```typescript
class ComplianceReport {
    async generate(params: ReportParams): Promise<ReportData> {
        const violations = await this.getComplianceViolations(params);
        const riskDistribution = await this.getRiskDistribution(params);
        const trendAnalysis = await this.getTrendAnalysis(params);
        
        return {
            title: '合规性报表',
            period: `${params.startDate} - ${params.endDate}`,
            sections: [
                {
                    title: '合规违规统计',
                    data: violations,
                    chartType: 'bar'
                },
                {
                    title: '风险等级分布',
                    data: riskDistribution,
                    chartType: 'pie'
                },
                {
                    title: '趋势分析',
                    data: trendAnalysis,
                    chartType: 'line'
                }
            ]
        };
    }
}
```

### 5.2 自定义报表

**报表配置：**
```typescript
interface CustomReportConfig {
    name: string;
    description: string;
    dataSource: string;
    filters: ReportFilter[];
    groupBy: string[];
    aggregations: ReportAggregation[];
    chartConfig: ChartConfig;
    schedule?: ReportSchedule;
}

interface ReportFilter {
    field: string;
    operator: string;
    value: any;
    required: boolean;
}

interface ReportAggregation {
    field: string;
    function: 'COUNT' | 'SUM' | 'AVG' | 'MIN' | 'MAX';
    alias: string;
}
```

**报表生成器：**
```typescript
class CustomReportGenerator {
    async generateReport(config: CustomReportConfig, params: any): Promise<ReportData> {
        // 1. 构建查询
        const query = this.buildQuery(config, params);
        
        // 2. 执行查询
        const rawData = await this.executeQuery(query);
        
        // 3. 数据处理
        const processedData = this.processData(rawData, config);
        
        // 4. 生成图表
        const chartData = this.generateChartData(processedData, config.chartConfig);
        
        return {
            title: config.name,
            description: config.description,
            data: processedData,
            chart: chartData,
            generatedAt: new Date()
        };
    }
}
```

## 6. 数据保留与归档

### 6.1 数据生命周期管理

**保留策略：**
```mermaid
graph LR
    A[实时数据] --> B[热数据 30天]
    B --> C[温数据 1年]
    C --> D[冷数据 7年]
    D --> E[归档/销毁]
    
    B --> F[高频访问]
    C --> G[中频访问]
    D --> H[低频访问]
```

**归档实现：**
```typescript
class AuditDataArchiver {
    async archiveOldData(): Promise<void> {
        const cutoffDate = new Date();
        cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
        
        // 1. 查询需要归档的数据
        const oldLogs = await this.auditRepository.findOlderThan(cutoffDate);
        
        // 2. 压缩和加密
        const compressedData = await this.compressData(oldLogs);
        const encryptedData = await this.encryptData(compressedData);
        
        // 3. 存储到归档系统
        const archiveLocation = await this.storeToArchive(encryptedData);
        
        // 4. 创建归档记录
        await this.createArchiveRecord({
            archiveDate: new Date(),
            dataCount: oldLogs.length,
            location: archiveLocation,
            checksum: this.calculateChecksum(encryptedData)
        });
        
        // 5. 删除原始数据
        await this.auditRepository.deleteOlderThan(cutoffDate);
    }
    
    async restoreFromArchive(archiveId: string): Promise<void> {
        const archiveRecord = await this.getArchiveRecord(archiveId);
        
        // 1. 从归档系统获取数据
        const encryptedData = await this.retrieveFromArchive(archiveRecord.location);
        
        // 2. 验证完整性
        const checksum = this.calculateChecksum(encryptedData);
        if (checksum !== archiveRecord.checksum) {
            throw new Error('Archive data integrity check failed');
        }
        
        // 3. 解密和解压
        const compressedData = await this.decryptData(encryptedData);
        const originalData = await this.decompressData(compressedData);
        
        // 4. 恢复到数据库
        await this.auditRepository.bulkInsert(originalData);
    }
}
```

## 7. 审计监控与告警

### 7.1 实时监控

**监控指标：**
```typescript
interface AuditMetrics {
    totalEvents: number;
    failedEvents: number;
    highRiskEvents: number;
    complianceViolations: number;
    averageRiskScore: number;
    topRiskUsers: UserRiskScore[];
    eventTypeDistribution: EventTypeCount[];
}

class AuditMonitor {
    async collectMetrics(timeWindow: number = 3600): Promise<AuditMetrics> {
        const startTime = new Date(Date.now() - timeWindow * 1000);
        
        const [
            totalEvents,
            failedEvents,
            highRiskEvents,
            violations,
            avgRiskScore,
            topRiskUsers,
            eventDistribution
        ] = await Promise.all([
            this.countTotalEvents(startTime),
            this.countFailedEvents(startTime),
            this.countHighRiskEvents(startTime),
            this.countViolations(startTime),
            this.calculateAverageRiskScore(startTime),
            this.getTopRiskUsers(startTime),
            this.getEventTypeDistribution(startTime)
        ]);
        
        return {
            totalEvents,
            failedEvents,
            highRiskEvents,
            complianceViolations: violations,
            averageRiskScore: avgRiskScore,
            topRiskUsers,
            eventTypeDistribution: eventDistribution
        };
    }
}
```

### 7.2 告警规则

**告警配置：**
```typescript
const alertRules: AlertRule[] = [
    {
        name: 'HIGH_RISK_ACTIVITY',
        condition: 'risk_score > 8',
        threshold: 1,
        timeWindow: 300, // 5分钟
        severity: 'CRITICAL',
        actions: ['EMAIL', 'SMS', 'WEBHOOK']
    },
    {
        name: 'MULTIPLE_LOGIN_FAILURES',
        condition: 'event_type = "LOGIN_FAILED"',
        threshold: 5,
        timeWindow: 900, // 15分钟
        groupBy: 'ip_address',
        severity: 'HIGH',
        actions: ['EMAIL', 'BLOCK_IP']
    },
    {
        name: 'COMPLIANCE_VIOLATION',
        condition: 'compliance_violation = true',
        threshold: 1,
        timeWindow: 60, // 1分钟
        severity: 'CRITICAL',
        actions: ['EMAIL', 'ESCALATE']
    }
];
```

**告警处理：**
```typescript
class AlertProcessor {
    async processAlert(alert: Alert): Promise<void> {
        // 1. 告警去重
        if (await this.isDuplicateAlert(alert)) {
            return;
        }
        
        // 2. 告警升级
        const escalatedAlert = await this.escalateIfNeeded(alert);
        
        // 3. 执行告警动作
        for (const action of escalatedAlert.actions) {
            await this.executeAction(action, escalatedAlert);
        }
        
        // 4. 记录告警历史
        await this.recordAlert(escalatedAlert);
    }
    
    private async executeAction(action: string, alert: Alert): Promise<void> {
        switch (action) {
            case 'EMAIL':
                await this.sendEmailAlert(alert);
                break;
            case 'SMS':
                await this.sendSMSAlert(alert);
                break;
            case 'WEBHOOK':
                await this.sendWebhookAlert(alert);
                break;
            case 'BLOCK_IP':
                await this.blockIPAddress(alert.sourceIP);
                break;
            case 'ESCALATE':
                await this.escalateToManager(alert);
                break;
        }
    }
}
```

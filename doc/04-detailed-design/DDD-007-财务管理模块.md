# DDD-006 财务管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-006 |
| 文档名称 | 财务管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

财务管理模块负责管理企业的财务业务，包括应收应付管理、成本核算和财务对账等功能。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "财务管理模块"
        A[应收账款服务]
        B[应付账款服务]
        C[成本核算服务]
        D[财务对账服务]
    end
    
    subgraph "核心功能"
        E[应收账款管理]
        F[应付账款管理]
        G[成本计算分析]
        H[自动对账处理]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
```

## 2. 应收账款管理

### 2.1 应收账款实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("accounts_receivable")
public class AccountsReceivable extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("ar_number")
    private String arNumber;

    @TableField("customer_id")
    private Long customerId;

    @TableField("sales_order_id")
    private Long salesOrderId;

    @TableField("invoice_number")
    private String invoiceNumber;

    @TableField("invoice_date")
    private LocalDate invoiceDate;
    
    @TableField("due_date")
    private LocalDate dueDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("paid_amount")
    private BigDecimal paidAmount = BigDecimal.ZERO;

    @TableField("outstanding_amount")
    private BigDecimal outstandingAmount;

    @TableField("status")
    @EnumValue
    private ARStatus status; // PENDING, PARTIAL_PAID, PAID, OVERDUE

    @TableField("payment_terms")
    private Integer paymentTerms;

    @TableField("remarks")
    private String remarks;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void addPayment(ARPayment payment) {
        payment.setAccountsReceivable(this);
        this.payments.add(payment);
        recalculateOutstanding();
    }
    
    public void recalculateOutstanding() {
        this.paidAmount = payments.stream()
            .map(ARPayment::getPaymentAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.outstandingAmount = this.totalAmount.subtract(this.paidAmount);
        
        // 更新状态
        if (this.outstandingAmount.compareTo(BigDecimal.ZERO) <= 0) {
            this.status = ARStatus.PAID;
        } else if (this.paidAmount.compareTo(BigDecimal.ZERO) > 0) {
            this.status = ARStatus.PARTIAL_PAID;
        } else if (LocalDate.now().isAfter(this.dueDate)) {
            this.status = ARStatus.OVERDUE;
        } else {
            this.status = ARStatus.PENDING;
        }
    }
    
    public boolean isOverdue() {
        return LocalDate.now().isAfter(this.dueDate) && 
               this.outstandingAmount.compareTo(BigDecimal.ZERO) > 0;
    }
}
```

### 2.2 应收账款付款记录

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ar_payments")
public class ARPayment extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("payment_number")
    private String paymentNumber;

    @TableField("accounts_receivable_id")
    private Long accountsReceivableId;

    @TableField("payment_date")
    private LocalDate paymentDate;

    @TableField("payment_amount")
    private BigDecimal paymentAmount;
    
    @TableField("payment_method")
    @EnumValue
    private PaymentMethod paymentMethod; // CASH, BANK_TRANSFER, CHECK, CREDIT_CARD

    @TableField("reference_number")
    private String referenceNumber;

    @TableField("remarks")
    private String remarks;

    @TableField("received_by")
    private Long receivedBy;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
```

### 2.3 应收账款服务实现

```java
@Service
@Transactional
public class AccountsReceivableServiceImpl extends ServiceImpl<AccountsReceivableMapper, AccountsReceivable> 
    implements AccountsReceivableService {
    
    @Autowired
    private SalesOrderService salesOrderService;
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private ARNumberGenerator numberGenerator;
    
    @Override
    public AccountsReceivableDTO createAccountsReceivable(CreateARRequest request) {
        // 1. 验证销售订单
        SalesOrder salesOrder = salesOrderService.getById(request.getSalesOrderId());
        if (salesOrder == null) {
            throw new BusinessException("销售订单不存在");
        }
        
        // 2. 检查是否已创建应收账款
        boolean exists = this.exists(
            new LambdaQueryWrapper<AccountsReceivable>()
                .eq(AccountsReceivable::getSalesOrderId, request.getSalesOrderId())
        );
        if (exists) {
            throw new BusinessException("该销售订单已创建应收账款");
        }
        
        // 3. 生成应收账款编号
        String arNumber = numberGenerator.generateARNumber();
        
        // 4. 创建应收账款
        AccountsReceivable ar = new AccountsReceivable();
        ar.setArNumber(arNumber);
        ar.setCustomerId(salesOrder.getCustomerId());
        ar.setSalesOrderId(request.getSalesOrderId());
        ar.setInvoiceNumber(request.getInvoiceNumber());
        ar.setInvoiceDate(request.getInvoiceDate());
        ar.setTotalAmount(salesOrder.getFinalAmount());
        ar.setOutstandingAmount(salesOrder.getFinalAmount());
        ar.setPaymentTerms(salesOrder.getPaymentTerms());
        ar.setStatus(ARStatus.PENDING);
        
        // 计算到期日
        LocalDate dueDate = request.getInvoiceDate().plusDays(ar.getPaymentTerms());
        ar.setDueDate(dueDate);
        
        // 5. 保存应收账款
        this.save(ar);
        
        return convertToDTO(ar);
    }
    
    @Override
    public ARPaymentDTO recordPayment(Long arId, RecordARPaymentRequest request) {
        // 1. 获取应收账款
        AccountsReceivable ar = this.getById(arId);
        if (ar == null) {
            throw new BusinessException("应收账款不存在");
        }
        
        // 2. 验证付款金额
        if (request.getPaymentAmount().compareTo(ar.getOutstandingAmount()) > 0) {
            throw new BusinessException("付款金额不能超过未付金额");
        }
        
        // 3. 生成付款编号
        String paymentNumber = numberGenerator.generatePaymentNumber();
        
        // 4. 创建付款记录
        ARPayment payment = new ARPayment();
        payment.setPaymentNumber(paymentNumber);
        payment.setPaymentDate(request.getPaymentDate());
        payment.setPaymentAmount(request.getPaymentAmount());
        payment.setPaymentMethod(request.getPaymentMethod());
        payment.setReferenceNumber(request.getReferenceNumber());
        payment.setRemarks(request.getRemarks());
        payment.setReceivedBy(request.getReceivedBy());
        
        // 5. 添加到应收账款
        ar.addPayment(payment);
        this.updateById(ar);
        
        // 6. 释放客户信用额度
        Customer customer = customerService.getById(ar.getCustomerId());
        customer.releaseCredit(request.getPaymentAmount());
        customerService.updateById(customer);
        
        // 7. 发布付款事件
        applicationEventPublisher.publishEvent(
            new ARPaymentRecordedEvent(arId, request.getPaymentAmount())
        );
        
        return convertToDTO(payment);
    }
    
    @Override
    public List<AccountsReceivableDTO> getOverdueAccounts() {
        LambdaQueryWrapper<AccountsReceivable> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(AccountsReceivable::getDueDate, LocalDate.now())
               .gt(AccountsReceivable::getOutstandingAmount, BigDecimal.ZERO)
               .orderByAsc(AccountsReceivable::getDueDate);
        
        List<AccountsReceivable> overdueAccounts = this.list(wrapper);
        return overdueAccounts.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public ARAgingReportDTO generateAgingReport(LocalDate asOfDate) {
        List<AccountsReceivable> allAR = this.list(
            new LambdaQueryWrapper<AccountsReceivable>()
                .gt(AccountsReceivable::getOutstandingAmount, BigDecimal.ZERO)
        );
        
        ARAgingReportDTO report = new ARAgingReportDTO();
        report.setAsOfDate(asOfDate);
        
        BigDecimal current = BigDecimal.ZERO;
        BigDecimal days1to30 = BigDecimal.ZERO;
        BigDecimal days31to60 = BigDecimal.ZERO;
        BigDecimal days61to90 = BigDecimal.ZERO;
        BigDecimal over90 = BigDecimal.ZERO;
        
        for (AccountsReceivable ar : allAR) {
            long daysPastDue = ChronoUnit.DAYS.between(ar.getDueDate(), asOfDate);
            BigDecimal amount = ar.getOutstandingAmount();
            
            if (daysPastDue <= 0) {
                current = current.add(amount);
            } else if (daysPastDue <= 30) {
                days1to30 = days1to30.add(amount);
            } else if (daysPastDue <= 60) {
                days31to60 = days31to60.add(amount);
            } else if (daysPastDue <= 90) {
                days61to90 = days61to90.add(amount);
            } else {
                over90 = over90.add(amount);
            }
        }
        
        report.setCurrent(current);
        report.setDays1to30(days1to30);
        report.setDays31to60(days31to60);
        report.setDays61to90(days61to90);
        report.setOver90(over90);
        report.setTotal(current.add(days1to30).add(days31to60).add(days61to90).add(over90));
        
        return report;
    }
}
```

## 3. 应付账款管理

### 3.1 应付账款实体设计

```java
@Entity
@Table(name = "accounts_payable")
@Data
@EqualsAndHashCode(callSuper = true)
public class AccountsPayable extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ap_number", unique = true, nullable = false)
    private String apNumber;

    @Column(name = "supplier_id")
    private Long supplierId;

    @Column(name = "purchase_order_id")
    private Long purchaseOrderId;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "invoice_date")
    private LocalDate invoiceDate;

    @Column(name = "due_date")
    private LocalDate dueDate;

    @Column(name = "total_amount", precision = 12, scale = 2)
    private BigDecimal totalAmount;

    @Column(name = "paid_amount", precision = 12, scale = 2)
    private BigDecimal paidAmount = BigDecimal.ZERO;

    @Column(name = "outstanding_amount", precision = 12, scale = 2)
    private BigDecimal outstandingAmount;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private APStatus status;

    @OneToMany(mappedBy = "accountsPayable", cascade = CascadeType.ALL)
    private List<APPayment> payments = new ArrayList<>();

    // 业务方法
    public void addPayment(APPayment payment) {
        payment.setAccountsPayable(this);
        this.payments.add(payment);
        recalculateOutstanding();
    }

    public void recalculateOutstanding() {
        this.paidAmount = payments.stream()
            .map(APPayment::getPaymentAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        this.outstandingAmount = this.totalAmount.subtract(this.paidAmount);

        // 更新状态
        if (this.outstandingAmount.compareTo(BigDecimal.ZERO) <= 0) {
            this.status = APStatus.PAID;
        } else if (this.paidAmount.compareTo(BigDecimal.ZERO) > 0) {
            this.status = APStatus.PARTIAL_PAID;
        } else if (LocalDate.now().isAfter(this.dueDate)) {
            this.status = APStatus.OVERDUE;
        } else {
            this.status = APStatus.PENDING;
        }
    }
}
```

## 4. 类图设计

### 4.1 财务管理核心类图

```mermaid
classDiagram
    class AccountsReceivable {
        +Long id
        +String arNumber
        +Long customerId
        +Long salesOrderId
        +String invoiceNumber
        +LocalDate invoiceDate
        +LocalDate dueDate
        +BigDecimal totalAmount
        +BigDecimal paidAmount
        +BigDecimal outstandingAmount
        +ARStatus status
        +addPayment()
        +recalculateOutstanding()
        +isOverdue()
    }

    class ARPayment {
        +Long id
        +String paymentNumber
        +Long accountsReceivableId
        +LocalDate paymentDate
        +BigDecimal paymentAmount
        +PaymentMethod paymentMethod
        +String referenceNumber
        +String remarks
        +Long receivedBy
    }

    class AccountsPayable {
        +Long id
        +String apNumber
        +Long supplierId
        +Long purchaseOrderId
        +String invoiceNumber
        +LocalDate invoiceDate
        +LocalDate dueDate
        +BigDecimal totalAmount
        +BigDecimal paidAmount
        +BigDecimal outstandingAmount
        +APStatus status
        +addPayment()
        +recalculateOutstanding()
    }

    class APPayment {
        +Long id
        +String paymentNumber
        +Long accountsPayableId
        +LocalDate paymentDate
        +BigDecimal paymentAmount
        +PaymentMethod paymentMethod
        +String referenceNumber
        +String remarks
        +Long paidBy
    }

    class CostCalculationService {
        +calculateProductCost()
        +analyzeSalesCost()
        +calculateWeightedAverageCost()
        +getLatestPurchaseCost()
    }

    class AccountsReceivableService {
        +createAccountsReceivable()
        +recordPayment()
        +getOverdueAccounts()
        +generateAgingReport()
    }

    class AccountsPayableService {
        +createAccountsPayable()
        +recordPayment()
        +getUpcomingPayments()
    }

    AccountsReceivable --o ARPayment
    AccountsPayable --o APPayment

    AccountsReceivableService ..> AccountsReceivable
    AccountsPayableService ..> AccountsPayable
    CostCalculationService ..> AccountsReceivable
    CostCalculationService ..> AccountsPayable
```

## 5. 时序图设计

### 5.1 应收账款创建时序图

```mermaid
sequenceDiagram
    participant U as 财务人员
    participant G as ShenYu网关
    participant FS as 财务服务
    participant SS as 销售服务
    participant CS as 客户服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 创建应收账款请求
    G->>FS: 转发请求

    FS->>SS: 验证销售订单
    SS-->>FS: 返回订单信息

    FS->>FS: 检查是否已创建应收账款
    FS->>FS: 生成应收账款编号
    FS->>FS: 计算到期日

    FS->>DB: 保存应收账款
    DB-->>FS: 返回应收账款ID

    FS->>K: 发布应收账款创建事件
    K-->>CS: 客户账款事件

    FS-->>G: 返回创建结果
    G-->>U: 返回响应

    Note over K: 异步事件处理
    K->>FS: 账款提醒设置事件
```

### 5.2 付款记录时序图

```mermaid
sequenceDiagram
    participant U as 财务人员
    participant G as ShenYu网关
    participant FS as 财务服务
    participant CS as 客户服务
    participant K as Kafka
    participant DB as 数据库
    participant NS as 通知服务

    U->>G: 记录付款请求
    G->>FS: 转发请求

    FS->>DB: 获取应收账款信息
    DB-->>FS: 返回账款信息

    FS->>FS: 验证付款金额
    FS->>FS: 生成付款编号
    FS->>FS: 创建付款记录

    FS->>DB: 保存付款记录
    FS->>DB: 更新应收账款状态
    DB-->>FS: 确认更新

    FS->>CS: 释放客户信用额度
    CS-->>FS: 确认释放

    FS->>K: 发布付款记录事件
    K-->>NS: 付款通知事件

    FS-->>G: 返回记录结果
    G-->>U: 返回响应

    Note over K: 异步通知处理
    NS->>NS: 发送付款确认通知
```

## 6. 交互图设计

### 6.1 应收账款管理流程交互图

```mermaid
graph TB
    subgraph "应收账款管理流程"
        A[销售订单完成] --> B[创建应收账款]
        B --> C[设置付款期限]
        C --> D[发送账单]

        D --> E{客户付款}
        E -->|按时付款| F[记录付款]
        E -->|逾期未付| G[逾期提醒]

        F --> H[更新账款状态]
        H --> I{完全付清?}
        I -->|是| J[账款结清]
        I -->|否| K[部分付款]

        G --> L[催收流程]
        L --> M{催收结果}
        M -->|付款| F
        M -->|继续逾期| N[升级催收]

        K --> O[继续跟进]
        O --> E

        J --> P[释放信用额度]
        N --> Q[法务处理]
    end
```

### 6.2 成本核算流程交互图

```mermaid
graph LR
    subgraph "成本核算流程"
        A[采购入库] --> B[记录采购成本]
        B --> C[更新商品成本]
        C --> D[计算加权平均成本]

        E[销售出库] --> F[获取商品成本]
        F --> G[计算销售成本]
        G --> H[计算毛利润]

        D --> I[成本分析报告]
        H --> J[盈利分析报告]

        I --> K[成本优化建议]
        J --> L[定价策略调整]
    end
```

## 7. 事件驱动设计

### 7.1 财务领域事件

```java
// 应收账款创建事件
@Data
@AllArgsConstructor
public class AccountsReceivableCreatedEvent extends DomainEvent {
    private Long arId;
    private String arNumber;
    private Long customerId;
    private Long salesOrderId;
    private BigDecimal totalAmount;
    private LocalDate dueDate;
}

// 付款记录事件
@Data
@AllArgsConstructor
public class PaymentRecordedEvent extends DomainEvent {
    private Long paymentId;
    private Long arId;
    private BigDecimal paymentAmount;
    private PaymentMethod paymentMethod;
    private LocalDate paymentDate;
    private Long receivedBy;
}

// 逾期账款事件
@Data
@AllArgsConstructor
public class OverdueAccountEvent extends DomainEvent {
    private Long arId;
    private Long customerId;
    private BigDecimal outstandingAmount;
    private Long overdueDays;
    private String customerName;
}
```

### 7.2 Kafka事件处理

```java
@Component
public class FinanceEventPublisher {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String FINANCE_TOPIC = "finance.events";

    public void publishARCreated(AccountsReceivableCreatedEvent event) {
        kafkaTemplate.send(FINANCE_TOPIC, "ar.created", event);
    }

    public void publishPaymentRecorded(PaymentRecordedEvent event) {
        kafkaTemplate.send(FINANCE_TOPIC, "payment.recorded", event);
    }

    public void publishOverdueAccount(OverdueAccountEvent event) {
        kafkaTemplate.send(FINANCE_TOPIC, "account.overdue", event);
    }
}

@Component
@KafkaListener(topics = "finance.events")
public class FinanceEventHandler {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private ReportService reportService;

    @KafkaHandler
    public void handleARCreated(AccountsReceivableCreatedEvent event) {
        // 设置付款提醒
        notificationService.schedulePaymentReminder(event);

        // 更新客户账款统计
        customerService.updateAccountsReceivableStats(event.getCustomerId());
    }

    @KafkaHandler
    public void handlePaymentRecorded(PaymentRecordedEvent event) {
        // 发送付款确认通知
        notificationService.sendPaymentConfirmation(event);

        // 更新财务报表缓存
        reportService.updateFinanceReportCache(event.getArId());
    }

    @KafkaHandler
    public void handleOverdueAccount(OverdueAccountEvent event) {
        // 发送逾期提醒
        notificationService.sendOverdueReminder(event);

        // 记录逾期历史
        customerService.recordOverdueHistory(event);
    }
}

// 定时检查逾期账款
@Component
public class OverdueAccountChecker {

    @Autowired
    private AccountsReceivableService arService;

    @Autowired
    private FinanceEventPublisher eventPublisher;

    @Scheduled(cron = "0 0 9 * * ?") // 每天上午9点执行
    public void checkOverdueAccounts() {
        List<AccountsReceivableDTO> overdueAccounts = arService.getOverdueAccounts();

        for (AccountsReceivableDTO ar : overdueAccounts) {
            long overdueDays = ChronoUnit.DAYS.between(ar.getDueDate(), LocalDate.now());

            OverdueAccountEvent event = new OverdueAccountEvent(
                ar.getId(),
                ar.getCustomerId(),
                ar.getOutstandingAmount(),
                overdueDays,
                ar.getCustomerName()
            );

            eventPublisher.publishOverdueAccount(event);
        }
    }
}
```

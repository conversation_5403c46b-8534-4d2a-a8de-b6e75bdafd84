# DDD-009 系统管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-009 |
| 文档名称 | 系统管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

### 1.1 模块职责

系统管理模块负责ERP系统的配置管理、数据备份恢复、操作日志记录和系统监控，确保系统的稳定运行和数据安全。

### 1.2 核心功能

- **系统配置管理**：系统参数配置、业务规则配置、字典数据管理
- **数据备份恢复**：定时数据备份、备份策略管理、数据恢复操作
- **操作日志管理**：用户操作日志、系统日志、审计追踪
- **系统监控**：性能监控、健康检查、告警管理、统计分析

### 1.3 模块架构图

```mermaid
graph TB
    subgraph "系统管理模块"
        A[系统配置管理]
        B[数据备份恢复]
        C[操作日志管理]
        D[系统监控]
    end
    
    subgraph "核心实体"
        E[SystemConfig系统配置]
        F[DataBackup数据备份]
        G[OperationLog操作日志]
        H[SystemMonitor系统监控]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
```

## 2. 实体设计

### 2.1 系统配置实体 (SystemConfig)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_configs")
public class SystemConfig extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("config_key")
    private String configKey;

    @TableField("config_value")
    private String configValue;

    @TableField("config_name")
    private String configName;

    @TableField("config_group")
    private String configGroup;

    @TableField("config_type")
    @EnumValue
    private ConfigType configType;

    @TableField("description")
    private String description;

    @TableField("is_system")
    private Boolean isSystem = false;

    @TableField("sort_order")
    private Integer sortOrder;

    @TableField("status")
    @EnumValue
    private ConfigStatus status;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public boolean isSystemConfig() {
        return Boolean.TRUE.equals(this.isSystem);
    }
    
    public void updateValue(String newValue) {
        this.configValue = newValue;
        this.updateTime = LocalDateTime.now();
    }
}

public enum ConfigType {
    STRING("字符串"),
    NUMBER("数字"),
    BOOLEAN("布尔值"),
    JSON("JSON对象"),
    TEXT("长文本");
    
    private final String description;
    
    ConfigType(String description) {
        this.description = description;
    }
}

public enum ConfigStatus {
    ACTIVE("启用"),
    INACTIVE("禁用");
    
    private final String description;
    
    ConfigStatus(String description) {
        this.description = description;
    }
}
```

### 2.2 数据备份实体 (DataBackup)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_data_backups")
public class DataBackup extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("backup_name")
    private String backupName;

    @TableField("backup_type")
    @EnumValue
    private BackupType backupType;

    @TableField("backup_path")
    private String backupPath;

    @TableField("file_size")
    private Long fileSize;

    @TableField("backup_time")
    private LocalDateTime backupTime;

    @TableField("backup_duration")
    private Long backupDuration; // 备份耗时（秒）

    @TableField("status")
    @EnumValue
    private BackupStatus status;

    @TableField("error_message")
    private String errorMessage;

    @TableField("description")
    private String description;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void markAsCompleted(String backupPath, Long fileSize, Long duration) {
        this.backupPath = backupPath;
        this.fileSize = fileSize;
        this.backupDuration = duration;
        this.status = BackupStatus.COMPLETED;
        this.backupTime = LocalDateTime.now();
    }
    
    public void markAsFailed(String errorMessage) {
        this.status = BackupStatus.FAILED;
        this.errorMessage = errorMessage;
        this.backupTime = LocalDateTime.now();
    }
    
    public String getFormattedFileSize() {
        if (fileSize == null) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
}

public enum BackupType {
    FULL("全量备份"),
    INCREMENTAL("增量备份"),
    MANUAL("手动备份");
    
    private final String description;
    
    BackupType(String description) {
        this.description = description;
    }
}

public enum BackupStatus {
    PENDING("等待中"),
    RUNNING("备份中"),
    COMPLETED("已完成"),
    FAILED("失败");
    
    private final String description;
    
    BackupStatus(String description) {
        this.description = description;
    }
}
```

### 2.3 操作日志实体 (OperationLog)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_operation_logs")
public class OperationLog extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("username")
    private String username;

    @TableField("operation_module")
    private String operationModule;

    @TableField("operation_type")
    private String operationType;

    @TableField("operation_desc")
    private String operationDesc;

    @TableField("request_method")
    private String requestMethod;

    @TableField("request_url")
    private String requestUrl;

    @TableField("request_params")
    private String requestParams;

    @TableField("response_result")
    private String responseResult;

    @TableField("client_ip")
    private String clientIp;

    @TableField("user_agent")
    private String userAgent;

    @TableField("execution_time")
    private Long executionTime; // 执行时间（毫秒）

    @TableField("status")
    @EnumValue
    private LogStatus status;

    @TableField("error_message")
    private String errorMessage;

    @TableField("operation_time")
    private LocalDateTime operationTime;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void markAsSuccess(String responseResult, Long executionTime) {
        this.responseResult = responseResult;
        this.executionTime = executionTime;
        this.status = LogStatus.SUCCESS;
    }
    
    public void markAsFailure(String errorMessage, Long executionTime) {
        this.errorMessage = errorMessage;
        this.executionTime = executionTime;
        this.status = LogStatus.FAILURE;
    }
}

public enum LogStatus {
    SUCCESS("成功"),
    FAILURE("失败");
    
    private final String description;
    
    LogStatus(String description) {
        this.description = description;
    }
}
```

### 2.4 系统监控实体 (SystemMonitor)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_monitors")
public class SystemMonitor extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("monitor_time")
    private LocalDateTime monitorTime;

    @TableField("cpu_usage")
    private Double cpuUsage;

    @TableField("memory_usage")
    private Double memoryUsage;

    @TableField("disk_usage")
    private Double diskUsage;

    @TableField("active_connections")
    private Integer activeConnections;

    @TableField("request_count")
    private Long requestCount;

    @TableField("error_count")
    private Long errorCount;

    @TableField("avg_response_time")
    private Double avgResponseTime;

    @TableField("jvm_heap_used")
    private Long jvmHeapUsed;

    @TableField("jvm_heap_max")
    private Long jvmHeapMax;

    @TableField("thread_count")
    private Integer threadCount;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public Double getMemoryUsagePercent() {
        if (jvmHeapMax == null || jvmHeapMax == 0) return 0.0;
        return (jvmHeapUsed.doubleValue() / jvmHeapMax.doubleValue()) * 100;
    }
    
    public Double getErrorRate() {
        if (requestCount == null || requestCount == 0) return 0.0;
        return (errorCount.doubleValue() / requestCount.doubleValue()) * 100;
    }
    
    public boolean isHealthy() {
        return cpuUsage < 80.0 && memoryUsage < 80.0 && diskUsage < 80.0;
    }
}
```

## 5. 业务规则引擎设计

### 5.1 规则引擎架构

基于DDD-012业务流程设计完善，系统管理模块集成业务规则引擎：

```java
@Service
public class BusinessRuleEngine {

    @Autowired
    private RuleRepository ruleRepository;

    @Autowired
    private RuleExecutor ruleExecutor;

    @Autowired
    private RuleCache ruleCache;

    /**
     * 执行业务规则组
     */
    public RuleExecutionResult executeRules(String ruleGroup, Object context) {
        // 1. 获取规则组（优先从缓存获取）
        List<BusinessRule> rules = ruleCache.getRules(ruleGroup);
        if (rules == null) {
            rules = ruleRepository.getRulesByGroup(ruleGroup);
            ruleCache.cacheRules(ruleGroup, rules);
        }

        // 2. 过滤启用的规则并按优先级排序
        List<BusinessRule> enabledRules = rules.stream()
            .filter(BusinessRule::isEnabled)
            .sorted(Comparator.comparing(BusinessRule::getPriority))
            .collect(Collectors.toList());

        // 3. 执行规则
        RuleExecutionResult result = new RuleExecutionResult();
        result.setRuleGroup(ruleGroup);
        result.setExecutionTime(LocalDateTime.now());

        for (BusinessRule rule : enabledRules) {
            if (rule.matches(context)) {
                RuleResult ruleResult = ruleExecutor.execute(rule, context);
                result.addRuleResult(ruleResult);

                // 记录规则执行指标
                recordRuleMetrics(rule, ruleResult);

                // 如果是阻断规则且失败，停止执行
                if (rule.isBlocking() && !ruleResult.isSuccess()) {
                    result.setBlocked(true);
                    result.setBlockReason(ruleResult.getErrorMessage());
                    break;
                }

                // 应用权重调整
                if (ruleResult.hasWeightAdjustment()) {
                    result.addWeightAdjustment(rule.getId(), ruleResult.getWeightAdjustment());
                }
            }
        }

        return result;
    }

    /**
     * 动态添加规则
     */
    public void addRule(BusinessRule rule) {
        // 1. 验证规则
        validateRule(rule);

        // 2. 保存规则
        ruleRepository.save(rule);

        // 3. 清除相关缓存
        ruleCache.evictRuleGroup(rule.getRuleGroup());

        // 4. 记录规则变更日志
        auditService.recordRuleChange("ADD", rule);
    }

    /**
     * 动态更新规则
     */
    public void updateRule(BusinessRule rule) {
        // 1. 验证规则
        validateRule(rule);

        // 2. 更新规则
        BusinessRule oldRule = ruleRepository.findById(rule.getId());
        ruleRepository.update(rule);

        // 3. 清除相关缓存
        ruleCache.evictRuleGroup(rule.getRuleGroup());

        // 4. 记录规则变更日志
        auditService.recordRuleChange("UPDATE", oldRule, rule);
    }

    /**
     * 验证规则
     */
    private void validateRule(BusinessRule rule) {
        if (rule.getRuleGroup() == null || rule.getRuleGroup().trim().isEmpty()) {
            throw new IllegalArgumentException("Rule group cannot be empty");
        }

        if (rule.getCondition() == null || rule.getCondition().trim().isEmpty()) {
            throw new IllegalArgumentException("Rule condition cannot be empty");
        }

        if (rule.getAction() == null) {
            throw new IllegalArgumentException("Rule action cannot be null");
        }

        // 验证条件表达式语法
        try {
            expressionParser.parseExpression(rule.getCondition());
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid rule condition syntax: " + e.getMessage());
        }
    }

    /**
     * 记录规则执行指标
     */
    private void recordRuleMetrics(BusinessRule rule, RuleResult result) {
        // 记录规则执行次数
        Counter.builder("business.rule.execution")
            .tag("rule_group", rule.getRuleGroup())
            .tag("rule_id", rule.getId())
            .tag("result", result.isSuccess() ? "SUCCESS" : "FAILURE")
            .register(meterRegistry)
            .increment();

        // 记录规则执行时间
        Timer.builder("business.rule.execution.duration")
            .tag("rule_group", rule.getRuleGroup())
            .tag("rule_id", rule.getId())
            .register(meterRegistry)
            .record(result.getExecutionDuration());
    }
}
```

### 5.2 规则配置管理

```java
@Entity
@Table(name = "business_rules")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessRule {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "rule_id", unique = true, nullable = false)
    private String ruleId;

    @Column(name = "rule_group", nullable = false)
    private String ruleGroup;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "priority", nullable = false)
    private Integer priority;

    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    @Column(name = "blocking", nullable = false)
    private Boolean blocking = false;

    @Column(name = "condition", nullable = false, columnDefinition = "TEXT")
    private String condition;

    @Enumerated(EnumType.STRING)
    @Column(name = "action", nullable = false)
    private RuleAction action;

    @Enumerated(EnumType.STRING)
    @Column(name = "failure_action")
    private RuleAction failureAction;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "weight_adjustment")
    private Double weightAdjustment;

    @Column(name = "parameters", columnDefinition = "JSON")
    private String parameters;

    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 检查规则是否匹配上下文
     */
    public boolean matches(Object context) {
        try {
            // 使用SpEL表达式评估条件
            StandardEvaluationContext evaluationContext = new StandardEvaluationContext(context);
            ExpressionParser parser = new SpelExpressionParser();
            Expression expression = parser.parseExpression(condition);

            Object result = expression.getValue(evaluationContext);
            return Boolean.TRUE.equals(result);

        } catch (Exception e) {
            log.error("Error evaluating rule condition: {}", condition, e);
            return false;
        }
    }
}

/**
 * 规则动作枚举
 */
public enum RuleAction {
    ALLOW_ALLOCATION,
    REJECT_ALLOCATION,
    PREFER_ALLOCATION,
    DEPRIORITIZE_ALLOCATION,
    EXCLUDE_WAREHOUSE,
    SEND_NOTIFICATION,
    RESERVE_INVENTORY,
    ROLLBACK_ALLOCATION,
    ESCALATE,
    MANUAL_INTERVENTION
}
```

## 6. 监控和指标设计

### 6.1 业务指标监控

```java
@Component
public class BusinessMetricsCollector {

    private final MeterRegistry meterRegistry;

    // 库存同步指标
    private final Counter inventorySyncSuccessCounter;
    private final Counter inventorySyncFailureCounter;
    private final Timer inventorySyncDurationTimer;

    // 订单分配指标
    private final Timer orderAllocationTimer;
    private final Counter orderAllocationSuccessCounter;
    private final Counter orderAllocationFailureCounter;
    private final Gauge activeOrdersGauge;

    // 异常处理指标
    private final Counter exceptionCounter;
    private final Timer exceptionHandlingTimer;

    // Saga执行指标
    private final Counter sagaExecutionCounter;
    private final Timer sagaExecutionTimer;
    private final Counter sagaCompensationCounter;

    public BusinessMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 初始化指标
        this.inventorySyncSuccessCounter = Counter.builder("inventory.sync.success")
            .description("库存同步成功次数")
            .register(meterRegistry);

        this.inventorySyncFailureCounter = Counter.builder("inventory.sync.failure")
            .description("库存同步失败次数")
            .register(meterRegistry);

        this.inventorySyncDurationTimer = Timer.builder("inventory.sync.duration")
            .description("库存同步耗时")
            .register(meterRegistry);

        this.orderAllocationTimer = Timer.builder("order.allocation.duration")
            .description("订单分配耗时")
            .register(meterRegistry);

        this.orderAllocationSuccessCounter = Counter.builder("order.allocation.success")
            .description("订单分配成功次数")
            .register(meterRegistry);

        this.orderAllocationFailureCounter = Counter.builder("order.allocation.failure")
            .description("订单分配失败次数")
            .register(meterRegistry);

        this.activeOrdersGauge = Gauge.builder("orders.active")
            .description("当前活跃订单数")
            .register(meterRegistry, this, BusinessMetricsCollector::getActiveOrderCount);

        this.exceptionCounter = Counter.builder("business.exception")
            .description("业务异常计数")
            .tag("type", "unknown")
            .register(meterRegistry);

        this.exceptionHandlingTimer = Timer.builder("exception.handling.duration")
            .description("异常处理耗时")
            .register(meterRegistry);

        this.sagaExecutionCounter = Counter.builder("saga.execution")
            .description("Saga执行计数")
            .register(meterRegistry);

        this.sagaExecutionTimer = Timer.builder("saga.execution.duration")
            .description("Saga执行耗时")
            .register(meterRegistry);

        this.sagaCompensationCounter = Counter.builder("saga.compensation")
            .description("Saga补偿执行计数")
            .register(meterRegistry);
    }

    // 指标记录方法
    public void recordInventorySyncSuccess() {
        inventorySyncSuccessCounter.increment();
    }

    public void recordInventorySyncFailure() {
        inventorySyncFailureCounter.increment();
    }

    public void recordInventorySyncDuration(Duration duration) {
        inventorySyncDurationTimer.record(duration);
    }

    public void recordOrderAllocationSuccess() {
        orderAllocationSuccessCounter.increment();
    }

    public void recordOrderAllocationFailure() {
        orderAllocationFailureCounter.increment();
    }

    public Timer.Sample startOrderAllocationTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordException(BusinessExceptionType type) {
        Counter.builder("business.exception")
            .tag("type", type.name())
            .tag("severity", type.getSeverity().name())
            .register(meterRegistry)
            .increment();
    }

    public void recordSagaExecution(String sagaType, SagaExecutionResult result) {
        sagaExecutionCounter.increment(
            Tags.of(
                "saga_type", sagaType,
                "status", result.getStatus().name()
            )
        );
    }

    private double getActiveOrderCount() {
        // 实际实现中从订单服务获取活跃订单数
        return orderService.getActiveOrderCount();
    }
}
```

### 6.2 告警规则配置

```yaml
# 业务流程告警规则配置
alert-rules:
  # 库存同步告警
  - alert-name: "inventory-sync-failure-rate-high"
    description: "库存同步失败率过高"
    condition: "rate(inventory_sync_failure_total[5m]) / rate(inventory_sync_total[5m]) > 0.05"
    severity: "WARNING"
    duration: "2m"
    labels:
      team: "inventory"
      service: "front-warehouse"
    annotations:
      summary: "库存同步失败率超过5%"
      description: "在过去5分钟内，库存同步失败率为 {{ $value | humanizePercentage }}"
    actions:
      - type: "EMAIL"
        recipients: ["<EMAIL>"]
      - type: "WEBHOOK"
        url: "http://alert-manager/webhook/inventory"

  # 订单分配延迟告警
  - alert-name: "order-allocation-latency-high"
    description: "订单分配延迟过高"
    condition: "histogram_quantile(0.95, order_allocation_duration_seconds) > 5"
    severity: "CRITICAL"
    duration: "1m"
    labels:
      team: "fulfillment"
      service: "front-warehouse"
    annotations:
      summary: "订单分配P95延迟超过5秒"
      description: "订单分配P95延迟为 {{ $value }}秒"
    actions:
      - type: "SMS"
        recipients: ["+86138****1234"]
      - type: "ESCALATION"
        escalation-policy: "on-call-engineer"

  # Saga执行失败告警
  - alert-name: "saga-execution-failure-rate-high"
    description: "Saga执行失败率过高"
    condition: "rate(saga_execution_total{status=\"FAILED\"}[10m]) / rate(saga_execution_total[10m]) > 0.1"
    severity: "HIGH"
    duration: "3m"
    labels:
      team: "platform"
      service: "saga-manager"
    annotations:
      summary: "Saga执行失败率超过10%"
      description: "在过去10分钟内，Saga执行失败率为 {{ $value | humanizePercentage }}"
    actions:
      - type: "SLACK"
        channel: "#platform-alerts"
      - type: "AUTO_ESCALATION"
        escalation-delay: "5m"

  # 系统资源告警
  - alert-name: "system-resource-usage-high"
    description: "系统资源使用率过高"
    condition: "system_cpu_usage > 80 OR system_memory_usage > 80 OR system_disk_usage > 80"
    severity: "WARNING"
    duration: "5m"
    labels:
      team: "infrastructure"
      service: "system-monitor"
    annotations:
      summary: "系统资源使用率过高"
      description: "CPU: {{ $labels.cpu_usage }}%, 内存: {{ $labels.memory_usage }}%, 磁盘: {{ $labels.disk_usage }}%"
    actions:
      - type: "EMAIL"
        recipients: ["<EMAIL>"]
      - type: "AUTO_SCALING"
        scaling-policy: "scale-out"

  # 业务异常告警
  - alert-name: "business-exception-rate-high"
    description: "业务异常率过高"
    condition: "rate(business_exception_total[5m]) > 10"
    severity: "HIGH"
    duration: "2m"
    labels:
      team: "development"
      service: "business-logic"
    annotations:
      summary: "业务异常率过高"
      description: "在过去5分钟内，业务异常发生率为 {{ $value }} 次/分钟"
    actions:
      - type: "SLACK"
        channel: "#dev-alerts"
      - type: "WEBHOOK"
        url: "http://incident-manager/webhook/business-exception"
```

### 6.3 告警服务实现

```java
@Service
public class AlertService {

    @Autowired
    private AlertRuleRepository alertRuleRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private AlertHistoryRepository alertHistoryRepository;

    /**
     * 处理告警
     */
    public void processAlert(AlertEvent alertEvent) {
        // 1. 获取匹配的告警规则
        List<AlertRule> matchingRules = alertRuleRepository.findByCondition(alertEvent.getMetricName());

        for (AlertRule rule : matchingRules) {
            if (evaluateAlertCondition(rule, alertEvent)) {
                // 2. 检查告警抑制
                if (isAlertSuppressed(rule, alertEvent)) {
                    continue;
                }

                // 3. 创建告警记录
                AlertRecord record = createAlertRecord(rule, alertEvent);

                // 4. 执行告警动作
                executeAlertActions(rule, record);

                // 5. 保存告警历史
                alertHistoryRepository.save(record);
            }
        }
    }

    /**
     * 评估告警条件
     */
    private boolean evaluateAlertCondition(AlertRule rule, AlertEvent event) {
        try {
            // 使用表达式引擎评估告警条件
            ExpressionParser parser = new SpelExpressionParser();
            Expression expression = parser.parseExpression(rule.getCondition());

            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariable("value", event.getValue());
            context.setVariable("threshold", rule.getThreshold());
            context.setVariable("labels", event.getLabels());

            Boolean result = expression.getValue(context, Boolean.class);
            return Boolean.TRUE.equals(result);

        } catch (Exception e) {
            log.error("Error evaluating alert condition: {}", rule.getCondition(), e);
            return false;
        }
    }

    /**
     * 检查告警抑制
     */
    private boolean isAlertSuppressed(AlertRule rule, AlertEvent event) {
        // 检查是否在抑制时间窗口内
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime suppressUntil = alertHistoryRepository.getLastAlertTime(rule.getId())
            .plus(rule.getSuppressDuration());

        return now.isBefore(suppressUntil);
    }

    /**
     * 创建告警记录
     */
    private AlertRecord createAlertRecord(AlertRule rule, AlertEvent event) {
        return AlertRecord.builder()
            .ruleId(rule.getId())
            .ruleName(rule.getName())
            .severity(rule.getSeverity())
            .metricName(event.getMetricName())
            .metricValue(event.getValue())
            .threshold(rule.getThreshold())
            .labels(event.getLabels())
            .description(rule.getDescription())
            .alertTime(LocalDateTime.now())
            .status(AlertStatus.ACTIVE)
            .build();
    }

    /**
     * 执行告警动作
     */
    private void executeAlertActions(AlertRule rule, AlertRecord record) {
        for (AlertAction action : rule.getActions()) {
            try {
                switch (action.getType()) {
                    case EMAIL:
                        sendEmailAlert(action, record);
                        break;
                    case SMS:
                        sendSmsAlert(action, record);
                        break;
                    case SLACK:
                        sendSlackAlert(action, record);
                        break;
                    case WEBHOOK:
                        sendWebhookAlert(action, record);
                        break;
                    case ESCALATION:
                        escalateAlert(action, record);
                        break;
                    case AUTO_SCALING:
                        triggerAutoScaling(action, record);
                        break;
                    default:
                        log.warn("Unknown alert action type: {}", action.getType());
                }
            } catch (Exception e) {
                log.error("Failed to execute alert action: {}", action.getType(), e);
            }
        }
    }

    /**
     * 发送邮件告警
     */
    private void sendEmailAlert(AlertAction action, AlertRecord record) {
        EmailNotification notification = EmailNotification.builder()
            .recipients(action.getRecipients())
            .subject(String.format("[%s] %s", record.getSeverity(), record.getRuleName()))
            .content(buildAlertContent(record))
            .priority(mapSeverityToPriority(record.getSeverity()))
            .build();

        notificationService.sendEmail(notification);
    }

    /**
     * 发送短信告警
     */
    private void sendSmsAlert(AlertAction action, AlertRecord record) {
        SmsNotification notification = SmsNotification.builder()
            .recipients(action.getRecipients())
            .content(buildShortAlertContent(record))
            .priority(mapSeverityToPriority(record.getSeverity()))
            .build();

        notificationService.sendSms(notification);
    }

    /**
     * 发送Slack告警
     */
    private void sendSlackAlert(AlertAction action, AlertRecord record) {
        SlackNotification notification = SlackNotification.builder()
            .channel(action.getChannel())
            .content(buildSlackAlertContent(record))
            .color(mapSeverityToColor(record.getSeverity()))
            .build();

        notificationService.sendSlack(notification);
    }

    /**
     * 构建告警内容
     */
    private String buildAlertContent(AlertRecord record) {
        return String.format(
            "告警规则: %s\n" +
            "告警级别: %s\n" +
            "指标名称: %s\n" +
            "当前值: %s\n" +
            "阈值: %s\n" +
            "告警时间: %s\n" +
            "描述: %s\n" +
            "标签: %s",
            record.getRuleName(),
            record.getSeverity(),
            record.getMetricName(),
            record.getMetricValue(),
            record.getThreshold(),
            record.getAlertTime(),
            record.getDescription(),
            record.getLabels()
        );
    }
}
```

## 7. 总结

系统管理模块通过集成业务规则引擎和完善的监控告警机制，为PISP系统提供了强大的管理和监控能力。

**主要特性：**
- **灵活的业务规则引擎**：支持动态规则配置和实时执行
- **全面的业务指标监控**：覆盖库存同步、订单分配、异常处理等关键业务流程
- **智能化的告警机制**：支持多种告警方式和自动化处理
- **完整的审计追踪**：记录所有规则变更和告警历史

通过这些设计完善，PISP系统具备了企业级的管理和监控能力，能够确保系统的稳定运行和业务流程的高效执行。

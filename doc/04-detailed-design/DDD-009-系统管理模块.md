# DDD-009 系统管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-009 |
| 文档名称 | 系统管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

### 1.1 模块职责

系统管理模块负责ERP系统的配置管理、数据备份恢复、操作日志记录和系统监控，确保系统的稳定运行和数据安全。

### 1.2 核心功能

- **系统配置管理**：系统参数配置、业务规则配置、字典数据管理
- **数据备份恢复**：定时数据备份、备份策略管理、数据恢复操作
- **操作日志管理**：用户操作日志、系统日志、审计追踪
- **系统监控**：性能监控、健康检查、告警管理、统计分析

### 1.3 模块架构图

```mermaid
graph TB
    subgraph "系统管理模块"
        A[系统配置管理]
        B[数据备份恢复]
        C[操作日志管理]
        D[系统监控]
    end
    
    subgraph "核心实体"
        E[SystemConfig系统配置]
        F[DataBackup数据备份]
        G[OperationLog操作日志]
        H[SystemMonitor系统监控]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
```

## 2. 实体设计

### 2.1 系统配置实体 (SystemConfig)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_configs")
public class SystemConfig extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("config_key")
    private String configKey;

    @TableField("config_value")
    private String configValue;

    @TableField("config_name")
    private String configName;

    @TableField("config_group")
    private String configGroup;

    @TableField("config_type")
    @EnumValue
    private ConfigType configType;

    @TableField("description")
    private String description;

    @TableField("is_system")
    private Boolean isSystem = false;

    @TableField("sort_order")
    private Integer sortOrder;

    @TableField("status")
    @EnumValue
    private ConfigStatus status;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public boolean isSystemConfig() {
        return Boolean.TRUE.equals(this.isSystem);
    }
    
    public void updateValue(String newValue) {
        this.configValue = newValue;
        this.updateTime = LocalDateTime.now();
    }
}

public enum ConfigType {
    STRING("字符串"),
    NUMBER("数字"),
    BOOLEAN("布尔值"),
    JSON("JSON对象"),
    TEXT("长文本");
    
    private final String description;
    
    ConfigType(String description) {
        this.description = description;
    }
}

public enum ConfigStatus {
    ACTIVE("启用"),
    INACTIVE("禁用");
    
    private final String description;
    
    ConfigStatus(String description) {
        this.description = description;
    }
}
```

### 2.2 数据备份实体 (DataBackup)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_data_backups")
public class DataBackup extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("backup_name")
    private String backupName;

    @TableField("backup_type")
    @EnumValue
    private BackupType backupType;

    @TableField("backup_path")
    private String backupPath;

    @TableField("file_size")
    private Long fileSize;

    @TableField("backup_time")
    private LocalDateTime backupTime;

    @TableField("backup_duration")
    private Long backupDuration; // 备份耗时（秒）

    @TableField("status")
    @EnumValue
    private BackupStatus status;

    @TableField("error_message")
    private String errorMessage;

    @TableField("description")
    private String description;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void markAsCompleted(String backupPath, Long fileSize, Long duration) {
        this.backupPath = backupPath;
        this.fileSize = fileSize;
        this.backupDuration = duration;
        this.status = BackupStatus.COMPLETED;
        this.backupTime = LocalDateTime.now();
    }
    
    public void markAsFailed(String errorMessage) {
        this.status = BackupStatus.FAILED;
        this.errorMessage = errorMessage;
        this.backupTime = LocalDateTime.now();
    }
    
    public String getFormattedFileSize() {
        if (fileSize == null) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
}

public enum BackupType {
    FULL("全量备份"),
    INCREMENTAL("增量备份"),
    MANUAL("手动备份");
    
    private final String description;
    
    BackupType(String description) {
        this.description = description;
    }
}

public enum BackupStatus {
    PENDING("等待中"),
    RUNNING("备份中"),
    COMPLETED("已完成"),
    FAILED("失败");
    
    private final String description;
    
    BackupStatus(String description) {
        this.description = description;
    }
}
```

### 2.3 操作日志实体 (OperationLog)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_operation_logs")
public class OperationLog extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("username")
    private String username;

    @TableField("operation_module")
    private String operationModule;

    @TableField("operation_type")
    private String operationType;

    @TableField("operation_desc")
    private String operationDesc;

    @TableField("request_method")
    private String requestMethod;

    @TableField("request_url")
    private String requestUrl;

    @TableField("request_params")
    private String requestParams;

    @TableField("response_result")
    private String responseResult;

    @TableField("client_ip")
    private String clientIp;

    @TableField("user_agent")
    private String userAgent;

    @TableField("execution_time")
    private Long executionTime; // 执行时间（毫秒）

    @TableField("status")
    @EnumValue
    private LogStatus status;

    @TableField("error_message")
    private String errorMessage;

    @TableField("operation_time")
    private LocalDateTime operationTime;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void markAsSuccess(String responseResult, Long executionTime) {
        this.responseResult = responseResult;
        this.executionTime = executionTime;
        this.status = LogStatus.SUCCESS;
    }
    
    public void markAsFailure(String errorMessage, Long executionTime) {
        this.errorMessage = errorMessage;
        this.executionTime = executionTime;
        this.status = LogStatus.FAILURE;
    }
}

public enum LogStatus {
    SUCCESS("成功"),
    FAILURE("失败");
    
    private final String description;
    
    LogStatus(String description) {
        this.description = description;
    }
}
```

### 2.4 系统监控实体 (SystemMonitor)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_monitors")
public class SystemMonitor extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("monitor_time")
    private LocalDateTime monitorTime;

    @TableField("cpu_usage")
    private Double cpuUsage;

    @TableField("memory_usage")
    private Double memoryUsage;

    @TableField("disk_usage")
    private Double diskUsage;

    @TableField("active_connections")
    private Integer activeConnections;

    @TableField("request_count")
    private Long requestCount;

    @TableField("error_count")
    private Long errorCount;

    @TableField("avg_response_time")
    private Double avgResponseTime;

    @TableField("jvm_heap_used")
    private Long jvmHeapUsed;

    @TableField("jvm_heap_max")
    private Long jvmHeapMax;

    @TableField("thread_count")
    private Integer threadCount;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public Double getMemoryUsagePercent() {
        if (jvmHeapMax == null || jvmHeapMax == 0) return 0.0;
        return (jvmHeapUsed.doubleValue() / jvmHeapMax.doubleValue()) * 100;
    }
    
    public Double getErrorRate() {
        if (requestCount == null || requestCount == 0) return 0.0;
        return (errorCount.doubleValue() / requestCount.doubleValue()) * 100;
    }
    
    public boolean isHealthy() {
        return cpuUsage < 80.0 && memoryUsage < 80.0 && diskUsage < 80.0;
    }
}
```

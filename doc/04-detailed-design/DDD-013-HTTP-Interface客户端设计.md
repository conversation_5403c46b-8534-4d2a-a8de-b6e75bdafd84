# DDD-013 HTTP Interface客户端设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-013 |
| 文档名称 | HTTP Interface客户端设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-07-02 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 完成 |
| 作者 | 系统架构师 |

## 1. 设计概述

### 1.1 技术选型变更

**从Feign客户端迁移到Spring 6 HTTP Interface：**

- **原技术方案**：Spring Cloud OpenFeign
- **新技术方案**：Spring 6 HTTP Interface + RestClient
- **变更原因**：
  - Spring 6原生支持，减少第三方依赖
  - 同步编程模型，更符合传统开发习惯
  - 更灵活的配置和自定义能力
  - 更好的性能和资源利用率

### 1.2 核心优势

**✅ Spring 6 HTTP Interface优势：**

1. **原生支持**：Spring Framework 6.1+原生支持，无需额外依赖
2. **同步编程**：基于RestClient，提供同步编程模型，易于理解和调试
3. **类型安全**：编译时类型检查，减少运行时错误
4. **灵活配置**：支持更细粒度的配置和自定义
5. **性能优越**：基于Apache HttpClient，性能稳定可靠
6. **统一编程模型**：与Spring MVC保持一致的编程模型

## 2. 技术架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "服务消费者"
        A[Controller层]
        B[Service层]
        C[HTTP Interface客户端]
    end
    
    subgraph "HTTP Interface框架"
        D[HttpServiceProxyFactory]
        E[RestClientAdapter]
        F[RestClient]
    end

    subgraph "负载均衡和服务发现"
        G[Spring Cloud LoadBalancer]
        H[Nacos服务注册中心]
    end

    subgraph "服务提供者"
        I[目标微服务]
    end

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
```

### 2.2 核心组件

**主要组件说明：**

1. **HTTP Interface**：定义服务调用接口的注解
2. **HttpServiceProxyFactory**：创建HTTP Interface代理实例
3. **RestClientAdapter**：RestClient适配器
4. **RestClient**：底层HTTP客户端
5. **LoadBalancer**：负载均衡器
6. **ServiceRegistry**：服务注册中心

## 3. 接口定义规范

### 3.1 HTTP Interface注解

**核心注解说明：**

```java
// 类级别注解
@HttpExchange("/api/v1")          // 定义基础路径
public interface ServiceClient {
    
    // 方法级别注解
    @GetExchange("/users/{id}")    // GET请求
    @PostExchange("/users")        // POST请求
    @PutExchange("/users/{id}")    // PUT请求
    @DeleteExchange("/users/{id}") // DELETE请求
    @PatchExchange("/users/{id}")  // PATCH请求
    
    // 参数注解
    @PathVariable Long id          // 路径变量
    @RequestParam String name      // 请求参数
    @RequestBody UserDTO user      // 请求体
    @RequestHeader String token    // 请求头
}
```

### 3.2 标准接口定义

**基础数据服务客户端：**

```java
@HttpExchange("/api/v1")
public interface BaseDataServiceClient {

    // 商品管理
    @GetExchange("/products")
    PageResult<ProductDTO> getProducts(
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "20") Integer size,
        @RequestParam(required = false) String keyword);

    @GetExchange("/products/{id}")
    ProductDTO getProduct(@PathVariable Long id);

    @PostExchange("/products")
    ProductDTO createProduct(@RequestBody CreateProductRequest request);

    @PutExchange("/products/{id}")
    ProductDTO updateProduct(@PathVariable Long id, @RequestBody UpdateProductRequest request);

    @DeleteExchange("/products/{id}")
    void deleteProduct(@PathVariable Long id);

    // 客户管理
    @GetExchange("/customers/{id}")
    CustomerDTO getCustomer(@PathVariable Long id);

    @GetExchange("/customers")
    List<CustomerDTO> getCustomersByIds(@RequestParam List<Long> ids);

    // 供应商管理
    @GetExchange("/suppliers/{id}")
    SupplierDTO getSupplier(@PathVariable Long id);

    @GetExchange("/suppliers/by-product/{productId}")
    List<SupplierDTO> getSuppliersByProduct(@PathVariable Long productId);
}
```

**库存服务客户端：**

```java
@HttpExchange("/api/v1")
public interface InventoryServiceClient {

    // 库存查询
    @GetExchange("/inventory/{productId}")
    InventoryDTO getInventory(@PathVariable Long productId);

    @PostExchange("/inventory/batch-query")
    List<InventoryDTO> batchGetInventory(@RequestBody List<Long> productIds);

    // 库存预留
    @PostExchange("/inventory/reserve")
    ReservationResult reserveInventory(@RequestBody ReserveInventoryRequest request);

    @PostExchange("/inventory/release")
    void releaseInventory(@RequestBody ReleaseInventoryRequest request);

    // 库存调整
    @PostExchange("/inventory/adjust")
    void adjustInventory(@RequestBody AdjustInventoryRequest request);

    // 库存检查
    @GetExchange("/inventory/check/{productId}")
    InventoryCheckResult checkInventoryAvailability(
        @PathVariable Long productId,
        @RequestParam Integer quantity);
}
```

**前置仓服务客户端：**

```java
@HttpExchange("/api/v1")
public interface FrontWarehouseServiceClient {

    // 前置仓管理
    @GetExchange("/front-warehouses")
    List<FrontWarehouseDTO> getAllFrontWarehouses();

    @GetExchange("/front-warehouses/{id}")
    FrontWarehouseDTO getFrontWarehouse(@PathVariable Long id);

    @GetExchange("/front-warehouses/by-location")
    List<FrontWarehouseDTO> getFrontWarehousesByLocation(
        @RequestParam Double latitude,
        @RequestParam Double longitude,
        @RequestParam Double radius);

    // 库存分配
    @PostExchange("/inventory/allocate")
    AllocationResult allocateInventory(@RequestBody AllocateInventoryRequest request);

    @GetExchange("/inventory/{warehouseId}/{productId}")
    FrontWarehouseInventoryDTO getWarehouseInventory(
        @PathVariable Long warehouseId,
        @PathVariable Long productId);

    // 订单履约
    @PostExchange("/orders/{orderId}/allocate")
    OrderAllocationResult allocateOrder(@PathVariable Long orderId);

    @GetExchange("/orders/{orderId}/fulfillment-status")
    OrderFulfillmentStatus getOrderFulfillmentStatus(@PathVariable Long orderId);

    // 拣选任务
    @PostExchange("/picking-tasks")
    PickingTaskDTO createPickingTask(@RequestBody CreatePickingTaskRequest request);

    @PutExchange("/picking-tasks/{id}/complete")
    void completePickingTask(@PathVariable Long id, @RequestBody CompletePickingTaskRequest request);

    // 配送任务
    @PostExchange("/delivery-tasks")
    DeliveryTaskDTO createDeliveryTask(@RequestBody CreateDeliveryTaskRequest request);

    @GetExchange("/delivery-tasks/{id}/status")
    DeliveryTaskStatus getDeliveryTaskStatus(@PathVariable Long id);
}
```

## 4. 配置管理

### 4.1 RestClient配置

**全局RestClient配置：**

```java
@Configuration
@EnableConfigurationProperties(HttpClientProperties.class)
public class RestClientConfiguration {

    @Bean
    @LoadBalanced
    public RestClient.Builder loadBalancedRestClientBuilder() {
        return RestClient.builder()
            .defaultHeader(HttpHeaders.USER_AGENT, "PISP-System/1.0")
            .defaultHeader("X-Request-Source", "PISP-Internal")
            .requestInterceptor(this::addRequestIdInterceptor)
            .requestInterceptor(this::addLoggingInterceptor)
            .messageConverters(this::configureMessageConverters);
    }

    private void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 配置JSON消息转换器
        MappingJackson2HttpMessageConverter jsonConverter =
            new MappingJackson2HttpMessageConverter(createObjectMapper());
        jsonConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.APPLICATION_JSON,
            MediaType.APPLICATION_JSON_UTF8
        ));
        converters.add(jsonConverter);

        // 配置字符串转换器
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringConverter.setWriteAcceptCharset(false);
        converters.add(stringConverter);
    }

    private ClientHttpResponse addRequestIdInterceptor(
            HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

        String requestId = MDC.get("requestId");
        if (requestId == null) {
            requestId = UUID.randomUUID().toString();
        }

        // 添加追踪头
        request.getHeaders().add("X-Request-ID", requestId);
        request.getHeaders().add("X-Trace-ID", MDC.get("traceId"));
        request.getHeaders().add("X-User-ID", MDC.get("userId"));

        return execution.execute(request, body);
    }

    private ClientHttpResponse addLoggingInterceptor(
            HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

        long startTime = System.currentTimeMillis();

        if (log.isDebugEnabled()) {
            log.debug("HTTP请求: {} {} - Headers: {}",
                request.getMethod(),
                request.getURI(),
                request.getHeaders().toSingleValueMap());
        }

        try {
            ClientHttpResponse response = execution.execute(request, body);
            long duration = System.currentTimeMillis() - startTime;

            if (response.getStatusCode().isError()) {
                log.warn("HTTP调用失败: {} {} - {} - 耗时: {}ms",
                    request.getMethod(),
                    request.getURI(),
                    response.getStatusCode(),
                    duration);
            } else if (log.isDebugEnabled()) {
                log.debug("HTTP调用成功: {} {} - {} - 耗时: {}ms",
                    request.getMethod(),
                    request.getURI(),
                    response.getStatusCode(),
                    duration);
            }

            return response;
        } catch (IOException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("HTTP调用异常: {} {} - {} - 耗时: {}ms",
                request.getMethod(),
                request.getURI(),
                e.getMessage(),
                duration);
            throw e;
        }
    }

    private ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.registerModule(new JavaTimeModule());

        // 配置日期格式
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        return mapper;
    }
}
```

### 4.2 HTTP Interface客户端配置

**客户端Bean配置：**

```java
@Configuration
@EnableConfigurationProperties(ServiceClientProperties.class)
public class HttpInterfaceClientConfiguration {

    private final RestClient.Builder restClientBuilder;
    private final ServiceClientProperties properties;

    public HttpInterfaceClientConfiguration(
            RestClient.Builder restClientBuilder,
            ServiceClientProperties properties) {
        this.restClientBuilder = restClientBuilder;
        this.properties = properties;
    }

    @Bean
    public BaseDataServiceClient baseDataServiceClient() {
        return createClient(
            BaseDataServiceClient.class,
            properties.getBaseDataService()
        );
    }

    @Bean
    public InventoryServiceClient inventoryServiceClient() {
        return createClient(
            InventoryServiceClient.class,
            properties.getInventoryService()
        );
    }

    @Bean
    public FrontWarehouseServiceClient frontWarehouseServiceClient() {
        return createClient(
            FrontWarehouseServiceClient.class,
            properties.getFrontWarehouseService()
        );
    }

    @Bean
    public FinanceServiceClient financeServiceClient() {
        return createClient(
            FinanceServiceClient.class,
            properties.getFinanceService()
        );
    }

    private <T> T createClient(Class<T> clientClass, ServiceClientProperties.ServiceConfig config) {
        RestClient restClient = restClientBuilder
            .baseUrl(config.getBaseUrl())
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .requestInterceptor((request, body, execution) -> {
                // 添加服务特定的头信息
                request.getHeaders().add("X-Target-Service", clientClass.getSimpleName());
                request.getHeaders().add("X-Client-Version", "1.0");

                // 设置超时时间
                if (config.getConnectTimeout() != null) {
                    // RestClient的超时配置通过底层HTTP客户端设置
                }

                return execution.execute(request, body);
            })
            .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
            .builderFor(RestClientAdapter.create(restClient))
            .build();

        return factory.createClient(clientClass);
    }
}
```

### 4.3 配置属性

**服务客户端配置属性：**

```java
@ConfigurationProperties(prefix = "pisp.service-clients")
@Data
public class ServiceClientProperties {

    private ServiceConfig baseDataService = new ServiceConfig();
    private ServiceConfig inventoryService = new ServiceConfig();
    private ServiceConfig frontWarehouseService = new ServiceConfig();
    private ServiceConfig financeService = new ServiceConfig();
    private ServiceConfig salesService = new ServiceConfig();
    private ServiceConfig purchaseService = new ServiceConfig();
    private ServiceConfig reportService = new ServiceConfig();
    private ServiceConfig systemService = new ServiceConfig();
    private ServiceConfig retailService = new ServiceConfig();

    @Data
    public static class ServiceConfig {
        private String baseUrl;
        private Duration connectTimeout = Duration.ofSeconds(5);
        private Duration readTimeout = Duration.ofSeconds(30);
        private Duration writeTimeout = Duration.ofSeconds(30);
        private int maxRetries = 3;
        private boolean enableCircuitBreaker = true;
        private boolean enableMetrics = true;
        private Map<String, String> defaultHeaders = new HashMap<>();
    }
}
```

**YAML配置示例：**

```yaml
# application.yml
pisp:
  service-clients:
    base-data-service:
      base-url: http://pisp-base-data-service
      connect-timeout: 5s
      read-timeout: 30s
      write-timeout: 30s
      max-retries: 3
      enable-circuit-breaker: true
      enable-metrics: true
      default-headers:
        X-Service-Name: "base-data-service"
        X-API-Version: "v1"

    inventory-service:
      base-url: http://pisp-inventory-service
      connect-timeout: 5s
      read-timeout: 30s
      max-retries: 3

    front-warehouse-service:
      base-url: http://pisp-front-warehouse-service
      connect-timeout: 5s
      read-timeout: 30s
      max-retries: 3

    finance-service:
      base-url: http://pisp-finance-service
      connect-timeout: 5s
      read-timeout: 30s
      max-retries: 3

# Spring Cloud LoadBalancer配置
spring:
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
      cache:
        enabled: true
        ttl: 35s
        capacity: 256
      health-check:
        initial-delay: 0s
        interval: 25s
```

## 5. 错误处理和重试机制

### 5.1 统一异常处理

**HTTP Interface异常处理：**

```java
@Component
public class HttpInterfaceExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(HttpInterfaceExceptionHandler.class);

    @EventListener
    public void handleRestClientException(RestClientResponseException ex) {
        HttpStatus status = HttpStatus.valueOf(ex.getStatusCode().value());
        String responseBody = ex.getResponseBodyAsString();

        log.error("HTTP调用异常: {} - {} - {}",
            ex.getStatusCode(),
            status,
            responseBody);

        // 根据状态码进行不同处理
        switch (status) {
            case NOT_FOUND:
                throw new ResourceNotFoundException("资源不存在，状态码: " + status);
            case BAD_REQUEST:
                throw new BadRequestException("请求参数错误: " + responseBody);
            case UNAUTHORIZED:
                throw new UnauthorizedException("认证失败");
            case FORBIDDEN:
                throw new ForbiddenException("权限不足");
            case INTERNAL_SERVER_ERROR:
                throw new ServiceUnavailableException("服务内部错误");
            case SERVICE_UNAVAILABLE:
                throw new ServiceUnavailableException("服务不可用");
            case GATEWAY_TIMEOUT:
                throw new ServiceTimeoutException("网关超时");
            case TOO_MANY_REQUESTS:
                throw new RateLimitException("请求频率过高");
            default:
                throw new HttpClientException("HTTP调用失败: " + status);
        }
    }

    @EventListener
    public void handleResourceAccessException(ResourceAccessException ex) {
        log.error("HTTP连接异常: {}", ex.getMessage());

        if (ex.getCause() instanceof ConnectTimeoutException) {
            throw new ServiceTimeoutException("连接超时");
        } else if (ex.getCause() instanceof SocketTimeoutException) {
            throw new ServiceTimeoutException("读取超时");
        } else {
            throw new ServiceUnavailableException("网络连接异常: " + ex.getMessage());
        }
    }
}
```

### 5.2 重试机制

**自定义重试配置：**

```java
@Configuration
public class RetryConfiguration {

    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();

        // 重试策略
        FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
        backOffPolicy.setBackOffPeriod(1000); // 1秒
        retryTemplate.setBackOffPolicy(backOffPolicy);

        // 重试条件
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(3);

        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(ResourceAccessException.class, true);
        retryableExceptions.put(ConnectTimeoutException.class, true);
        retryableExceptions.put(SocketTimeoutException.class, true);
        retryableExceptions.put(RestClientException.class, true);
        retryPolicy.setRetryableExceptions(retryableExceptions);

        retryTemplate.setRetryPolicy(retryPolicy);

        return retryTemplate;
    }
}
```

**在服务中使用重试：**

```java
@Service
public class OrderService {

    private final BaseDataServiceClient baseDataServiceClient;
    private final RetryTemplate retryTemplate;

    public OrderService(BaseDataServiceClient baseDataServiceClient, RetryTemplate retryTemplate) {
        this.baseDataServiceClient = baseDataServiceClient;
        this.retryTemplate = retryTemplate;
    }

    public ProductDTO getProductWithRetry(Long productId) {
        return retryTemplate.execute(context -> {
            log.debug("尝试获取商品信息，第{}次重试", context.getRetryCount() + 1);
            return baseDataServiceClient.getProduct(productId);
        });
    }
}
```

## 6. 监控和指标

### 6.1 Micrometer集成

**HTTP客户端指标配置：**

```java
@Configuration
public class HttpClientMetricsConfiguration {

    @Bean
    public RestClientCustomizer restClientMetricsCustomizer(MeterRegistry meterRegistry) {
        return restClientBuilder -> {
            restClientBuilder.requestInterceptor((request, body, execution) -> {
                Timer.Sample sample = Timer.start(meterRegistry);
                String serviceName = extractServiceName(request.getURI());

                try {
                    ClientHttpResponse response = execution.execute(request, body);

                    // 记录成功指标
                    sample.stop(Timer.builder("http.client.requests")
                        .description("HTTP客户端请求耗时")
                        .tag("service", serviceName)
                        .tag("method", request.getMethod().name())
                        .tag("status", String.valueOf(response.getStatusCode().value()))
                        .tag("outcome", response.getStatusCode().is2xxSuccessful() ? "SUCCESS" : "ERROR")
                        .register(meterRegistry));

                    // 记录请求计数
                    Counter.builder("http.client.requests.total")
                        .description("HTTP客户端请求总数")
                        .tag("service", serviceName)
                        .tag("method", request.getMethod().name())
                        .tag("status", String.valueOf(response.getStatusCode().value()))
                        .register(meterRegistry)
                        .increment();

                    return response;
                } catch (Exception e) {
                    // 记录异常指标
                    sample.stop(Timer.builder("http.client.requests")
                        .description("HTTP客户端请求耗时")
                        .tag("service", serviceName)
                        .tag("method", request.getMethod().name())
                        .tag("status", "UNKNOWN")
                        .tag("outcome", "ERROR")
                        .register(meterRegistry));

                    Counter.builder("http.client.requests.errors")
                        .description("HTTP客户端请求错误数")
                        .tag("service", serviceName)
                        .tag("method", request.getMethod().name())
                        .tag("exception", e.getClass().getSimpleName())
                        .register(meterRegistry)
                        .increment();

                    throw e;
                }
            });
        };
    }

    private String extractServiceName(URI uri) {
        String host = uri.getHost();
        if (host != null && host.startsWith("pisp-")) {
            return host.substring(5); // 移除 "pisp-" 前缀
        }
        return host != null ? host : "unknown";
    }

    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }
}
```

**自定义指标收集：**

```java
@Component
public class HttpInterfaceMetrics {

    private final Counter httpRequestCounter;
    private final Timer httpRequestTimer;
    private final Gauge activeConnectionsGauge;

    public HttpInterfaceMetrics(MeterRegistry meterRegistry) {
        this.httpRequestCounter = Counter.builder("http.client.requests")
            .description("HTTP客户端请求总数")
            .register(meterRegistry);

        this.httpRequestTimer = Timer.builder("http.client.request.duration")
            .description("HTTP客户端请求耗时")
            .register(meterRegistry);

        this.activeConnectionsGauge = Gauge.builder("http.client.connections.active")
            .description("活跃连接数")
            .register(meterRegistry, this, HttpInterfaceMetrics::getActiveConnections);
    }

    public void recordRequest(String service, String method, String status, Duration duration) {
        httpRequestCounter.increment(
            Tags.of(
                "service", service,
                "method", method,
                "status", status
            )
        );

        httpRequestTimer.record(duration,
            Tags.of(
                "service", service,
                "method", method
            )
        );
    }

    private double getActiveConnections() {
        // 实现获取活跃连接数的逻辑
        return 0.0;
    }
}
```

## 7. 测试支持

### 7.1 Mock测试

**HTTP Interface Mock配置：**

```java
@TestConfiguration
public class HttpInterfaceTestConfiguration {

    @Bean
    @Primary
    public BaseDataServiceClient mockBaseDataServiceClient() {
        return Mockito.mock(BaseDataServiceClient.class);
    }

    @Bean
    @Primary
    public InventoryServiceClient mockInventoryServiceClient() {
        return Mockito.mock(InventoryServiceClient.class);
    }

    @Bean
    @Primary
    public FrontWarehouseServiceClient mockFrontWarehouseServiceClient() {
        return Mockito.mock(FrontWarehouseServiceClient.class);
    }
}
```

**单元测试示例：**

```java
@ExtendWith(MockitoExtension.class)
class OrderServiceTest {

    @Mock
    private BaseDataServiceClient baseDataServiceClient;

    @Mock
    private InventoryServiceClient inventoryServiceClient;

    @InjectMocks
    private OrderService orderService;

    @Test
    void testCreateOrder() {
        // Given
        Long productId = 1L;
        ProductDTO product = ProductDTO.builder()
            .id(productId)
            .name("测试商品")
            .price(BigDecimal.valueOf(100))
            .build();

        when(baseDataServiceClient.getProduct(productId)).thenReturn(product);

        InventoryDTO inventory = InventoryDTO.builder()
            .productId(productId)
            .availableQuantity(100)
            .build();

        when(inventoryServiceClient.getInventory(productId)).thenReturn(inventory);

        // When
        CreateOrderRequest request = CreateOrderRequest.builder()
            .customerId(1L)
            .items(List.of(OrderItemRequest.builder()
                .productId(productId)
                .quantity(2)
                .build()))
            .build();

        OrderDTO result = orderService.createOrder(request);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getItems()).hasSize(1);

        verify(baseDataServiceClient).getProduct(productId);
        verify(inventoryServiceClient).getInventory(productId);
    }
}
```

### 7.2 集成测试

**WireMock集成测试：**

```java
@SpringBootTest
@TestPropertySource(properties = {
    "pisp.service-clients.base-data-service.base-url=http://localhost:8089",
    "pisp.service-clients.inventory-service.base-url=http://localhost:8089"
})
class HttpInterfaceIntegrationTest {

    @RegisterExtension
    static WireMockExtension wireMock = WireMockExtension.newInstance()
        .options(wireMockConfig().port(8089))
        .build();

    @Autowired
    private BaseDataServiceClient baseDataServiceClient;

    @Test
    void testGetProduct() {
        // Given
        Long productId = 1L;
        wireMock.stubFor(get(urlEqualTo("/api/v1/products/" + productId))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("Content-Type", "application/json")
                .withBody("""
                    {
                        "id": 1,
                        "name": "测试商品",
                        "price": 100.00
                    }
                    """)));

        // When
        ProductDTO result = baseDataServiceClient.getProduct(productId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(productId);
        assertThat(result.getName()).isEqualTo("测试商品");
        assertThat(result.getPrice()).isEqualTo(BigDecimal.valueOf(100.00));
    }
}
```

## 8. 迁移指南

### 8.1 从Feign迁移步骤

**迁移步骤：**

1. **更新依赖**
   ```xml
   <!-- 移除Feign依赖 -->
   <!--
   <dependency>
       <groupId>org.springframework.cloud</groupId>
       <artifactId>spring-cloud-starter-openfeign</artifactId>
   </dependency>
   -->

   <!-- 添加Spring Web依赖 -->
   <dependency>
       <groupId>org.springframework</groupId>
       <artifactId>spring-web</artifactId>
   </dependency>

   <!-- 添加LoadBalancer依赖 -->
   <dependency>
       <groupId>org.springframework.cloud</groupId>
       <artifactId>spring-cloud-starter-loadbalancer</artifactId>
   </dependency>
   ```

2. **更新接口定义**
   ```java
   // 原Feign接口
   @FeignClient(name = "base-data-service", path = "/api/v1")
   public interface BaseDataServiceClient {
       @GetMapping("/products/{id}")
       ProductDTO getProduct(@PathVariable("id") Long id);
   }

   // 新HTTP Interface
   @HttpExchange("/api/v1")
   public interface BaseDataServiceClient {
       @GetExchange("/products/{id}")
       ProductDTO getProduct(@PathVariable Long id);
   }
   ```

3. **更新配置类**
   ```java
   // 移除@EnableFeignClients注解
   // @EnableFeignClients

   // 添加HTTP Interface配置
   @Configuration
   public class HttpInterfaceConfiguration {
       // 配置代码...
   }
   ```

4. **更新配置文件**
   ```yaml
   # 移除Feign配置
   # feign:
   #   client:
   #     config:
   #       default:
   #         connectTimeout: 5000
   #         readTimeout: 30000

   # 添加HTTP Interface配置
   pisp:
     service-clients:
       base-data-service:
         base-url: http://pisp-base-data-service
         connect-timeout: 5s
         read-timeout: 30s
   ```

### 8.2 兼容性注意事项

**注意事项：**

1. **注解差异**：`@FeignClient` → `@HttpExchange`，`@GetMapping` → `@GetExchange`
2. **参数绑定**：`@PathVariable("id")` → `@PathVariable`（可省略value）
3. **配置方式**：从Feign配置迁移到WebClient配置
4. **错误处理**：从FeignException迁移到RestClientException
5. **重试机制**：需要重新配置重试策略
6. **同步模型**：从异步响应式模型迁移到同步阻塞模型

## 9. 最佳实践

### 9.1 接口设计原则

**设计原则：**

1. **接口职责单一**：每个客户端接口只负责一个服务的调用
2. **方法命名清晰**：方法名应该清楚表达业务意图
3. **参数类型安全**：使用强类型参数，避免Map传参
4. **返回值明确**：返回值类型应该明确，避免使用Object
5. **异常处理统一**：统一的异常处理和错误码规范

### 9.2 性能优化建议

**优化建议：**

1. **连接池配置**：合理配置连接池大小和超时时间
2. **缓存策略**：对不经常变化的数据进行适当缓存
3. **批量操作**：尽量使用批量接口减少网络调用
4. **异步调用**：对于非关键路径使用异步调用
5. **监控告警**：建立完善的监控和告警机制

### 9.3 安全考虑

**安全措施：**

1. **认证传递**：确保认证信息正确传递
2. **HTTPS通信**：生产环境使用HTTPS
3. **敏感数据保护**：避免在日志中记录敏感信息
4. **访问控制**：实施适当的访问控制策略
5. **审计日志**：记录重要操作的审计日志

## 10. 总结

### 10.1 技术优势总结

通过从Feign迁移到Spring 6 HTTP Interface + RestClient，PISP系统获得了以下优势：

**✅ 技术优势：**
- 减少第三方依赖，提高系统稳定性
- 同步编程模型，更符合传统开发习惯
- 更灵活的配置和自定义能力
- 更好的性能和资源利用率
- 与Spring生态系统更好的集成

**✅ 开发优势：**
- 统一的编程模型和注解体系
- 更好的类型安全和编译时检查
- 更简洁的配置和使用方式
- 更好的测试支持和Mock能力
- 更容易调试和排查问题

### 10.2 实施建议

**实施建议：**
1. 分阶段迁移，先从非核心服务开始
2. 充分测试，确保功能正确性
3. 监控性能指标，确保性能不下降
4. 培训开发团队，确保技术掌握
5. 建立最佳实践文档，规范使用方式

通过这次技术升级，PISP系统的服务间通信将更加高效、稳定和易于维护。RestClient的同步编程模型使得代码更容易理解和调试，同时保持了Spring 6 HTTP Interface的所有优势。
```

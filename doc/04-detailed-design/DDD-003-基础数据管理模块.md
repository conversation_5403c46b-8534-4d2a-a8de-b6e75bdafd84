# DDD-002 基础数据管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-002 |
| 文档名称 | 基础数据管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

基础数据管理模块是进销存系统的核心基础模块，负责管理系统中的基础数据，包括商品信息、客户信息、供应商信息、仓库信息和门店信息，为B2B和B2C业务提供统一的主数据支撑。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "基础数据管理模块"
        A[商品管理服务]
        B[客户管理服务]
        C[供应商管理服务]
        D[仓库管理服务]
        E[门店管理服务]
    end

    subgraph "数据层"
        F[商品数据]
        G[客户数据]
        H[供应商数据]
        I[仓库数据]
        J[门店数据]
    end

    subgraph "业务支撑"
        K[B2B采购销售]
        L[B2C零售业务]
        M[库存管理]
        N[财务管理]
    end

    A --> F
    B --> G
    C --> H
    D --> I
    E --> J

    F --> K
    F --> L
    G --> K
    I --> M
    J --> L
```

## 2. 商品信息管理

### 2.1 商品实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("products")
public class Product extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("product_code")
    private String productCode;

    @TableField("product_name")
    private String productName;

    @TableField("category_id")
    private Long categoryId;

    @TableField("unit")
    private String unit;

    @TableField("cost_price")
    private BigDecimal costPrice;
    
    @TableField("sale_price")
    private BigDecimal salePrice;

    @TableField("description")
    private String description;

    @TableField("status")
    @EnumValue
    private ProductStatus status;

    @TableField("barcode")
    private String barcode;

    @TableField("brand")
    private String brand;

    @TableField("model")
    private String model;

    @TableField("weight")
    private BigDecimal weight;

    @TableField("volume")
    private BigDecimal volume;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void updatePrice(BigDecimal costPrice, BigDecimal salePrice) {
        this.costPrice = costPrice;
        this.salePrice = salePrice;
    }
    
    public void activate() {
        this.status = ProductStatus.ACTIVE;
    }
    
    public void deactivate() {
        this.status = ProductStatus.INACTIVE;
    }
    
    public boolean isActive() {
        return ProductStatus.ACTIVE.equals(this.status);
    }
}
```

### 2.2 商品分类实体

```java
@Entity
@Table(name = "product_categories")
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductCategory extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "category_name", nullable = false)
    private String categoryName;
    
    @Column(name = "category_code", unique = true)
    private String categoryCode;
    
    @Column(name = "parent_id")
    private Long parentId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private ProductCategory parent;
    
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL)
    private List<ProductCategory> children = new ArrayList<>();
    
    @Column(name = "level")
    private Integer level;
    
    @Column(name = "sort_order")
    private Integer sortOrder;
    
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private CategoryStatus status;
    
    // 业务方法
    public boolean isLeafCategory() {
        return children == null || children.isEmpty();
    }
    
    public void addChild(ProductCategory child) {
        child.setParent(this);
        child.setParentId(this.id);
        child.setLevel(this.level + 1);
        this.children.add(child);
    }
}
```

### 2.3 商品服务实现

```java
@Service
@Transactional
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {
    
    @Autowired
    private ProductCategoryService categoryService;
    
    @Autowired
    private ProductCodeGenerator codeGenerator;
    
    @Override
    public ProductDTO createProduct(CreateProductRequest request) {
        // 1. 验证分类是否存在
        ProductCategory category = categoryService.getById(request.getCategoryId());
        if (category == null) {
            throw new BusinessException("商品分类不存在");
        }
        
        // 2. 生成商品编码
        String productCode = request.getProductCode();
        if (StringUtils.isEmpty(productCode)) {
            productCode = codeGenerator.generateProductCode(request.getCategoryId());
        }
        
        // 3. 检查编码唯一性
        if (existsByProductCode(productCode)) {
            throw new BusinessException("商品编码已存在");
        }
        
        // 4. 创建商品
        Product product = new Product();
        BeanUtils.copyProperties(request, product);
        product.setProductCode(productCode);
        product.setStatus(ProductStatus.ACTIVE);
        
        // 5. 保存商品
        this.save(product);
        
        // 6. 处理商品属性
        if (CollectionUtils.isNotEmpty(request.getAttributes())) {
            saveProductAttributes(product.getId(), request.getAttributes());
        }
        
        // 7. 处理商品图片
        if (CollectionUtils.isNotEmpty(request.getImageUrls())) {
            saveProductImages(product.getId(), request.getImageUrls());
        }
        
        return convertToDTO(product);
    }
    
    @Override
    public IPage<ProductDTO> getProducts(ProductQueryRequest request) {
        Page<Product> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(request.getKeyword()), 
                    Product::getProductName, request.getKeyword())
               .or()
               .like(StringUtils.hasText(request.getKeyword()), 
                    Product::getProductCode, request.getKeyword())
               .eq(request.getCategoryId() != null, 
                   Product::getCategoryId, request.getCategoryId())
               .eq(request.getStatus() != null, 
                   Product::getStatus, request.getStatus())
               .between(request.getMinPrice() != null && request.getMaxPrice() != null,
                       Product::getSalePrice, request.getMinPrice(), request.getMaxPrice())
               .orderByDesc(Product::getCreateTime);
        
        IPage<Product> productPage = this.page(page, wrapper);
        return productPage.convert(this::convertToDTO);
    }
    
    @Override
    public void batchImportProducts(List<ProductImportDTO> importList) {
        List<Product> products = new ArrayList<>();
        
        for (ProductImportDTO importDTO : importList) {
            // 验证数据
            validateImportData(importDTO);
            
            // 转换为实体
            Product product = convertFromImportDTO(importDTO);
            products.add(product);
        }
        
        // 批量保存
        this.saveBatch(products);
    }
    
    @Override
    public void batchUpdatePrices(List<ProductPriceUpdateDTO> priceUpdates) {
        for (ProductPriceUpdateDTO update : priceUpdates) {
            Product product = this.getById(update.getProductId());
            if (product != null) {
                product.updatePrice(update.getCostPrice(), update.getSalePrice());
                this.updateById(product);
            }
        }
    }
    
    private void validateImportData(ProductImportDTO importDTO) {
        if (StringUtils.isEmpty(importDTO.getProductName())) {
            throw new BusinessException("商品名称不能为空");
        }
        if (importDTO.getCostPrice() != null && importDTO.getCostPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("成本价格不能为负数");
        }
        if (importDTO.getSalePrice() != null && importDTO.getSalePrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("销售价格不能为负数");
        }
    }
    
    private Product convertFromImportDTO(ProductImportDTO importDTO) {
        Product product = new Product();
        BeanUtils.copyProperties(importDTO, product);
        
        // 生成商品编码
        if (StringUtils.isEmpty(product.getProductCode())) {
            product.setProductCode(codeGenerator.generateProductCode(product.getCategoryId()));
        }
        
        product.setStatus(ProductStatus.ACTIVE);
        return product;
    }
    
    private void saveProductAttributes(Long productId, List<ProductAttributeDTO> attributes) {
        for (ProductAttributeDTO attrDTO : attributes) {
            ProductAttribute attribute = new ProductAttribute();
            attribute.setProductId(productId);
            attribute.setAttributeName(attrDTO.getAttributeName());
            attribute.setAttributeValue(attrDTO.getAttributeValue());
            productAttributeService.save(attribute);
        }
    }
    
    private void saveProductImages(Long productId, List<String> imageUrls) {
        for (int i = 0; i < imageUrls.size(); i++) {
            ProductImage image = new ProductImage();
            image.setProductId(productId);
            image.setImageUrl(imageUrls.get(i));
            image.setSortOrder(i + 1);
            image.setIsPrimary(i == 0);
            productImageService.save(image);
        }
    }
}
```

## 3. 客户信息管理

### 3.1 客户实体设计

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("customers")
public class Customer extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("customer_code")
    private String customerCode;

    @TableField("customer_name")
    private String customerName;

    @TableField("customer_type")
    @EnumValue
    private CustomerType customerType; // INDIVIDUAL, ENTERPRISE

    @TableField("contact_person")
    private String contactPerson;

    @TableField("phone")
    private String phone;

    @TableField("email")
    private String email;

    @TableField("address")
    private String address;

    @TableField("tax_number")
    private String taxNumber;

    @TableField("credit_limit")
    private BigDecimal creditLimit;

    @TableField("credit_used")
    private BigDecimal creditUsed = BigDecimal.ZERO;

    @TableField("credit_level")
    @EnumValue
    private CreditLevel creditLevel;

    @TableField("payment_terms")
    private Integer paymentTerms; // 付款期限（天）

    @TableField("status")
    @EnumValue
    private CustomerStatus status;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法
    public BigDecimal getAvailableCredit() {
        return creditLimit.subtract(creditUsed);
    }

    public boolean hasAvailableCredit(BigDecimal amount) {
        return getAvailableCredit().compareTo(amount) >= 0;
    }

    public void occupyCredit(BigDecimal amount) {
        if (!hasAvailableCredit(amount)) {
            throw new BusinessException("客户信用额度不足");
        }
        this.creditUsed = this.creditUsed.add(amount);
    }

    public void releaseCredit(BigDecimal amount) {
        this.creditUsed = this.creditUsed.subtract(amount);
        if (this.creditUsed.compareTo(BigDecimal.ZERO) < 0) {
            this.creditUsed = BigDecimal.ZERO;
        }
    }
}
```

### 3.2 客户服务实现

```java
@Service
@Transactional
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer> implements CustomerService {

    @Autowired
    private CustomerCodeGenerator codeGenerator;

    @Override
    public CustomerDTO createCustomer(CreateCustomerRequest request) {
        // 1. 生成客户编码
        String customerCode = request.getCustomerCode();
        if (StringUtils.isEmpty(customerCode)) {
            customerCode = codeGenerator.generateCustomerCode(request.getCustomerType());
        }

        // 2. 检查编码唯一性
        if (existsByCustomerCode(customerCode)) {
            throw new BusinessException("客户编码已存在");
        }

        // 3. 创建客户
        Customer customer = new Customer();
        BeanUtils.copyProperties(request, customer);
        customer.setCustomerCode(customerCode);
        customer.setStatus(CustomerStatus.ACTIVE);
        customer.setCreditUsed(BigDecimal.ZERO);

        // 4. 保存客户
        this.save(customer);

        // 5. 保存联系人信息
        if (CollectionUtils.isNotEmpty(request.getContacts())) {
            saveCustomerContacts(customer.getId(), request.getContacts());
        }

        return convertToDTO(customer);
    }

    @Override
    public void updateCreditLimit(Long customerId, BigDecimal newCreditLimit) {
        Customer customer = this.getById(customerId);
        if (customer == null) {
            throw new BusinessException("客户不存在");
        }

        if (newCreditLimit.compareTo(customer.getCreditUsed()) < 0) {
            throw new BusinessException("新信用额度不能小于已使用额度");
        }

        customer.setCreditLimit(newCreditLimit);
        this.updateById(customer);
    }

    @Override
    public IPage<CustomerDTO> getCustomers(CustomerQueryRequest request) {
        Page<Customer> page = new Page<>(request.getPageNum(), request.getPageSize());

        LambdaQueryWrapper<Customer> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(request.getKeyword()),
                    Customer::getCustomerName, request.getKeyword())
               .or()
               .like(StringUtils.hasText(request.getKeyword()),
                    Customer::getCustomerCode, request.getKeyword())
               .eq(request.getCustomerType() != null,
                   Customer::getCustomerType, request.getCustomerType())
               .eq(request.getStatus() != null,
                   Customer::getStatus, request.getStatus())
               .orderByDesc(Customer::getCreateTime);

        IPage<Customer> customerPage = this.page(page, wrapper);
        return customerPage.convert(this::convertToDTO);
    }
}
```

## 4. 供应商管理

### 4.1 供应商实体设计

```java
@Entity
@Table(name = "suppliers")
@Data
@EqualsAndHashCode(callSuper = true)
public class Supplier extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "supplier_code", unique = true, nullable = false)
    private String supplierCode;

    @Column(name = "supplier_name", nullable = false)
    private String supplierName;

    @Enumerated(EnumType.STRING)
    @Column(name = "supplier_type")
    private SupplierType supplierType;

    @Column(name = "contact_person")
    private String contactPerson;

    @Column(name = "phone")
    private String phone;

    @Column(name = "email")
    private String email;

    @Column(name = "address")
    private String address;

    @Column(name = "tax_number")
    private String taxNumber;

    @Column(name = "bank_account")
    private String bankAccount;

    @Column(name = "bank_name")
    private String bankName;

    @Enumerated(EnumType.STRING)
    @Column(name = "rating")
    private SupplierRating rating; // A, B, C, D

    @Column(name = "payment_terms")
    private Integer paymentTerms;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private SupplierStatus status;

    @OneToMany(mappedBy = "supplier", cascade = CascadeType.ALL)
    private List<SupplierProduct> supplierProducts = new ArrayList<>();

    // 业务方法
    public void updateRating(SupplierRating newRating) {
        this.rating = newRating;
    }

    public boolean isActive() {
        return SupplierStatus.ACTIVE.equals(this.status);
    }
}
```

## 5. 事件驱动设计

### 5.1 领域事件定义

```java
// 商品创建事件
@Data
@AllArgsConstructor
public class ProductCreatedEvent extends DomainEvent {
    private Long productId;
    private String productCode;
    private String productName;
    private Long categoryId;
    private BigDecimal costPrice;
    private BigDecimal salePrice;
}

// 客户信用额度变更事件
@Data
@AllArgsConstructor
public class CustomerCreditChangedEvent extends DomainEvent {
    private Long customerId;
    private BigDecimal oldCreditLimit;
    private BigDecimal newCreditLimit;
    private String changeReason;
}

// 供应商状态变更事件
@Data
@AllArgsConstructor
public class SupplierStatusChangedEvent extends DomainEvent {
    private Long supplierId;
    private SupplierStatus oldStatus;
    private SupplierStatus newStatus;
    private String changeReason;
}
```

### 5.2 RocketMQ事件发布

```java
@Component
public class BaseDataEventPublisher {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    private static final String PRODUCT_TOPIC = "base_data_product_events";
    private static final String CUSTOMER_TOPIC = "base_data_customer_events";
    private static final String SUPPLIER_TOPIC = "base_data_supplier_events";

    public void publishProductCreated(ProductCreatedEvent event) {
        rocketMQTemplate.convertAndSend(PRODUCT_TOPIC + ":product_created", event);
    }

    public void publishCustomerCreditChanged(CustomerCreditChangedEvent event) {
        rocketMQTemplate.convertAndSend(CUSTOMER_TOPIC + ":customer_credit_changed", event);
    }

    public void publishSupplierStatusChanged(SupplierStatusChangedEvent event) {
        kafkaTemplate.send(SUPPLIER_TOPIC, "supplier.status.changed", event);
    }
}
```

### 5.3 事件处理器

```java
@Component
@KafkaListener(topics = "base-data.product.events")
public class ProductEventHandler {

    @Autowired
    private InventoryService inventoryService;

    @KafkaHandler
    public void handleProductCreated(ProductCreatedEvent event) {
        // 为新商品初始化库存记录
        inventoryService.initializeInventoryForNewProduct(event.getProductId());
    }
}

@Component
@KafkaListener(topics = "base-data.customer.events")
public class CustomerEventHandler {

    @Autowired
    private NotificationService notificationService;

    @KafkaHandler
    public void handleCustomerCreditChanged(CustomerCreditChangedEvent event) {
        // 发送信用额度变更通知
        notificationService.sendCreditChangeNotification(event);
    }
}
```

## 6. 类图设计

### 6.1 商品管理类图

```mermaid
classDiagram
    class Product {
        +Long id
        +String productCode
        +String productName
        +Long categoryId
        +String unit
        +BigDecimal costPrice
        +BigDecimal salePrice
        +ProductStatus status
        +updatePrice()
        +activate()
        +deactivate()
        +isActive()
    }

    class ProductCategory {
        +Long id
        +String categoryName
        +String categoryCode
        +Long parentId
        +Integer level
        +Integer sortOrder
        +CategoryStatus status
        +isLeafCategory()
        +addChild()
    }

    class ProductAttribute {
        +Long id
        +Long productId
        +String attributeName
        +String attributeValue
        +Integer sortOrder
    }

    class ProductImage {
        +Long id
        +Long productId
        +String imageUrl
        +Integer sortOrder
        +Boolean isPrimary
    }

    class ProductService {
        +createProduct()
        +updateProduct()
        +getProducts()
        +batchImportProducts()
        +batchUpdatePrices()
    }

    ProductCategory --o ProductCategory
    Product o-- ProductCategory
    Product --o ProductAttribute
    Product --o ProductImage
    ProductService ..> Product
    ProductService ..> ProductCategory
```

### 6.2 客户管理时序图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant CS as 客户服务
    participant DB as 数据库
    participant K as Kafka
    participant NS as 通知服务

    C->>CS: 创建客户请求
    CS->>CS: 生成客户编码
    CS->>CS: 验证数据
    CS->>DB: 保存客户信息
    DB-->>CS: 返回客户ID

    CS->>K: 发布客户创建事件
    K-->>NS: 客户创建事件
    NS->>NS: 发送欢迎通知

    CS-->>C: 返回客户信息

    Note over C,NS: 异步通知处理
```

## 6. 门店管理

### 6.1 门店实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("retail_stores")
public class RetailStore extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("store_code")
    private String storeCode;

    @TableField("store_name")
    private String storeName;

    @TableField("store_type")
    @EnumValue
    private StoreType storeType;

    @TableField("address")
    private String address;

    @TableField("phone")
    private String phone;

    @TableField("manager_id")
    private Long managerId;

    @TableField("opening_hours")
    private String openingHours;

    @TableField("business_area")
    private BigDecimal businessArea;

    @TableField("status")
    @EnumValue
    private StoreStatus status;

    @TableField("pos_count")
    private Integer posCount;

    @TableField("warehouse_id")
    private Long warehouseId; // 关联仓库

    @TableField("region")
    private String region; // 所属区域

    @TableField("city")
    private String city; // 所在城市

    @TableField("province")
    private String province; // 所在省份

    @TableField("postal_code")
    private String postalCode; // 邮政编码

    @TableField("email")
    private String email; // 门店邮箱

    @TableField("fax")
    private String fax; // 传真号码

    @TableField("rent_cost")
    private BigDecimal rentCost; // 月租金

    @TableField("decoration_cost")
    private BigDecimal decorationCost; // 装修费用

    @TableField("opening_date")
    private LocalDate openingDate; // 开业日期

    @TableField("contract_start_date")
    private LocalDate contractStartDate; // 合同开始日期

    @TableField("contract_end_date")
    private LocalDate contractEndDate; // 合同结束日期

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法
    public boolean isOperating() {
        return this.status == StoreStatus.OPERATING;
    }

    public boolean isInContract() {
        LocalDate now = LocalDate.now();
        return now.isAfter(contractStartDate) && now.isBefore(contractEndDate);
    }

    public BigDecimal calculateMonthlyOperatingCost() {
        // 计算月度运营成本（租金 + 其他费用）
        return this.rentCost != null ? this.rentCost : BigDecimal.ZERO;
    }
}
```

### 6.2 门店类型枚举

```java
public enum StoreType {
    FLAGSHIP("旗舰店"),
    STANDARD("标准店"),
    MINI("迷你店"),
    OUTLET("奥特莱斯"),
    FRANCHISE("加盟店"),
    POPUP("快闪店");

    private final String description;

    StoreType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
```

### 6.3 门店状态枚举

```java
public enum StoreStatus {
    PLANNING("筹建中"),
    DECORATING("装修中"),
    OPERATING("营业中"),
    SUSPENDED("暂停营业"),
    CLOSED("已关闭"),
    RELOCATED("已迁址");

    private final String description;

    StoreStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
```

### 6.4 门店管理服务

```java
@Service
@Transactional
public class RetailStoreServiceImpl extends ServiceImpl<RetailStoreMapper, RetailStore>
    implements RetailStoreService {

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private UserService userService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    public RetailStoreDTO createStore(CreateStoreRequest request) {
        // 1. 验证门店编码唯一性
        if (existsByStoreCode(request.getStoreCode())) {
            throw new BusinessException("门店编码已存在");
        }

        // 2. 验证关联仓库
        if (request.getWarehouseId() != null) {
            Warehouse warehouse = warehouseService.getById(request.getWarehouseId());
            if (warehouse == null) {
                throw new BusinessException("关联仓库不存在");
            }
        }

        // 3. 验证门店经理
        if (request.getManagerId() != null) {
            User manager = userService.getById(request.getManagerId());
            if (manager == null) {
                throw new BusinessException("门店经理不存在");
            }
        }

        // 4. 创建门店
        RetailStore store = new RetailStore();
        BeanUtils.copyProperties(request, store);
        store.setStatus(StoreStatus.PLANNING);
        store.setOpeningDate(request.getOpeningDate());

        this.save(store);

        // 5. 发布门店创建事件
        eventPublisher.publishEvent(new StoreCreatedEvent(store.getId()));

        return convertToDTO(store);
    }

    @Override
    public RetailStoreDTO updateStore(Long storeId, UpdateStoreRequest request) {
        RetailStore store = this.getById(storeId);
        if (store == null) {
            throw new BusinessException("门店不存在");
        }

        // 更新门店信息
        BeanUtils.copyProperties(request, store);
        this.updateById(store);

        // 发布门店更新事件
        eventPublisher.publishEvent(new StoreUpdatedEvent(storeId));

        return convertToDTO(store);
    }

    @Override
    public void changeStoreStatus(Long storeId, StoreStatus newStatus) {
        RetailStore store = this.getById(storeId);
        if (store == null) {
            throw new BusinessException("门店不存在");
        }

        StoreStatus oldStatus = store.getStatus();
        store.setStatus(newStatus);
        this.updateById(store);

        // 发布状态变更事件
        eventPublisher.publishEvent(new StoreStatusChangedEvent(storeId, oldStatus, newStatus));
    }

    @Override
    public List<RetailStoreDTO> getStoresByRegion(String region) {
        LambdaQueryWrapper<RetailStore> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RetailStore::getRegion, region)
                   .eq(RetailStore::getStatus, StoreStatus.OPERATING);

        List<RetailStore> stores = this.list(queryWrapper);
        return stores.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
    }

    @Override
    public StoreSalesStatisticsDTO getStoreSalesStatistics(Long storeId, LocalDate startDate, LocalDate endDate) {
        // 调用零售服务获取销售统计
        return retailSalesService.getStoreSalesStatistics(storeId, startDate, endDate);
    }

    private boolean existsByStoreCode(String storeCode) {
        LambdaQueryWrapper<RetailStore> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RetailStore::getStoreCode, storeCode);
        return this.count(queryWrapper) > 0;
    }

    private RetailStoreDTO convertToDTO(RetailStore store) {
        RetailStoreDTO dto = new RetailStoreDTO();
        BeanUtils.copyProperties(store, dto);

        // 设置关联信息
        if (store.getManagerId() != null) {
            User manager = userService.getById(store.getManagerId());
            if (manager != null) {
                dto.setManagerName(manager.getRealName());
            }
        }

        if (store.getWarehouseId() != null) {
            Warehouse warehouse = warehouseService.getById(store.getWarehouseId());
            if (warehouse != null) {
                dto.setWarehouseName(warehouse.getWarehouseName());
            }
        }

        return dto;
    }
}
```

### 6.5 门店管理接口

```java
@RestController
@RequestMapping("/api/v1/base-data/stores")
@Tag(name = "门店管理", description = "门店基础数据管理接口")
public class RetailStoreController {

    @Autowired
    private RetailStoreService storeService;

    @PostMapping
    @Operation(summary = "创建门店")
    public Result<RetailStoreDTO> createStore(@RequestBody @Valid CreateStoreRequest request) {
        return Result.success(storeService.createStore(request));
    }

    @GetMapping("/{storeId}")
    @Operation(summary = "查询门店详情")
    public Result<RetailStoreDTO> getStore(@PathVariable Long storeId) {
        return Result.success(storeService.getStoreById(storeId));
    }

    @GetMapping
    @Operation(summary = "查询门店列表")
    public Result<PageResult<RetailStoreDTO>> getStores(@Valid StoreQueryRequest request) {
        return Result.success(storeService.getStores(request));
    }

    @PutMapping("/{storeId}")
    @Operation(summary = "更新门店信息")
    public Result<RetailStoreDTO> updateStore(
            @PathVariable Long storeId,
            @RequestBody @Valid UpdateStoreRequest request) {
        return Result.success(storeService.updateStore(storeId, request));
    }

    @PutMapping("/{storeId}/status")
    @Operation(summary = "变更门店状态")
    public Result<Void> changeStoreStatus(
            @PathVariable Long storeId,
            @RequestBody @Valid ChangeStoreStatusRequest request) {
        storeService.changeStoreStatus(storeId, request.getStatus());
        return Result.success();
    }

    @GetMapping("/region/{region}")
    @Operation(summary = "按区域查询门店")
    public Result<List<RetailStoreDTO>> getStoresByRegion(@PathVariable String region) {
        return Result.success(storeService.getStoresByRegion(region));
    }

    @GetMapping("/{storeId}/statistics")
    @Operation(summary = "查询门店销售统计")
    public Result<StoreSalesStatisticsDTO> getStoreSalesStatistics(
            @PathVariable Long storeId,
            @Valid SalesStatisticsQueryRequest request) {
        return Result.success(storeService.getStoreSalesStatistics(
            storeId, request.getStartDate(), request.getEndDate()));
    }
}
```

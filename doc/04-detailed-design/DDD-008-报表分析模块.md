# DDD-007 报表分析模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-007 |
| 文档名称 | 报表分析模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

报表分析模块负责生成各类业务报表和数据分析，包括B2B业务（销售报表、采购报表、库存报表、财务报表）和B2C零售业务（门店销售报表、会员分析报表、促销效果报表）的全面数据分析。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "报表分析模块"
        A[销售报表服务]
        B[采购报表服务]
        C[库存报表服务]
        D[财务报表服务]
        E[零售报表服务]
        F[数据分析服务]
    end

    subgraph "B2B报表类型"
        G[销售汇总报表]
        H[采购汇总报表]
        I[库存价值报表]
        J[财务分析报表]
    end

    subgraph "B2C报表类型"
        K[门店销售报表]
        L[会员分析报表]
        M[促销效果报表]
        N[POS交易报表]
    end

    A --> G
    B --> H
    C --> I
    D --> J
    E --> K
    E --> L
    E --> M
    E --> N
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    F --> L
    F --> M
    F --> N
```

## 2. 销售报表服务

### 2.1 销售报表服务实现

```java
@Service
@Transactional(readOnly = true)
public class SalesReportServiceImpl implements SalesReportService {
    
    @Autowired
    private SalesOrderMapper salesOrderMapper;
    
    @Autowired
    private CustomerMapper customerMapper;
    
    @Autowired
    private ProductMapper productMapper;
    
    @Override
    public SalesSummaryReportDTO generateSalesSummaryReport(SalesReportRequest request) {
        // 1. 获取销售订单数据
        List<SalesOrder> salesOrders = getSalesOrdersByDateRange(
            request.getStartDate(), request.getEndDate(), request.getCustomerId()
        );
        
        // 2. 计算汇总数据
        int totalOrders = salesOrders.size();
        BigDecimal totalSalesAmount = salesOrders.stream()
            .map(SalesOrder::getFinalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal averageOrderValue = totalOrders > 0 ?
            totalSalesAmount.divide(new BigDecimal(totalOrders), 2, RoundingMode.HALF_UP) :
            BigDecimal.ZERO;
        
        // 3. 按状态统计
        Map<SalesOrderStatus, Long> statusCount = salesOrders.stream()
            .collect(Collectors.groupingBy(
                SalesOrder::getStatus,
                Collectors.counting()
            ));
        
        // 4. 按客户统计
        Map<Long, BigDecimal> customerSales = salesOrders.stream()
            .collect(Collectors.groupingBy(
                SalesOrder::getCustomerId,
                Collectors.mapping(
                    SalesOrder::getFinalAmount,
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                )
            ));
        
        // 5. 获取Top客户
        List<CustomerSalesDTO> topCustomers = customerSales.entrySet().stream()
            .sorted(Map.Entry.<Long, BigDecimal>comparingByValue().reversed())
            .limit(10)
            .map(entry -> {
                Customer customer = customerMapper.selectById(entry.getKey());
                return CustomerSalesDTO.builder()
                    .customerId(entry.getKey())
                    .customerName(customer.getCustomerName())
                    .salesAmount(entry.getValue())
                    .build();
            })
            .collect(Collectors.toList());
        
        return SalesSummaryReportDTO.builder()
            .reportPeriod(request.getStartDate() + " 至 " + request.getEndDate())
            .totalOrders(totalOrders)
            .totalSalesAmount(totalSalesAmount)
            .averageOrderValue(averageOrderValue)
            .statusStatistics(statusCount)
            .topCustomers(topCustomers)
            .generatedAt(LocalDateTime.now())
            .build();
    }
    
    @Override
    public List<SalesTrendDTO> generateSalesTrendReport(SalesReportRequest request) {
        List<SalesTrendDTO> trendData = new ArrayList<>();
        
        LocalDate currentDate = request.getStartDate();
        while (!currentDate.isAfter(request.getEndDate())) {
            LocalDate nextDate = currentDate.plusDays(1);
            
            List<SalesOrder> dailyOrders = getSalesOrdersByDateRange(
                currentDate, nextDate, request.getCustomerId()
            );
            
            BigDecimal dailySales = dailyOrders.stream()
                .map(SalesOrder::getFinalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            trendData.add(SalesTrendDTO.builder()
                .date(currentDate)
                .orderCount(dailyOrders.size())
                .salesAmount(dailySales)
                .build());
            
            currentDate = nextDate;
        }
        
        return trendData;
    }
    
    @Override
    public List<ProductSalesDTO> generateProductSalesReport(SalesReportRequest request) {
        // 获取销售订单项数据
        List<SalesOrderItem> orderItems = getOrderItemsByDateRange(
            request.getStartDate(), request.getEndDate()
        );
        
        // 按商品分组统计
        Map<Long, List<SalesOrderItem>> productGroups = orderItems.stream()
            .collect(Collectors.groupingBy(SalesOrderItem::getProductId));
        
        return productGroups.entrySet().stream()
            .map(entry -> {
                Long productId = entry.getKey();
                List<SalesOrderItem> items = entry.getValue();
                
                Product product = productMapper.selectById(productId);
                
                BigDecimal totalQuantity = items.stream()
                    .map(SalesOrderItem::getQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                BigDecimal totalAmount = items.stream()
                    .map(SalesOrderItem::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                BigDecimal averagePrice = totalQuantity.compareTo(BigDecimal.ZERO) > 0 ?
                    totalAmount.divide(totalQuantity, 2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO;
                
                return ProductSalesDTO.builder()
                    .productId(productId)
                    .productName(product.getProductName())
                    .productCode(product.getProductCode())
                    .totalQuantity(totalQuantity)
                    .totalAmount(totalAmount)
                    .averagePrice(averagePrice)
                    .orderCount(items.size())
                    .build();
            })
            .sorted(Comparator.comparing(ProductSalesDTO::getTotalAmount).reversed())
            .collect(Collectors.toList());
    }
    
    private List<SalesOrder> getSalesOrdersByDateRange(LocalDate startDate, LocalDate endDate, Long customerId) {
        LambdaQueryWrapper<SalesOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(SalesOrder::getOrderDate, startDate, endDate)
               .eq(customerId != null, SalesOrder::getCustomerId, customerId)
               .eq(SalesOrder::getStatus, SalesOrderStatus.COMPLETED);
        
        return salesOrderMapper.selectList(wrapper);
    }
    
    private List<SalesOrderItem> getOrderItemsByDateRange(LocalDate startDate, LocalDate endDate) {
        return salesOrderMapper.selectOrderItemsByDateRange(startDate, endDate);
    }
}
```

## 3. 采购报表服务

### 3.1 采购报表服务实现

```java
@Service
@Transactional(readOnly = true)
public class PurchaseReportServiceImpl implements PurchaseReportService {
    
    @Autowired
    private PurchaseOrderMapper purchaseOrderMapper;
    
    @Autowired
    private SupplierMapper supplierMapper;
    
    @Override
    public PurchaseSummaryReportDTO generatePurchaseSummaryReport(PurchaseReportRequest request) {
        // 1. 获取采购订单数据
        List<PurchaseOrder> purchaseOrders = getPurchaseOrdersByDateRange(
            request.getStartDate(), request.getEndDate(), request.getSupplierId()
        );
        
        // 2. 计算汇总数据
        int totalOrders = purchaseOrders.size();
        BigDecimal totalPurchaseAmount = purchaseOrders.stream()
            .map(PurchaseOrder::getFinalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal averageOrderValue = totalOrders > 0 ?
            totalPurchaseAmount.divide(new BigDecimal(totalOrders), 2, RoundingMode.HALF_UP) :
            BigDecimal.ZERO;
        
        // 3. 按状态统计
        Map<PurchaseOrderStatus, Long> statusCount = purchaseOrders.stream()
            .collect(Collectors.groupingBy(
                PurchaseOrder::getStatus,
                Collectors.counting()
            ));
        
        // 4. 按供应商统计
        Map<Long, BigDecimal> supplierPurchases = purchaseOrders.stream()
            .collect(Collectors.groupingBy(
                PurchaseOrder::getSupplierId,
                Collectors.mapping(
                    PurchaseOrder::getFinalAmount,
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                )
            ));
        
        // 5. 获取Top供应商
        List<SupplierPurchaseDTO> topSuppliers = supplierPurchases.entrySet().stream()
            .sorted(Map.Entry.<Long, BigDecimal>comparingByValue().reversed())
            .limit(10)
            .map(entry -> {
                Supplier supplier = supplierMapper.selectById(entry.getKey());
                return SupplierPurchaseDTO.builder()
                    .supplierId(entry.getKey())
                    .supplierName(supplier.getSupplierName())
                    .purchaseAmount(entry.getValue())
                    .build();
            })
            .collect(Collectors.toList());
        
        return PurchaseSummaryReportDTO.builder()
            .reportPeriod(request.getStartDate() + " 至 " + request.getEndDate())
            .totalOrders(totalOrders)
            .totalPurchaseAmount(totalPurchaseAmount)
            .averageOrderValue(averageOrderValue)
            .statusStatistics(statusCount)
            .topSuppliers(topSuppliers)
            .generatedAt(LocalDateTime.now())
            .build();
    }
    
    @Override
    public List<PurchaseTrendDTO> generatePurchaseTrendReport(PurchaseReportRequest request) {
        List<PurchaseTrendDTO> trendData = new ArrayList<>();
        
        LocalDate currentDate = request.getStartDate();
        while (!currentDate.isAfter(request.getEndDate())) {
            LocalDate nextDate = currentDate.plusDays(1);
            
            List<PurchaseOrder> dailyOrders = getPurchaseOrdersByDateRange(
                currentDate, nextDate, request.getSupplierId()
            );
            
            BigDecimal dailyPurchases = dailyOrders.stream()
                .map(PurchaseOrder::getFinalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            trendData.add(PurchaseTrendDTO.builder()
                .date(currentDate)
                .orderCount(dailyOrders.size())
                .purchaseAmount(dailyPurchases)
                .build());
            
            currentDate = nextDate;
        }
        
        return trendData;
    }
    
    private List<PurchaseOrder> getPurchaseOrdersByDateRange(LocalDate startDate, LocalDate endDate, Long supplierId) {
        LambdaQueryWrapper<PurchaseOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(PurchaseOrder::getOrderDate, startDate, endDate)
               .eq(supplierId != null, PurchaseOrder::getSupplierId, supplierId)
               .eq(PurchaseOrder::getStatus, PurchaseOrderStatus.COMPLETED);
        
        return purchaseOrderMapper.selectList(wrapper);
    }
}
```

## 4. 库存报表服务

### 4.1 库存报表服务实现

```java
@Service
@Transactional(readOnly = true)
public class InventoryReportServiceImpl implements InventoryReportService {
    
    @Autowired
    private InventoryMapper inventoryMapper;
    
    @Autowired
    private InventoryTransactionMapper transactionMapper;
    
    @Override
    public InventorySummaryReportDTO generateInventorySummaryReport(InventoryReportRequest request) {
        // 1. 获取库存数据
        List<Inventory> inventories = getInventoriesByWarehouse(request.getWarehouseId());
        
        // 2. 计算汇总数据
        int totalProducts = inventories.size();
        
        BigDecimal totalValue = inventories.stream()
            .map(inv -> inv.getTotalQuantity().multiply(inv.getUnitCost()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal totalQuantity = inventories.stream()
            .map(Inventory::getTotalQuantity)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 3. 库存状态统计
        long lowStockCount = inventories.stream()
            .filter(Inventory::isLowStock)
            .count();
        
        long outOfStockCount = inventories.stream()
            .filter(inv -> inv.getAvailableQuantity().compareTo(BigDecimal.ZERO) <= 0)
            .count();
        
        long overStockCount = inventories.stream()
            .filter(Inventory::isOverStock)
            .count();
        
        return InventorySummaryReportDTO.builder()
            .warehouseId(request.getWarehouseId())
            .totalProducts(totalProducts)
            .totalValue(totalValue)
            .totalQuantity(totalQuantity)
            .lowStockCount((int) lowStockCount)
            .outOfStockCount((int) outOfStockCount)
            .overStockCount((int) overStockCount)
            .generatedAt(LocalDateTime.now())
            .build();
    }
    
    @Override
    public List<InventoryMovementDTO> generateInventoryMovementReport(InventoryReportRequest request) {
        List<InventoryTransaction> transactions = getTransactionsByDateRange(
            request.getStartDate(), request.getEndDate(), request.getWarehouseId()
        );
        
        return transactions.stream()
            .map(transaction -> InventoryMovementDTO.builder()
                .transactionDate(transaction.getCreateTime().toLocalDate())
                .productId(transaction.getProductId())
                .transactionType(transaction.getTransactionType())
                .quantity(transaction.getQuantity())
                .unitCost(transaction.getUnitCost())
                .totalAmount(transaction.getQuantity().multiply(transaction.getUnitCost()))
                .reason(transaction.getReason())
                .build())
            .collect(Collectors.toList());
    }
    
    private List<Inventory> getInventoriesByWarehouse(Long warehouseId) {
        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(warehouseId != null, Inventory::getWarehouseId, warehouseId);
        
        return inventoryMapper.selectList(wrapper);
    }
    
    private List<InventoryTransaction> getTransactionsByDateRange(LocalDate startDate, LocalDate endDate, Long warehouseId) {
        LambdaQueryWrapper<InventoryTransaction> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(startDate != null && endDate != null, 
                       InventoryTransaction::getCreateTime, 
                       startDate.atStartOfDay(), endDate.atTime(23, 59, 59))
               .eq(warehouseId != null, InventoryTransaction::getWarehouseId, warehouseId)
               .orderByDesc(InventoryTransaction::getCreateTime);
        
        return transactionMapper.selectList(wrapper);
    }
}
```

## 4. 财务报表服务

### 4.1 财务报表服务实现

```java
@Service
@Transactional(readOnly = true)
public class FinancialReportServiceImpl implements FinancialReportService {

    @Autowired
    private AccountsReceivableMapper arMapper;

    @Autowired
    private AccountsPayableMapper apMapper;

    @Override
    public FinancialSummaryReportDTO generateFinancialSummaryReport(FinancialReportRequest request) {
        // 1. 应收账款统计
        List<AccountsReceivable> arList = getARByDateRange(
            request.getStartDate(), request.getEndDate()
        );

        BigDecimal totalAR = arList.stream()
            .map(AccountsReceivable::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal outstandingAR = arList.stream()
            .map(AccountsReceivable::getOutstandingAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 2. 应付账款统计
        List<AccountsPayable> apList = getAPByDateRange(
            request.getStartDate(), request.getEndDate()
        );

        BigDecimal totalAP = apList.stream()
            .map(AccountsPayable::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal outstandingAP = apList.stream()
            .map(AccountsPayable::getOutstandingAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 3. 现金流计算
        BigDecimal cashInflow = totalAR.subtract(outstandingAR);
        BigDecimal cashOutflow = totalAP.subtract(outstandingAP);
        BigDecimal netCashFlow = cashInflow.subtract(cashOutflow);

        return FinancialSummaryReportDTO.builder()
            .reportPeriod(request.getStartDate() + " 至 " + request.getEndDate())
            .totalAccountsReceivable(totalAR)
            .outstandingAccountsReceivable(outstandingAR)
            .totalAccountsPayable(totalAP)
            .outstandingAccountsPayable(outstandingAP)
            .cashInflow(cashInflow)
            .cashOutflow(cashOutflow)
            .netCashFlow(netCashFlow)
            .generatedAt(LocalDateTime.now())
            .build();
    }

    private List<AccountsReceivable> getARByDateRange(LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<AccountsReceivable> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(AccountsReceivable::getInvoiceDate, startDate, endDate);
        return arMapper.selectList(wrapper);
    }

    private List<AccountsPayable> getAPByDateRange(LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<AccountsPayable> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(AccountsPayable::getInvoiceDate, startDate, endDate);
        return apMapper.selectList(wrapper);
    }
}
```

## 5. 类图设计

### 5.1 报表分析核心类图

```mermaid
classDiagram
    class SalesReportService {
        +generateSalesSummaryReport()
        +generateSalesTrendReport()
        +generateProductSalesReport()
        +generateCustomerSalesReport()
    }

    class PurchaseReportService {
        +generatePurchaseSummaryReport()
        +generatePurchaseTrendReport()
        +generateSupplierPurchaseReport()
    }

    class InventoryReportService {
        +generateInventorySummaryReport()
        +generateInventoryMovementReport()
        +generateInventoryValueReport()
        +generateInventoryTurnoverReport()
    }

    class FinancialReportService {
        +generateFinancialSummaryReport()
        +generateCashFlowReport()
        +generateProfitLossReport()
        +generateARAgingReport()
    }

    class ReportScheduler {
        +scheduleReport()
        +executeScheduledReports()
        +cancelScheduledReport()
    }

    class ReportExportService {
        +exportToExcel()
        +exportToPDF()
        +exportToCSV()
    }

    class SalesOrder {
        +Long id
        +String orderNumber
        +BigDecimal finalAmount
    }

    class PurchaseOrder {
        +Long id
        +String orderNumber
        +BigDecimal finalAmount
    }

    class Inventory {
        +Long id
        +BigDecimal totalQuantity
        +BigDecimal unitCost
    }

    class AccountsReceivable {
        +Long id
        +BigDecimal totalAmount
        +BigDecimal outstandingAmount
    }

    class AccountsPayable {
        +Long id
        +BigDecimal totalAmount
        +BigDecimal outstandingAmount
    }

    SalesReportService ..> SalesOrder
    PurchaseReportService ..> PurchaseOrder
    InventoryReportService ..> Inventory
    FinancialReportService ..> AccountsReceivable
    FinancialReportService ..> AccountsPayable

    ReportScheduler ..> SalesReportService
    ReportScheduler ..> PurchaseReportService
    ReportScheduler ..> InventoryReportService
    ReportScheduler ..> FinancialReportService

    ReportExportService ..> SalesReportService
    ReportExportService ..> PurchaseReportService
    ReportExportService ..> InventoryReportService
    ReportExportService ..> FinancialReportService
```

## 6. 时序图设计

### 6.1 报表生成时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as ShenYu网关
    participant RS as 报表服务
    participant DS as 数据服务
    participant C as 缓存
    participant DB as 数据库
    participant ES as 导出服务

    U->>G: 生成报表请求
    G->>RS: 转发请求

    RS->>C: 检查缓存
    C-->>RS: 缓存未命中

    RS->>DS: 查询销售数据
    DS->>DB: 执行查询
    DB-->>DS: 返回数据
    DS-->>RS: 返回销售数据

    RS->>DS: 查询库存数据
    DS->>DB: 执行查询
    DB-->>DS: 返回数据
    DS-->>RS: 返回库存数据

    RS->>RS: 计算报表数据
    RS->>RS: 生成报表对象

    RS->>C: 缓存报表数据
    C-->>RS: 缓存成功

    alt 需要导出
        RS->>ES: 导出报表
        ES-->>RS: 返回文件
        RS-->>G: 返回文件
    else 在线查看
        RS-->>G: 返回报表数据
    end

    G-->>U: 返回结果
```

### 6.2 定时报表生成时序图

```mermaid
sequenceDiagram
    participant T as 定时任务
    participant RS as 报表调度器
    participant SS as 销售报表服务
    participant IS as 库存报表服务
    participant FS as 财务报表服务
    participant ES as 导出服务
    participant NS as 通知服务
    participant K as Kafka

    T->>RS: 触发定时报表
    RS->>RS: 获取报表配置

    par 并行生成报表
        RS->>SS: 生成销售报表
        and
        RS->>IS: 生成库存报表
        and
        RS->>FS: 生成财务报表
    end

    SS-->>RS: 销售报表数据
    IS-->>RS: 库存报表数据
    FS-->>RS: 财务报表数据

    RS->>ES: 导出报表文件
    ES-->>RS: 返回文件路径

    RS->>K: 发布报表生成事件
    K-->>NS: 报表生成事件

    NS->>NS: 发送报表通知

    Note over T,NS: 每日/周/月定时执行
```

## 7. 交互图设计

### 7.1 报表数据流交互图

```mermaid
graph TB
    subgraph "报表数据流"
        A[业务数据] --> B[数据清洗]
        B --> C[数据聚合]
        C --> D[指标计算]

        D --> E{报表类型}
        E -->|销售报表| F[销售数据分析]
        E -->|库存报表| G[库存数据分析]
        E -->|财务报表| H[财务数据分析]

        F --> I[销售趋势图]
        F --> J[客户分析表]
        F --> K[商品销量排行]

        G --> L[库存价值表]
        G --> M[库存周转率]
        G --> N[库存预警列表]

        H --> O[应收应付统计]
        H --> P[现金流分析]
        H --> Q[利润分析]

        I --> R[报表展示]
        J --> R
        K --> R
        L --> R
        M --> R
        N --> R
        O --> R
        P --> R
        Q --> R
    end
```

### 7.2 报表缓存策略交互图

```mermaid
graph LR
    subgraph "报表缓存策略"
        A[报表请求] --> B{缓存检查}
        B -->|命中| C[返回缓存数据]
        B -->|未命中| D[查询数据库]

        D --> E[计算报表]
        E --> F[存入缓存]
        F --> G[返回数据]

        H[数据变更事件] --> I[清除相关缓存]
        I --> J[标记缓存失效]

        K[定时任务] --> L[预热热门报表]
        L --> M[更新缓存]

        C --> N[用户获得结果]
        G --> N
    end
```

## 8. 事件驱动设计

### 8.1 报表领域事件

```java
// 报表生成完成事件
@Data
@AllArgsConstructor
public class ReportGeneratedEvent extends DomainEvent {
    private String reportId;
    private String reportType;
    private String reportName;
    private LocalDateTime generatedAt;
    private String filePath;
    private Long generatedBy;
    private List<String> recipients;
}

// 报表数据更新事件
@Data
@AllArgsConstructor
public class ReportDataUpdatedEvent extends DomainEvent {
    private String dataType; // SALES, INVENTORY, FINANCE
    private Long entityId;
    private String updateType; // CREATE, UPDATE, DELETE
    private LocalDateTime updateTime;
}

// 定时报表执行事件
@Data
@AllArgsConstructor
public class ScheduledReportExecutedEvent extends DomainEvent {
    private Long scheduleId;
    private String reportType;
    private String cronExpression;
    private LocalDateTime executedAt;
    private boolean success;
    private String errorMessage;
}
```

### 8.2 Kafka事件处理

```java
@Component
public class ReportEventPublisher {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String REPORT_TOPIC = "report.events";

    public void publishReportGenerated(ReportGeneratedEvent event) {
        kafkaTemplate.send(REPORT_TOPIC, "report.generated", event);
    }

    public void publishDataUpdated(ReportDataUpdatedEvent event) {
        kafkaTemplate.send(REPORT_TOPIC, "data.updated", event);
    }

    public void publishScheduledReportExecuted(ScheduledReportExecutedEvent event) {
        kafkaTemplate.send(REPORT_TOPIC, "scheduled.executed", event);
    }
}

@Component
@KafkaListener(topics = "report.events")
public class ReportEventHandler {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ReportCacheService cacheService;

    @KafkaHandler
    public void handleReportGenerated(ReportGeneratedEvent event) {
        // 发送报表生成通知
        notificationService.sendReportGeneratedNotification(event);
    }

    @KafkaHandler
    public void handleDataUpdated(ReportDataUpdatedEvent event) {
        // 清除相关报表缓存
        cacheService.clearRelatedCache(event.getDataType(), event.getEntityId());
    }

    @KafkaHandler
    public void handleScheduledReportExecuted(ScheduledReportExecutedEvent event) {
        if (!event.isSuccess()) {
            // 发送执行失败通知
            notificationService.sendScheduledReportFailureNotification(event);
        }
    }
}

// 监听业务数据变更事件
@Component
public class BusinessDataEventHandler {

    @Autowired
    private ReportEventPublisher reportEventPublisher;

    @RocketMQMessageListener(topic = "sales_events", consumerGroup = "report-consumer-group")
    public void handleSalesOrderCreated(SalesOrderCreatedEvent event) {
        ReportDataUpdatedEvent reportEvent = new ReportDataUpdatedEvent(
            "SALES", event.getOrderId(), "CREATE", LocalDateTime.now()
        );
        reportEventPublisher.publishDataUpdated(reportEvent);
    }

    @RocketMQMessageListener(topic = "inventory_events", consumerGroup = "report-consumer-group")
    public void handleInventoryChanged(InventoryChangedEvent event) {
        ReportDataUpdatedEvent reportEvent = new ReportDataUpdatedEvent(
            "INVENTORY", event.getProductId(), "UPDATE", LocalDateTime.now()
        );
        reportEventPublisher.publishDataUpdated(reportEvent);
    }

    @KafkaHandler
    public void handlePaymentRecorded(PaymentRecordedEvent event) {
        ReportDataUpdatedEvent reportEvent = new ReportDataUpdatedEvent(
            "FINANCE", event.getArId(), "UPDATE", LocalDateTime.now()
        );
        reportEventPublisher.publishDataUpdated(reportEvent);
    }
}

// 定时报表调度器
@Component
public class ReportScheduler {

    @Autowired
    private SalesReportService salesReportService;

    @Autowired
    private InventoryReportService inventoryReportService;

    @Autowired
    private FinancialReportService financialReportService;

    @Autowired
    private ReportEventPublisher eventPublisher;

    @Scheduled(cron = "0 0 8 * * ?") // 每天上午8点
    public void generateDailyReports() {
        try {
            // 生成昨日销售报表
            LocalDate yesterday = LocalDate.now().minusDays(1);
            SalesReportRequest request = new SalesReportRequest();
            request.setStartDate(yesterday);
            request.setEndDate(yesterday);

            SalesSummaryReportDTO report = salesReportService.generateSalesSummaryReport(request);

            ReportGeneratedEvent event = new ReportGeneratedEvent(
                UUID.randomUUID().toString(),
                "DAILY_SALES",
                "每日销售报表",
                LocalDateTime.now(),
                "/reports/daily_sales_" + yesterday + ".xlsx",
                null,
                Arrays.asList("<EMAIL>", "<EMAIL>")
            );

            eventPublisher.publishReportGenerated(event);

        } catch (Exception e) {
            ScheduledReportExecutedEvent errorEvent = new ScheduledReportExecutedEvent(
                1L, "DAILY_SALES", "0 0 8 * * ?", LocalDateTime.now(), false, e.getMessage()
            );
            eventPublisher.publishScheduledReportExecuted(errorEvent);
        }
    }
}
```

## 6. 零售报表服务

### 6.1 零售报表服务实现

```java
@Service
@Transactional(readOnly = true)
public class RetailReportServiceImpl implements RetailReportService {

    @Autowired
    private RetailPosSaleMapper posSaleMapper;

    @Autowired
    private RetailMemberMapper memberMapper;

    @Autowired
    private RetailPromotionMapper promotionMapper;

    @Autowired
    private RetailStoreMapper storeMapper;

    @Override
    public StoreSalesReportDTO generateStoreSalesReport(StoreSalesReportRequest request) {
        // 1. 查询门店销售数据
        List<StoreSalesData> salesData = posSaleMapper.getStoreSalesData(
            request.getStoreId(),
            request.getStartDate(),
            request.getEndDate()
        );

        // 2. 计算销售指标
        StoreSalesReportDTO report = new StoreSalesReportDTO();
        report.setStoreId(request.getStoreId());
        report.setReportPeriod(request.getStartDate() + " 至 " + request.getEndDate());

        BigDecimal totalSales = salesData.stream()
            .map(StoreSalesData::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        Integer totalTransactions = salesData.size();
        BigDecimal avgTransactionValue = totalTransactions > 0 ?
            totalSales.divide(new BigDecimal(totalTransactions), 2, RoundingMode.HALF_UP) :
            BigDecimal.ZERO;

        report.setTotalSales(totalSales);
        report.setTotalTransactions(totalTransactions);
        report.setAvgTransactionValue(avgTransactionValue);

        // 3. 按日期分组统计
        Map<LocalDate, BigDecimal> dailySales = salesData.stream()
            .collect(Collectors.groupingBy(
                data -> data.getSaleDate(),
                Collectors.mapping(
                    StoreSalesData::getTotalAmount,
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                )
            ));

        report.setDailySalesData(dailySales);

        // 4. 热销商品统计
        List<ProductSalesRankDTO> topProducts = posSaleMapper.getTopSellingProducts(
            request.getStoreId(),
            request.getStartDate(),
            request.getEndDate(),
            10
        );
        report.setTopSellingProducts(topProducts);

        return report;
    }

    @Override
    public MemberAnalysisReportDTO generateMemberAnalysisReport(MemberAnalysisReportRequest request) {
        // 1. 会员基础统计
        MemberAnalysisReportDTO report = new MemberAnalysisReportDTO();

        // 总会员数
        Integer totalMembers = memberMapper.getTotalMemberCount(request.getStoreId());

        // 新增会员数
        Integer newMembers = memberMapper.getNewMemberCount(
            request.getStoreId(),
            request.getStartDate(),
            request.getEndDate()
        );

        // 活跃会员数（期间内有消费的会员）
        Integer activeMembers = memberMapper.getActiveMemberCount(
            request.getStoreId(),
            request.getStartDate(),
            request.getEndDate()
        );

        report.setTotalMembers(totalMembers);
        report.setNewMembers(newMembers);
        report.setActiveMembers(activeMembers);
        report.setMemberActivationRate(
            totalMembers > 0 ?
            new BigDecimal(activeMembers).divide(new BigDecimal(totalMembers), 4, RoundingMode.HALF_UP) :
            BigDecimal.ZERO
        );

        // 2. 会员等级分布
        List<MemberLevelDistributionDTO> levelDistribution = memberMapper.getMemberLevelDistribution(request.getStoreId());
        report.setMemberLevelDistribution(levelDistribution);

        // 3. 会员消费分析
        List<MemberConsumptionAnalysisDTO> consumptionAnalysis = memberMapper.getMemberConsumptionAnalysis(
            request.getStoreId(),
            request.getStartDate(),
            request.getEndDate()
        );
        report.setMemberConsumptionAnalysis(consumptionAnalysis);

        // 4. 会员积分统计
        MemberPointsStatisticsDTO pointsStats = memberMapper.getMemberPointsStatistics(
            request.getStoreId(),
            request.getStartDate(),
            request.getEndDate()
        );
        report.setPointsStatistics(pointsStats);

        return report;
    }

    @Override
    public PromotionEffectReportDTO generatePromotionEffectReport(PromotionEffectReportRequest request) {
        // 1. 促销活动基础信息
        PromotionEffectReportDTO report = new PromotionEffectReportDTO();

        List<PromotionEffectData> promotionData = promotionMapper.getPromotionEffectData(
            request.getPromotionId(),
            request.getStartDate(),
            request.getEndDate()
        );

        // 2. 促销效果统计
        BigDecimal totalDiscountAmount = promotionData.stream()
            .map(PromotionEffectData::getDiscountAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalSalesAmount = promotionData.stream()
            .map(PromotionEffectData::getSalesAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        Integer totalUsageCount = promotionData.stream()
            .mapToInt(PromotionEffectData::getUsageCount)
            .sum();

        report.setTotalDiscountAmount(totalDiscountAmount);
        report.setTotalSalesAmount(totalSalesAmount);
        report.setTotalUsageCount(totalUsageCount);

        // 计算促销ROI
        BigDecimal promotionROI = totalDiscountAmount.compareTo(BigDecimal.ZERO) > 0 ?
            totalSalesAmount.divide(totalDiscountAmount, 2, RoundingMode.HALF_UP) :
            BigDecimal.ZERO;
        report.setPromotionROI(promotionROI);

        // 3. 按日期统计促销效果
        Map<LocalDate, PromotionDailyEffectDTO> dailyEffect = promotionData.stream()
            .collect(Collectors.groupingBy(
                PromotionEffectData::getDate,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        PromotionDailyEffectDTO daily = new PromotionDailyEffectDTO();
                        daily.setDate(list.get(0).getDate());
                        daily.setUsageCount(list.stream().mapToInt(PromotionEffectData::getUsageCount).sum());
                        daily.setDiscountAmount(list.stream()
                            .map(PromotionEffectData::getDiscountAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        daily.setSalesAmount(list.stream()
                            .map(PromotionEffectData::getSalesAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        return daily;
                    }
                )
            ));

        report.setDailyEffectData(dailyEffect);

        return report;
    }

    @Override
    public PosTransactionReportDTO generatePosTransactionReport(PosTransactionReportRequest request) {
        // 1. POS交易统计
        PosTransactionReportDTO report = new PosTransactionReportDTO();

        List<PosTransactionData> transactionData = posSaleMapper.getPosTransactionData(
            request.getStoreId(),
            request.getStartDate(),
            request.getEndDate()
        );

        // 2. 交易量统计
        Integer totalTransactions = transactionData.size();
        BigDecimal totalAmount = transactionData.stream()
            .map(PosTransactionData::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        report.setTotalTransactions(totalTransactions);
        report.setTotalAmount(totalAmount);

        // 3. 支付方式分析
        Map<String, PaymentMethodStatDTO> paymentMethodStats = transactionData.stream()
            .collect(Collectors.groupingBy(
                PosTransactionData::getPaymentMethod,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        PaymentMethodStatDTO stat = new PaymentMethodStatDTO();
                        stat.setPaymentMethod(list.get(0).getPaymentMethod());
                        stat.setTransactionCount(list.size());
                        stat.setTotalAmount(list.stream()
                            .map(PosTransactionData::getTotalAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        return stat;
                    }
                )
            ));

        report.setPaymentMethodStats(paymentMethodStats);

        // 4. 时段分析
        Map<Integer, HourlyTransactionStatDTO> hourlyStats = transactionData.stream()
            .collect(Collectors.groupingBy(
                data -> data.getTransactionTime().getHour(),
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        HourlyTransactionStatDTO stat = new HourlyTransactionStatDTO();
                        stat.setHour(list.get(0).getTransactionTime().getHour());
                        stat.setTransactionCount(list.size());
                        stat.setTotalAmount(list.stream()
                            .map(PosTransactionData::getTotalAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        return stat;
                    }
                )
            ));

        report.setHourlyStats(hourlyStats);

        return report;
    }
}
```

### 6.2 零售报表DTO

```java
@Data
public class StoreSalesReportDTO {
    private Long storeId;
    private String storeName;
    private String reportPeriod;
    private BigDecimal totalSales;
    private Integer totalTransactions;
    private BigDecimal avgTransactionValue;
    private Map<LocalDate, BigDecimal> dailySalesData;
    private List<ProductSalesRankDTO> topSellingProducts;
    private LocalDateTime generatedAt;
}

@Data
public class MemberAnalysisReportDTO {
    private Long storeId;
    private String storeName;
    private String reportPeriod;
    private Integer totalMembers;
    private Integer newMembers;
    private Integer activeMembers;
    private BigDecimal memberActivationRate;
    private List<MemberLevelDistributionDTO> memberLevelDistribution;
    private List<MemberConsumptionAnalysisDTO> memberConsumptionAnalysis;
    private MemberPointsStatisticsDTO pointsStatistics;
    private LocalDateTime generatedAt;
}

@Data
public class PromotionEffectReportDTO {
    private Long promotionId;
    private String promotionName;
    private String reportPeriod;
    private BigDecimal totalDiscountAmount;
    private BigDecimal totalSalesAmount;
    private Integer totalUsageCount;
    private BigDecimal promotionROI;
    private Map<LocalDate, PromotionDailyEffectDTO> dailyEffectData;
    private LocalDateTime generatedAt;
}

@Data
public class PosTransactionReportDTO {
    private Long storeId;
    private String storeName;
    private String reportPeriod;
    private Integer totalTransactions;
    private BigDecimal totalAmount;
    private Map<String, PaymentMethodStatDTO> paymentMethodStats;
    private Map<Integer, HourlyTransactionStatDTO> hourlyStats;
    private LocalDateTime generatedAt;
}

@Data
public class ProductSalesRankDTO {
    private Long productId;
    private String productName;
    private String productCode;
    private Integer salesQuantity;
    private BigDecimal salesAmount;
    private Integer rank;
}

@Data
public class MemberLevelDistributionDTO {
    private String memberLevel;
    private Integer memberCount;
    private BigDecimal percentage;
}

@Data
public class MemberConsumptionAnalysisDTO {
    private String memberLevel;
    private BigDecimal avgConsumptionAmount;
    private Integer avgTransactionCount;
    private BigDecimal totalConsumptionAmount;
}

@Data
public class MemberPointsStatisticsDTO {
    private Integer totalPointsEarned;
    private Integer totalPointsRedeemed;
    private Integer totalPointsBalance;
    private BigDecimal avgPointsPerTransaction;
}

@Data
public class PromotionDailyEffectDTO {
    private LocalDate date;
    private Integer usageCount;
    private BigDecimal discountAmount;
    private BigDecimal salesAmount;
}

@Data
public class PaymentMethodStatDTO {
    private String paymentMethod;
    private Integer transactionCount;
    private BigDecimal totalAmount;
    private BigDecimal percentage;
}

@Data
public class HourlyTransactionStatDTO {
    private Integer hour;
    private Integer transactionCount;
    private BigDecimal totalAmount;
}
```

### 6.3 零售报表接口

```java
@RestController
@RequestMapping("/api/v1/reports/retail")
@Tag(name = "零售报表管理", description = "零售业务报表相关接口")
public class RetailReportController {

    @Autowired
    private RetailReportService retailReportService;

    @PostMapping("/store-sales")
    @Operation(summary = "生成门店销售报表")
    public Result<StoreSalesReportDTO> generateStoreSalesReport(
            @RequestBody @Valid StoreSalesReportRequest request) {
        return Result.success(retailReportService.generateStoreSalesReport(request));
    }

    @PostMapping("/member-analysis")
    @Operation(summary = "生成会员分析报表")
    public Result<MemberAnalysisReportDTO> generateMemberAnalysisReport(
            @RequestBody @Valid MemberAnalysisReportRequest request) {
        return Result.success(retailReportService.generateMemberAnalysisReport(request));
    }

    @PostMapping("/promotion-effect")
    @Operation(summary = "生成促销效果报表")
    public Result<PromotionEffectReportDTO> generatePromotionEffectReport(
            @RequestBody @Valid PromotionEffectReportRequest request) {
        return Result.success(retailReportService.generatePromotionEffectReport(request));
    }

    @PostMapping("/pos-transaction")
    @Operation(summary = "生成POS交易报表")
    public Result<PosTransactionReportDTO> generatePosTransactionReport(
            @RequestBody @Valid PosTransactionReportRequest request) {
        return Result.success(retailReportService.generatePosTransactionReport(request));
    }

    @GetMapping("/store-sales/export")
    @Operation(summary = "导出门店销售报表")
    public void exportStoreSalesReport(
            @Valid StoreSalesReportRequest request,
            HttpServletResponse response) throws IOException {

        StoreSalesReportDTO report = retailReportService.generateStoreSalesReport(request);

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
            "attachment; filename=store_sales_report_" + System.currentTimeMillis() + ".xlsx");

        // 导出Excel
        retailReportService.exportStoreSalesReportToExcel(report, response.getOutputStream());
    }

    @GetMapping("/member-analysis/export")
    @Operation(summary = "导出会员分析报表")
    public void exportMemberAnalysisReport(
            @Valid MemberAnalysisReportRequest request,
            HttpServletResponse response) throws IOException {

        MemberAnalysisReportDTO report = retailReportService.generateMemberAnalysisReport(request);

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
            "attachment; filename=member_analysis_report_" + System.currentTimeMillis() + ".xlsx");

        retailReportService.exportMemberAnalysisReportToExcel(report, response.getOutputStream());
    }
}
```

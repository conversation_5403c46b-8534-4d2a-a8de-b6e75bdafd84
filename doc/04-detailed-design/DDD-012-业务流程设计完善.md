# DDD-012 业务流程设计完善

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-012 |
| 文档名称 | 业务流程设计完善 |
| 版本号 | v1.0 |
| 创建日期 | 2025-07-02 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 草稿 |
| 作者 | 系统架构师 |

## 1. 设计完善概述

### 1.1 设计不完整问题识别

基于系统完整性检查报告和现有设计文档分析，发现以下关键设计不完整问题：

**🚨 关键设计缺陷：**
1. **数据流向设计模糊**：前置仓与中心仓库的数据同步机制不明确
2. **服务协调机制缺失**：跨服务业务流程的协调和一致性保证机制不完整
3. **异常处理设计不足**：缺少统一的异常处理和恢复机制设计
4. **业务规则引擎缺失**：缺少灵活的业务规则配置和执行机制
5. **决策点设计不明确**：关键业务决策点的逻辑和规则不清晰

### 1.2 设计完善目标

**🎯 完善目标：**
- 明确前置仓系统的完整数据流向设计
- 设计完整的跨服务协调和事务一致性机制
- 建立统一的异常处理和故障恢复机制
- 设计灵活的业务规则引擎和决策机制
- 完善关键业务流程的详细设计

## 2. 数据流向设计完善

### 2.1 整体数据流架构设计

```mermaid
graph TB
    subgraph "数据源层"
        A[前置仓本地数据]
        B[中心仓库数据]
        C[订单系统数据]
        D[外部系统数据]
    end

    subgraph "数据处理层"
        E[数据收集服务]
        F[数据验证服务]
        G[数据转换服务]
        H[数据同步服务]
        I[冲突解决服务]
    end

    subgraph "业务逻辑层"
        J[库存管理服务]
        K[订单履约服务]
        L[智能补货服务]
        M[配送调度服务]
    end

    subgraph "数据存储层"
        N[(主数据库)]
        O[(本地缓存)]
        P[(消息队列)]
        Q[(数据仓库)]
    end

    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    H --> I
    
    I --> J
    I --> K
    I --> L
    I --> M
    
    J --> N
    K --> N
    L --> N
    M --> N
    
    J --> O
    K --> O
    L --> O
    M --> O
    
    J --> P
    K --> P
    L --> P
    M --> P
```

### 2.2 库存同步机制设计

**实时同步流程设计：**

```mermaid
sequenceDiagram
    participant FW as 前置仓
    participant MQ as RocketMQ
    participant IS as 库存同步服务
    participant DB as 中心数据库
    participant Cache as Redis缓存
    participant AS as 告警服务

    Note over FW,AS: 正常同步流程
    FW->>MQ: 库存变动事件
    MQ->>IS: 消费事件
    IS->>IS: 数据验证
    IS->>IS: 冲突检测
    IS->>DB: 更新中心库存
    IS->>Cache: 更新缓存
    IS->>MQ: 发送确认事件
    MQ->>FW: 同步确认

    Note over FW,AS: 冲突处理流程
    FW->>MQ: 库存变动事件
    MQ->>IS: 消费事件
    IS->>IS: 检测到冲突
    IS->>IS: 执行冲突解决策略
    IS->>DB: 更新解决后的数据
    IS->>MQ: 发送冲突解决通知
    MQ->>FW: 冲突解决结果

    Note over FW,AS: 异常处理流程
    FW->>MQ: 库存变动事件
    MQ->>IS: 消费事件
    IS->>IS: 处理失败
    IS->>AS: 发送告警
    IS->>MQ: 发送到死信队列
    IS->>IS: 记录失败日志
    IS->>MQ: 触发重试机制
```

### 2.3 数据一致性保证机制设计

**分布式事务设计：**

```java
@Service
@Transactional
public class DistributedTransactionService {
    
    @Autowired
    private TransactionTemplate transactionTemplate;
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    /**
     * 分布式事务处理 - 库存分配
     */
    public AllocationResult executeInventoryAllocation(AllocationRequest request) {
        // 1. 预检查阶段
        PreCheckResult preCheck = preCheckAllocation(request);
        if (!preCheck.isValid()) {
            throw new AllocationException("Pre-check failed: " + preCheck.getErrorMessage());
        }
        
        // 2. 本地事务处理
        AllocationRecord record = transactionTemplate.execute(status -> {
            try {
                // 2.1 锁定中心库存
                boolean locked = inventoryService.lockCentralInventory(request);
                if (!locked) {
                    throw new AllocationException("Failed to lock central inventory");
                }
                
                // 2.2 创建分配记录
                AllocationRecord allocationRecord = allocationService.createRecord(request);
                
                // 2.3 发送事务消息
                SendResult sendResult = rocketMQTemplate.sendMessageInTransaction(
                    "inventory-allocation-topic",
                    MessageBuilder.withPayload(allocationRecord).build(),
                    request
                );
                
                if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                    throw new AllocationException("Failed to send transaction message");
                }
                
                return allocationRecord;
                
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new AllocationException("Local transaction failed", e);
            }
        });
        
        // 3. 异步验证结果
        CompletableFuture<Boolean> verificationFuture = CompletableFuture.supplyAsync(() -> {
            return verifyAllocationResult(request, record);
        });
        
        return AllocationResult.builder()
            .record(record)
            .verificationFuture(verificationFuture)
            .build();
    }
    
    /**
     * 事务消息监听器
     */
    @RocketMQTransactionListener
    public class AllocationTransactionListener implements RocketMQLocalTransactionListener {
        
        @Override
        public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
            try {
                AllocationRequest request = (AllocationRequest) arg;
                
                // 执行前置仓库存更新
                boolean success = frontWarehouseService.updateInventory(request);
                
                if (success) {
                    // 记录事务状态
                    transactionService.recordTransactionState(
                        msg.getKeys(), 
                        TransactionState.COMMITTED
                    );
                    return RocketMQLocalTransactionState.COMMIT;
                } else {
                    transactionService.recordTransactionState(
                        msg.getKeys(), 
                        TransactionState.ROLLBACK
                    );
                    return RocketMQLocalTransactionState.ROLLBACK;
                }
                
            } catch (Exception e) {
                log.error("Local transaction execution failed", e);
                transactionService.recordTransactionState(
                    msg.getKeys(), 
                    TransactionState.ROLLBACK
                );
                return RocketMQLocalTransactionState.ROLLBACK;
            }
        }
        
        @Override
        public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
            // 检查本地事务状态
            String transactionId = msg.getKeys();
            TransactionState state = transactionService.getTransactionState(transactionId);
            
            switch (state) {
                case COMMITTED:
                    return RocketMQLocalTransactionState.COMMIT;
                case ROLLBACK:
                    return RocketMQLocalTransactionState.ROLLBACK;
                case UNKNOWN:
                default:
                    return RocketMQLocalTransactionState.UNKNOWN;
            }
        }
    }
}
```

## 3. 订单分配逻辑设计完善

### 3.1 智能分配算法设计

**多维度评分机制：**

```java
@Service
public class EnhancedOrderAllocationService {
    
    @Autowired
    private BusinessRuleEngine ruleEngine;
    
    @Autowired
    private AllocationMetricsCollector metricsCollector;
    
    /**
     * 智能订单分配
     */
    public AllocationResult allocateOrder(Order order) {
        Timer.Sample sample = Timer.start(metricsCollector.getMeterRegistry());
        
        try {
            // 1. 执行分配前置规则
            RuleExecutionResult preRules = ruleEngine.executeRules("PRE_ALLOCATION", order);
            if (preRules.isBlocked()) {
                throw new AllocationException("Pre-allocation rules blocked: " + preRules.getBlockReason());
            }
            
            // 2. 获取候选前置仓
            List<FrontWarehouse> candidates = getCandidateWarehouses(order);
            if (candidates.isEmpty()) {
                throw new AllocationException("No candidate warehouses available for order: " + order.getId());
            }
            
            // 3. 多维度评分
            Map<FrontWarehouse, AllocationScore> scores = calculateAllocationScores(order, candidates);
            
            // 4. 执行分配决策规则
            RuleExecutionResult decisionRules = ruleEngine.executeRules("ALLOCATION_DECISION", 
                Map.of("order", order, "scores", scores));
            
            // 5. 选择最优前置仓
            FrontWarehouse selectedWarehouse = selectOptimalWarehouse(scores, decisionRules);
            
            // 6. 验证分配结果
            validateAllocation(order, selectedWarehouse);
            
            // 7. 记录分配决策
            AllocationDecision decision = recordAllocationDecision(order, selectedWarehouse, scores);
            
            // 8. 执行分配后置规则
            ruleEngine.executeRules("POST_ALLOCATION", 
                Map.of("order", order, "warehouse", selectedWarehouse, "decision", decision));
            
            metricsCollector.recordAllocationSuccess();
            
            return AllocationResult.builder()
                .warehouse(selectedWarehouse)
                .decision(decision)
                .scores(scores)
                .build();
                
        } catch (Exception e) {
            metricsCollector.recordAllocationFailure();
            throw e;
        } finally {
            sample.stop(metricsCollector.getAllocationTimer());
        }
    }
}
```

### 3.2 分配决策规则设计

**业务规则配置：**

```yaml
# 订单分配业务规则配置
order-allocation-rules:
  # 前置规则组
  pre-allocation:
    - rule-id: "ORDER_VALIDATION"
      name: "订单有效性检查"
      priority: 1
      blocking: true
      condition: "order.status == 'PENDING' && order.items.size() > 0"
      action: "ALLOW_ALLOCATION"
      failure-action: "REJECT_ALLOCATION"
      error-message: "订单状态无效或商品为空"

    - rule-id: "CUSTOMER_CREDIT_CHECK"
      name: "客户信用检查"
      priority: 2
      blocking: true
      condition: "customer.creditScore >= 60 && customer.status == 'ACTIVE'"
      action: "ALLOW_ALLOCATION"
      failure-action: "REJECT_ALLOCATION"
      error-message: "客户信用不足或状态异常"

  # 分配决策规则组
  allocation-decision:
    - rule-id: "STOCK_AVAILABILITY"
      name: "库存可用性检查"
      priority: 1
      blocking: true
      condition: "warehouse.availableStock >= order.totalQuantity"
      action: "ALLOW_ALLOCATION"
      failure-action: "EXCLUDE_WAREHOUSE"
      weight-adjustment: 0.0

    - rule-id: "DISTANCE_PREFERENCE"
      name: "距离偏好规则"
      priority: 2
      blocking: false
      condition: "distance <= 5000"  # 5公里内
      action: "PREFER_ALLOCATION"
      failure-action: "DEPRIORITIZE_ALLOCATION"
      weight-adjustment: 0.2

    - rule-id: "LOAD_BALANCE"
      name: "负载均衡规则"
      priority: 3
      blocking: false
      condition: "warehouse.currentLoad < warehouse.maxLoad * 0.8"
      action: "PREFER_ALLOCATION"
      failure-action: "DEPRIORITIZE_ALLOCATION"
      weight-adjustment: 0.15

  # 后置规则组
  post-allocation:
    - rule-id: "ALLOCATION_NOTIFICATION"
      name: "分配通知规则"
      priority: 1
      blocking: false
      condition: "allocation.success == true"
      action: "SEND_NOTIFICATION"
      parameters:
        notification-type: "ORDER_ALLOCATED"
        recipients: ["warehouse.manager", "customer"]

    - rule-id: "INVENTORY_RESERVATION"
      name: "库存预留规则"
      priority: 2
      blocking: true
      condition: "allocation.success == true"
      action: "RESERVE_INVENTORY"
      failure-action: "ROLLBACK_ALLOCATION"
      timeout: "300s"  # 5分钟预留时间
```

## 4. 异常处理机制设计完善

### 4.1 异常分类体系设计

```java
/**
 * 业务异常类型枚举
 */
public enum BusinessExceptionType {
    // 库存相关异常
    INSUFFICIENT_STOCK("INVENTORY_001", "库存不足", Severity.HIGH, RetryStrategy.ALTERNATIVE_ALLOCATION),
    INVENTORY_SYNC_FAILED("INVENTORY_002", "库存同步失败", Severity.MEDIUM, RetryStrategy.EXPONENTIAL_BACKOFF),
    INVENTORY_CONFLICT("INVENTORY_003", "库存冲突", Severity.HIGH, RetryStrategy.MANUAL_INTERVENTION),
    INVENTORY_LOCK_TIMEOUT("INVENTORY_004", "库存锁定超时", Severity.MEDIUM, RetryStrategy.IMMEDIATE_RETRY),

    // 订单相关异常
    ORDER_ALLOCATION_FAILED("ORDER_001", "订单分配失败", Severity.HIGH, RetryStrategy.ALTERNATIVE_ALLOCATION),
    ORDER_TIMEOUT("ORDER_002", "订单处理超时", Severity.MEDIUM, RetryStrategy.ESCALATION),
    ORDER_VALIDATION_FAILED("ORDER_003", "订单验证失败", Severity.LOW, RetryStrategy.MANUAL_INTERVENTION),

    // 前置仓相关异常
    WAREHOUSE_UNAVAILABLE("WAREHOUSE_001", "前置仓不可用", Severity.HIGH, RetryStrategy.ALTERNATIVE_ALLOCATION),
    WAREHOUSE_OVERLOAD("WAREHOUSE_002", "前置仓负载过高", Severity.MEDIUM, RetryStrategy.LOAD_BALANCE),
    WAREHOUSE_NETWORK_ERROR("WAREHOUSE_003", "前置仓网络异常", Severity.HIGH, RetryStrategy.CIRCUIT_BREAKER),

    // 系统相关异常
    NETWORK_TIMEOUT("SYSTEM_001", "网络超时", Severity.MEDIUM, RetryStrategy.EXPONENTIAL_BACKOFF),
    SERVICE_UNAVAILABLE("SYSTEM_002", "服务不可用", Severity.HIGH, RetryStrategy.CIRCUIT_BREAKER),
    DATA_CORRUPTION("SYSTEM_003", "数据损坏", Severity.CRITICAL, RetryStrategy.MANUAL_INTERVENTION),
    TRANSACTION_ROLLBACK("SYSTEM_004", "事务回滚", Severity.HIGH, RetryStrategy.COMPENSATION);

    private final String code;
    private final String description;
    private final Severity severity;
    private final RetryStrategy retryStrategy;
}

/**
 * 异常严重程度
 */
public enum Severity {
    LOW(1), MEDIUM(2), HIGH(3), CRITICAL(4);
    private final int level;
}

/**
 * 重试策略
 */
public enum RetryStrategy {
    IMMEDIATE_RETRY,        // 立即重试
    EXPONENTIAL_BACKOFF,    // 指数退避重试
    ALTERNATIVE_ALLOCATION, // 替代分配
    MANUAL_INTERVENTION,    // 人工干预
    ESCALATION,            // 升级处理
    CIRCUIT_BREAKER,       // 熔断处理
    LOAD_BALANCE,          // 负载均衡
    COMPENSATION           // 补偿处理
}
```

### 4.2 统一异常处理框架设计

```java
@Component
public class UnifiedExceptionHandler {

    @Autowired
    private RetryService retryService;

    @Autowired
    private AlertService alertService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private CompensationService compensationService;

    /**
     * 统一异常处理入口
     */
    public ExceptionHandlingResult handleException(BusinessException exception, Object context) {
        // 1. 记录异常审计日志
        ExceptionAuditRecord auditRecord = auditService.recordException(exception, context);

        // 2. 根据异常类型选择处理策略
        ExceptionHandlingStrategy strategy = getHandlingStrategy(exception.getType());

        // 3. 执行处理策略
        ExceptionHandlingResult result = strategy.handle(exception, context);

        // 4. 更新审计记录
        auditService.updateAuditRecord(auditRecord.getId(), result);

        // 5. 发送告警（如果需要）
        if (shouldSendAlert(exception, result)) {
            alertService.sendAlert(createAlert(exception, result));
        }

        // 6. 执行补偿操作（如果需要）
        if (result.needsCompensation()) {
            compensationService.executeCompensation(result.getCompensationPlan());
        }

        return result;
    }

    /**
     * 获取异常处理策略
     */
    private ExceptionHandlingStrategy getHandlingStrategy(BusinessExceptionType type) {
        switch (type.getRetryStrategy()) {
            case IMMEDIATE_RETRY:
                return new ImmediateRetryStrategy();
            case EXPONENTIAL_BACKOFF:
                return new ExponentialBackoffStrategy();
            case ALTERNATIVE_ALLOCATION:
                return new AlternativeAllocationStrategy();
            case MANUAL_INTERVENTION:
                return new ManualInterventionStrategy();
            case ESCALATION:
                return new EscalationStrategy();
            case CIRCUIT_BREAKER:
                return new CircuitBreakerStrategy();
            case LOAD_BALANCE:
                return new LoadBalanceStrategy();
            case COMPENSATION:
                return new CompensationStrategy();
            default:
                return new DefaultHandlingStrategy();
        }
    }

    /**
     * 替代分配策略
     */
    @Component
    public static class AlternativeAllocationStrategy implements ExceptionHandlingStrategy {

        @Autowired
        private OrderAllocationService allocationService;

        @Override
        public ExceptionHandlingResult handle(BusinessException exception, Object context) {
            if (!(context instanceof Order)) {
                return ExceptionHandlingResult.failed("Context is not an Order");
            }

            Order order = (Order) context;

            try {
                // 1. 排除失败的前置仓
                List<Long> excludedWarehouses = getExcludedWarehouses(exception);

                // 2. 重新分配订单
                AllocationResult newAllocation = allocationService.reallocateOrder(
                    order, excludedWarehouses);

                if (newAllocation.isSuccess()) {
                    return ExceptionHandlingResult.success()
                        .withMessage("Successfully reallocated to warehouse: " +
                                   newAllocation.getWarehouse().getId())
                        .withData(newAllocation);
                } else {
                    return ExceptionHandlingResult.failed("All alternative allocations failed")
                        .withNeedsEscalation(true);
                }

            } catch (Exception e) {
                return ExceptionHandlingResult.failed("Alternative allocation failed: " + e.getMessage())
                    .withNeedsManualIntervention(true);
            }
        }
    }

    /**
     * 补偿策略
     */
    @Component
    public static class CompensationStrategy implements ExceptionHandlingStrategy {

        @Autowired
        private CompensationService compensationService;

        @Override
        public ExceptionHandlingResult handle(BusinessException exception, Object context) {
            try {
                // 1. 分析需要补偿的操作
                List<CompensationAction> actions = analyzeCompensationNeeds(exception, context);

                // 2. 创建补偿计划
                CompensationPlan plan = compensationService.createCompensationPlan(actions);

                // 3. 执行补偿
                CompensationResult result = compensationService.executeCompensation(plan);

                if (result.isSuccess()) {
                    return ExceptionHandlingResult.success()
                        .withMessage("Compensation completed successfully")
                        .withCompensationResult(result);
                } else {
                    return ExceptionHandlingResult.failed("Compensation failed")
                        .withNeedsManualIntervention(true)
                        .withCompensationResult(result);
                }

            } catch (Exception e) {
                return ExceptionHandlingResult.failed("Compensation execution failed: " + e.getMessage())
                    .withNeedsManualIntervention(true);
            }
        }

        private List<CompensationAction> analyzeCompensationNeeds(
                BusinessException exception, Object context) {
            List<CompensationAction> actions = new ArrayList<>();

            // 根据异常类型确定补偿操作
            switch (exception.getType()) {
                case TRANSACTION_ROLLBACK:
                    if (context instanceof AllocationRequest) {
                        actions.add(new ReleaseInventoryLockAction((AllocationRequest) context));
                        actions.add(new CancelReservationAction((AllocationRequest) context));
                    }
                    break;
                case INVENTORY_SYNC_FAILED:
                    actions.add(new ResyncInventoryAction(context));
                    break;
                case ORDER_ALLOCATION_FAILED:
                    actions.add(new RestoreOrderStatusAction((Order) context));
                    break;
            }

            return actions;
        }
    }
}
```

## 5. 业务规则引擎设计

### 5.1 规则引擎架构设计

```java
@Service
public class BusinessRuleEngine {

    @Autowired
    private RuleRepository ruleRepository;

    @Autowired
    private RuleExecutor ruleExecutor;

    @Autowired
    private RuleCache ruleCache;

    /**
     * 执行业务规则组
     */
    public RuleExecutionResult executeRules(String ruleGroup, Object context) {
        // 1. 获取规则组（优先从缓存获取）
        List<BusinessRule> rules = ruleCache.getRules(ruleGroup);
        if (rules == null) {
            rules = ruleRepository.getRulesByGroup(ruleGroup);
            ruleCache.cacheRules(ruleGroup, rules);
        }

        // 2. 过滤启用的规则并按优先级排序
        List<BusinessRule> enabledRules = rules.stream()
            .filter(BusinessRule::isEnabled)
            .sorted(Comparator.comparing(BusinessRule::getPriority))
            .collect(Collectors.toList());

        // 3. 执行规则
        RuleExecutionResult result = new RuleExecutionResult();
        result.setRuleGroup(ruleGroup);
        result.setExecutionTime(LocalDateTime.now());

        for (BusinessRule rule : enabledRules) {
            if (rule.matches(context)) {
                RuleResult ruleResult = ruleExecutor.execute(rule, context);
                result.addRuleResult(ruleResult);

                // 记录规则执行指标
                recordRuleMetrics(rule, ruleResult);

                // 如果是阻断规则且失败，停止执行
                if (rule.isBlocking() && !ruleResult.isSuccess()) {
                    result.setBlocked(true);
                    result.setBlockReason(ruleResult.getErrorMessage());
                    break;
                }

                // 应用权重调整
                if (ruleResult.hasWeightAdjustment()) {
                    result.addWeightAdjustment(rule.getId(), ruleResult.getWeightAdjustment());
                }
            }
        }

        return result;
    }

    /**
     * 动态添加规则
     */
    public void addRule(BusinessRule rule) {
        // 1. 验证规则
        validateRule(rule);

        // 2. 保存规则
        ruleRepository.save(rule);

        // 3. 清除相关缓存
        ruleCache.evictRuleGroup(rule.getRuleGroup());

        // 4. 记录规则变更日志
        auditService.recordRuleChange("ADD", rule);
    }

    /**
     * 动态更新规则
     */
    public void updateRule(BusinessRule rule) {
        // 1. 验证规则
        validateRule(rule);

        // 2. 更新规则
        BusinessRule oldRule = ruleRepository.findById(rule.getId());
        ruleRepository.update(rule);

        // 3. 清除相关缓存
        ruleCache.evictRuleGroup(rule.getRuleGroup());

        // 4. 记录规则变更日志
        auditService.recordRuleChange("UPDATE", oldRule, rule);
    }
}
```

## 6. 跨服务协调机制设计

### 6.1 Saga模式实现设计

```java
@Service
public class OrderFulfillmentSaga {

    @Autowired
    private SagaManager sagaManager;

    @Autowired
    private SagaStateRepository sagaStateRepository;

    /**
     * 订单履约Saga流程
     */
    public SagaExecutionResult executeOrderFulfillment(Order order) {
        // 1. 创建Saga定义
        SagaDefinition saga = createOrderFulfillmentSaga(order);

        // 2. 初始化Saga状态
        SagaState sagaState = sagaStateRepository.initializeSaga(saga.getSagaId(), order);

        // 3. 执行Saga
        try {
            SagaExecutionResult result = sagaManager.execute(saga, order);

            // 4. 更新最终状态
            sagaStateRepository.updateSagaState(saga.getSagaId(), result);

            return result;

        } catch (SagaExecutionException e) {
            // 5. 处理Saga执行异常
            handleSagaFailure(saga, e);
            throw e;
        }
    }

    /**
     * 创建订单履约Saga定义
     */
    private SagaDefinition createOrderFulfillmentSaga(Order order) {
        return SagaDefinition.builder()
            .sagaId("order-fulfillment-" + order.getId())
            .sagaType("ORDER_FULFILLMENT")
            .timeout(Duration.ofMinutes(30))

            // 步骤1：分配前置仓
            .step("allocateWarehouse")
                .description("分配最优前置仓")
                .invokeParticipant("front-warehouse-service", "allocateOrder")
                .withCompensation("front-warehouse-service", "releaseAllocation")
                .timeout(Duration.ofSeconds(30))
                .retryPolicy(RetryPolicy.exponentialBackoff(3, Duration.ofSeconds(1)))

            // 步骤2：预留库存
            .step("reserveInventory")
                .description("预留订单商品库存")
                .invokeParticipant("inventory-service", "reserveStock")
                .withCompensation("inventory-service", "releaseReservation")
                .timeout(Duration.ofSeconds(15))
                .retryPolicy(RetryPolicy.fixedDelay(2, Duration.ofSeconds(2)))
                .dependsOn("allocateWarehouse")

            // 步骤3：创建拣选任务
            .step("createPickingTask")
                .description("创建拣选作业任务")
                .invokeParticipant("front-warehouse-service", "createPickingTask")
                .withCompensation("front-warehouse-service", "cancelPickingTask")
                .timeout(Duration.ofSeconds(10))
                .dependsOn("reserveInventory")

            // 步骤4：创建打包任务
            .step("createPackingTask")
                .description("创建打包作业任务")
                .invokeParticipant("front-warehouse-service", "createPackingTask")
                .withCompensation("front-warehouse-service", "cancelPackingTask")
                .timeout(Duration.ofSeconds(10))
                .dependsOn("createPickingTask")

            // 步骤5：调度配送
            .step("scheduleDelivery")
                .description("调度配送任务")
                .invokeParticipant("front-warehouse-service", "scheduleDelivery")
                .withCompensation("front-warehouse-service", "cancelDelivery")
                .timeout(Duration.ofSeconds(20))
                .dependsOn("createPackingTask")

            // 步骤6：通知客户
            .step("notifyCustomer")
                .description("通知客户订单状态")
                .invokeParticipant("notification-service", "sendOrderNotification")
                .withCompensation("notification-service", "sendCancellationNotification")
                .timeout(Duration.ofSeconds(5))
                .dependsOn("scheduleDelivery")
                .optional(true)  // 可选步骤，失败不影响主流程

            .build();
    }

    /**
     * 处理Saga执行失败
     */
    private void handleSagaFailure(SagaDefinition saga, SagaExecutionException e) {
        // 1. 记录失败日志
        log.error("Saga execution failed: {}", saga.getSagaId(), e);

        // 2. 更新Saga状态
        sagaStateRepository.markSagaFailed(saga.getSagaId(), e.getMessage());

        // 3. 发送告警
        alertService.sendSagaFailureAlert(saga, e);

        // 4. 如果需要，触发人工干预
        if (e.needsManualIntervention()) {
            manualInterventionService.createIntervention(saga, e);
        }
    }
}
```

### 6.2 分布式锁机制设计

```java
@Service
public class DistributedLockService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private LockMetricsCollector metricsCollector;

    /**
     * 获取分布式锁
     */
    public LockResult acquireLock(LockRequest request) {
        String lockKey = buildLockKey(request);
        String lockValue = generateLockValue(request);

        Timer.Sample sample = Timer.start(metricsCollector.getMeterRegistry());

        try {
            // 1. 尝试获取锁
            Boolean acquired = redisTemplate.opsForValue().setIfAbsent(
                lockKey,
                lockValue,
                Duration.ofMillis(request.getExpireTime())
            );

            if (Boolean.TRUE.equals(acquired)) {
                // 2. 获取成功，记录锁信息
                LockInfo lockInfo = LockInfo.builder()
                    .lockKey(lockKey)
                    .lockValue(lockValue)
                    .acquiredTime(LocalDateTime.now())
                    .expireTime(LocalDateTime.now().plusNanos(request.getExpireTime() * 1_000_000))
                    .owner(request.getOwner())
                    .purpose(request.getPurpose())
                    .build();

                metricsCollector.recordLockAcquired(request.getLockType());

                return LockResult.success(lockInfo);

            } else {
                // 3. 获取失败，检查锁状态
                String existingValue = redisTemplate.opsForValue().get(lockKey);
                LockInfo existingLock = parseLockValue(existingValue);

                metricsCollector.recordLockFailed(request.getLockType());

                return LockResult.failed("Lock already held by: " + existingLock.getOwner())
                    .withExistingLock(existingLock);
            }

        } catch (Exception e) {
            metricsCollector.recordLockError(request.getLockType());
            return LockResult.error("Failed to acquire lock: " + e.getMessage());

        } finally {
            sample.stop(metricsCollector.getLockAcquisitionTimer());
        }
    }

    /**
     * 释放分布式锁
     */
    public boolean releaseLock(LockInfo lockInfo) {
        String script =
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('del', KEYS[1]) " +
            "else " +
            "    return 0 " +
            "end";

        try {
            Boolean result = redisTemplate.execute(
                RedisScript.of(script, Boolean.class),
                Collections.singletonList(lockInfo.getLockKey()),
                lockInfo.getLockValue()
            );

            if (Boolean.TRUE.equals(result)) {
                metricsCollector.recordLockReleased(lockInfo.getLockType());
                return true;
            } else {
                metricsCollector.recordLockReleaseFailed(lockInfo.getLockType());
                return false;
            }

        } catch (Exception e) {
            log.error("Failed to release lock: {}", lockInfo.getLockKey(), e);
            metricsCollector.recordLockError(lockInfo.getLockType());
            return false;
        }
    }

    /**
     * 构建锁键名
     */
    private String buildLockKey(LockRequest request) {
        return String.format("lock:%s:%s:%s",
            request.getLockType().name().toLowerCase(),
            request.getResourceType(),
            request.getResourceId()
        );
    }

    /**
     * 生成锁值
     */
    private String generateLockValue(LockRequest request) {
        return String.format("%s:%s:%d",
            request.getOwner(),
            request.getPurpose(),
            System.currentTimeMillis()
        );
    }
}
```

## 7. 监控和指标设计

### 7.1 业务指标监控设计

```java
@Component
public class BusinessMetricsCollector {

    private final MeterRegistry meterRegistry;

    // 库存同步指标
    private final Counter inventorySyncSuccessCounter;
    private final Counter inventorySyncFailureCounter;
    private final Timer inventorySyncDurationTimer;

    // 订单分配指标
    private final Timer orderAllocationTimer;
    private final Counter orderAllocationSuccessCounter;
    private final Counter orderAllocationFailureCounter;
    private final Gauge activeOrdersGauge;

    // 异常处理指标
    private final Counter exceptionCounter;
    private final Timer exceptionHandlingTimer;

    // Saga执行指标
    private final Counter sagaExecutionCounter;
    private final Timer sagaExecutionTimer;
    private final Counter sagaCompensationCounter;

    public BusinessMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 初始化指标
        this.inventorySyncSuccessCounter = Counter.builder("inventory.sync.success")
            .description("库存同步成功次数")
            .register(meterRegistry);

        this.inventorySyncFailureCounter = Counter.builder("inventory.sync.failure")
            .description("库存同步失败次数")
            .register(meterRegistry);

        this.inventorySyncDurationTimer = Timer.builder("inventory.sync.duration")
            .description("库存同步耗时")
            .register(meterRegistry);

        this.orderAllocationTimer = Timer.builder("order.allocation.duration")
            .description("订单分配耗时")
            .register(meterRegistry);

        this.orderAllocationSuccessCounter = Counter.builder("order.allocation.success")
            .description("订单分配成功次数")
            .register(meterRegistry);

        this.orderAllocationFailureCounter = Counter.builder("order.allocation.failure")
            .description("订单分配失败次数")
            .register(meterRegistry);

        this.activeOrdersGauge = Gauge.builder("orders.active")
            .description("当前活跃订单数")
            .register(meterRegistry, this, BusinessMetricsCollector::getActiveOrderCount);

        this.exceptionCounter = Counter.builder("business.exception")
            .description("业务异常计数")
            .tag("type", "unknown")
            .register(meterRegistry);

        this.exceptionHandlingTimer = Timer.builder("exception.handling.duration")
            .description("异常处理耗时")
            .register(meterRegistry);

        this.sagaExecutionCounter = Counter.builder("saga.execution")
            .description("Saga执行计数")
            .register(meterRegistry);

        this.sagaExecutionTimer = Timer.builder("saga.execution.duration")
            .description("Saga执行耗时")
            .register(meterRegistry);

        this.sagaCompensationCounter = Counter.builder("saga.compensation")
            .description("Saga补偿执行计数")
            .register(meterRegistry);
    }

    // 指标记录方法
    public void recordInventorySyncSuccess() {
        inventorySyncSuccessCounter.increment();
    }

    public void recordInventorySyncFailure() {
        inventorySyncFailureCounter.increment();
    }

    public void recordInventorySyncDuration(Duration duration) {
        inventorySyncDurationTimer.record(duration);
    }

    public void recordOrderAllocationSuccess() {
        orderAllocationSuccessCounter.increment();
    }

    public void recordOrderAllocationFailure() {
        orderAllocationFailureCounter.increment();
    }

    public Timer.Sample startOrderAllocationTimer() {
        return Timer.start(meterRegistry);
    }

    public void recordException(BusinessExceptionType type) {
        Counter.builder("business.exception")
            .tag("type", type.name())
            .tag("severity", type.getSeverity().name())
            .register(meterRegistry)
            .increment();
    }

    public void recordSagaExecution(String sagaType, SagaExecutionResult result) {
        sagaExecutionCounter.increment(
            Tags.of(
                "saga_type", sagaType,
                "status", result.getStatus().name()
            )
        );
    }

    private double getActiveOrderCount() {
        // 实际实现中从订单服务获取活跃订单数
        return orderService.getActiveOrderCount();
    }
}
```

### 7.2 告警规则设计

```yaml
# 业务流程告警规则配置
alert-rules:
  # 库存同步告警
  - alert-name: "inventory-sync-failure-rate-high"
    description: "库存同步失败率过高"
    condition: "rate(inventory_sync_failure_total[5m]) / rate(inventory_sync_total[5m]) > 0.05"
    severity: "WARNING"
    duration: "2m"
    labels:
      team: "inventory"
      service: "front-warehouse"
    annotations:
      summary: "库存同步失败率超过5%"
      description: "在过去5分钟内，库存同步失败率为 {{ $value | humanizePercentage }}"
    actions:
      - type: "EMAIL"
        recipients: ["<EMAIL>"]
      - type: "WEBHOOK"
        url: "http://alert-manager/webhook/inventory"

  # 订单分配延迟告警
  - alert-name: "order-allocation-latency-high"
    description: "订单分配延迟过高"
    condition: "histogram_quantile(0.95, order_allocation_duration_seconds) > 5"
    severity: "CRITICAL"
    duration: "1m"
    labels:
      team: "fulfillment"
      service: "front-warehouse"
    annotations:
      summary: "订单分配P95延迟超过5秒"
      description: "订单分配P95延迟为 {{ $value }}秒"
    actions:
      - type: "SMS"
        recipients: ["+86138****1234"]
      - type: "ESCALATION"
        escalation-policy: "on-call-engineer"

  # Saga执行失败告警
  - alert-name: "saga-execution-failure-rate-high"
    description: "Saga执行失败率过高"
    condition: "rate(saga_execution_total{status=\"FAILED\"}[10m]) / rate(saga_execution_total[10m]) > 0.1"
    severity: "HIGH"
    duration: "3m"
    labels:
      team: "platform"
      service: "saga-manager"
    annotations:
      summary: "Saga执行失败率超过10%"
      description: "在过去10分钟内，Saga执行失败率为 {{ $value | humanizePercentage }}"
    actions:
      - type: "SLACK"
        channel: "#platform-alerts"
      - type: "AUTO_ESCALATION"
        escalation-delay: "5m"
```

## 8. 设计完善总结

### 8.1 完善内容概述

本文档完善了PISP系统业务流程设计中的关键缺失部分：

**🎯 主要完善内容：**

1. **数据流向设计完善**
   - 明确了前置仓与中心仓库的数据同步机制
   - 设计了完整的冲突检测和解决策略
   - 建立了分布式事务一致性保证机制

2. **订单分配逻辑设计完善**
   - 实现了多维度智能评分算法
   - 设计了灵活的业务规则配置机制
   - 建立了完整的分配决策流程

3. **异常处理机制设计完善**
   - 建立了统一的异常分类体系
   - 设计了多种异常处理策略
   - 实现了自动化的异常恢复机制

4. **跨服务协调机制设计完善**
   - 采用Saga模式实现分布式事务协调
   - 设计了分布式锁机制保证数据一致性
   - 建立了完整的补偿机制

5. **业务规则引擎设计**
   - 实现了灵活的规则配置和执行框架
   - 支持动态规则更新和缓存机制
   - 建立了规则执行的监控和审计

6. **监控和告警设计**
   - 设计了全面的业务指标监控
   - 建立了智能化的告警规则
   - 实现了多渠道的告警通知机制

### 8.2 设计原则遵循

**✅ 设计原则：**
- **完整性**：覆盖了业务流程的所有关键环节
- **一致性**：确保了跨服务的数据和状态一致性
- **可靠性**：建立了完善的异常处理和恢复机制
- **可扩展性**：支持业务规则的动态配置和扩展
- **可观测性**：提供了全面的监控和告警能力

### 8.3 后续实施建议

**📋 实施优先级：**

1. **高优先级**：数据流向设计和异常处理机制
2. **中优先级**：订单分配逻辑和跨服务协调机制
3. **低优先级**：业务规则引擎和监控告警系统

**🔄 迭代计划：**
- **第一阶段**：实现核心数据流和异常处理
- **第二阶段**：完善订单分配和服务协调
- **第三阶段**：集成规则引擎和监控系统

通过这些设计完善，PISP系统的业务流程将更加完整、可靠和可维护。

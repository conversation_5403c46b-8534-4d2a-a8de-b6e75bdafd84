# DDD-011 前置仓管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-011 |
| 文档名称 | 前置仓管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-07-02 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 草稿 |
| 作者 | 系统架构师 |

## 1. 模块概述

前置仓管理模块负责管理分布式前置仓的库存业务，包括前置仓管理、库存分配、智能补货和库存同步等功能。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "前置仓管理模块"
        A[前置仓管理服务]
        B[库存分配服务]
        C[智能补货服务]
        D[库存同步服务]
        E[预测分析服务]
        F[订单履约服务]
        G[拣选管理服务]
        H[打包发货服务]
        I[配送调度服务]
    end

    subgraph "核心功能"
        J[前置仓基础管理]
        K[智能库存分配]
        L[自动补货策略]
        M[实时库存同步]
        N[需求预测分析]
        O[订单智能分配]
        P[高效拣选作业]
        Q[智能打包发货]
        R[优化配送调度]
    end

    A --> J
    B --> K
    C --> L
    D --> M
    E --> N
    F --> O
    G --> P
    H --> Q
    I --> R
```

## 2. 核心业务流程

### 2.1 库存分配流程

```mermaid
flowchart TD
    A[触发分配] --> B[收集数据]
    B --> C[需求预测]
    C --> D[计算分配方案]
    D --> E[方案优化]
    E --> F[生成分配单]
    F --> G[审核确认]
    G --> H{审核通过?}
    H -->|是| I[执行分配]
    H -->|否| J[调整方案]
    J --> F
    I --> K[更新库存]
    K --> L[分配完成]
    
    subgraph "数据收集"
        B1[历史销售数据]
        B2[当前库存数据]
        B3[季节性因素]
        B4[促销计划]
    end
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
```

**流程说明：**
1. **触发分配**：定时任务或手动触发库存分配
2. **收集数据**：收集历史销售、当前库存、季节性因素等数据
3. **需求预测**：基于机器学习算法预测各前置仓需求
4. **计算分配方案**：根据预测需求和库存约束计算最优分配
5. **方案优化**：考虑物流成本、库存周转等因素优化方案
6. **生成分配单**：生成详细的库存分配单据
7. **审核确认**：人工审核分配方案的合理性
8. **执行分配**：执行库存分配，更新各仓库库存

### 2.2 智能补货流程

```mermaid
flowchart TD
    A[库存监控] --> B{触发条件?}
    B -->|低于安全库存| C[计算补货需求]
    B -->|预测缺货| C
    B -->|定期补货| C
    B -->|否| A
    
    C --> D[选择补货策略]
    D --> E[计算补货数量]
    E --> F[选择补货来源]
    F --> G[生成补货订单]
    G --> H[发送补货请求]
    H --> I[跟踪补货状态]
    I --> J[确认收货]
    J --> K[更新库存]
    K --> L[补货完成]
    
    subgraph "补货策略"
        D1[定量补货]
        D2[定期补货]
        D3[预测补货]
        D4[紧急补货]
    end
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
```

**补货触发条件：**
- **库存预警**：当前库存低于安全库存
- **预测缺货**：基于销售预测可能出现缺货
- **定期补货**：按固定周期进行补货
- **紧急补货**：突发情况下的紧急补货需求

### 2.3 库存同步流程

```mermaid
sequenceDiagram
    participant FW as 前置仓
    participant MQ as 消息队列
    participant CS as 中心系统
    participant DB as 数据库
    participant Cache as 缓存

    Note over FW,Cache: 正常同步流程
    FW->>MQ: 库存变动事件
    MQ->>CS: 转发事件
    CS->>DB: 更新库存
    CS->>Cache: 更新缓存
    DB-->>CS: 确认更新
    CS->>MQ: 同步确认
    MQ->>FW: 同步完成

    Note over FW,Cache: 异常处理流程
    FW->>MQ: 库存变动事件
    MQ->>CS: 转发事件
    CS->>DB: 更新失败
    CS->>MQ: 同步失败
    MQ->>FW: 重试通知
    FW->>FW: 本地缓存
    
    Note over FW,Cache: 网络恢复后
    FW->>MQ: 批量同步事件
    MQ->>CS: 批量处理
    CS->>DB: 批量更新
    CS->>Cache: 批量更新缓存
```

**同步机制特点：**
- **实时同步**：库存变动立即同步到中心系统
- **离线支持**：网络中断时本地缓存变动
- **批量恢复**：网络恢复后批量同步数据
- **冲突处理**：自动检测和处理数据冲突

### 2.4 订单履约流程

```mermaid
flowchart TD
    A[接收订单] --> B[订单分析]
    B --> C[库存检查]
    C --> D{库存充足?}
    D -->|是| E[分配前置仓]
    D -->|否| F[库存调拨/拆单]
    F --> E
    E --> G[生成拣选任务]
    G --> H[拣选作业]
    H --> I[质量检查]
    I --> J{质检通过?}
    J -->|否| K[异常处理]
    J -->|是| L[打包作业]
    K --> H
    L --> M[生成包裹]
    M --> N[配送调度]
    N --> O[配送执行]
    O --> P[确认收货]
    P --> Q[订单完成]

    subgraph "订单分配策略"
        E1[距离优先]
        E2[库存优先]
        E3[负载均衡]
        E4[时效优先]
    end

    E --> E1
    E --> E2
    E --> E3
    E --> E4
```

### 2.5 拣选作业流程

```mermaid
flowchart TD
    A[接收拣选任务] --> B[优化拣选路径]
    B --> C[分配拣选员]
    C --> D[开始拣选]
    D --> E[扫码商品位置]
    E --> F[确认商品信息]
    F --> G{商品正确?}
    G -->|是| H[扫码商品条码]
    G -->|否| I[报告异常]
    H --> J{数量正确?}
    J -->|是| K[放入拣选容器]
    J -->|否| L[调整数量]
    I --> M[寻找替代商品]
    M --> N{找到替代?}
    N -->|是| H
    N -->|否| O[标记缺货]
    L --> K
    K --> P{任务完成?}
    P -->|否| Q[下一个商品]
    P -->|是| R[提交拣选结果]
    Q --> E
    O --> P
    R --> S[移交打包]
```

### 2.6 打包发货流程

```mermaid
flowchart TD
    A[接收拣选商品] --> B[商品质检]
    B --> C{质检通过?}
    C -->|否| D[退回重新拣选]
    C -->|是| E[计算包装方案]
    E --> F[选择包装材料]
    F --> G[开始打包]
    G --> H[放入商品]
    H --> I[添加保护材料]
    I --> J[封装包裹]
    J --> K[称重检查]
    K --> L{重量正确?}
    L -->|否| M[重新打包]
    L -->|是| N[打印标签]
    M --> G
    N --> O[贴标签]
    O --> P[生成发货单]
    P --> Q[移交配送]

    subgraph "包装优化"
        E1[体积优化]
        E2[重量分配]
        E3[保护优化]
        E4[成本控制]
    end

    E --> E1
    E --> E2
    E --> E3
    E --> E4
```

### 2.7 配送调度流程

```mermaid
flowchart TD
    A[接收发货包裹] --> B[分析配送地址]
    B --> C[计算配送距离]
    C --> D[检查时间窗口]
    D --> E[规划配送路线]
    E --> F[分配配送员]
    F --> G[生成配送任务]
    G --> H[配送员接单]
    H --> I[装载包裹]
    I --> J[开始配送]
    J --> K[实时位置跟踪]
    K --> L[到达配送地址]
    L --> M[联系客户]
    M --> N{客户在家?}
    N -->|是| O[确认收货]
    N -->|否| P[预约重新配送]
    O --> Q[获取签收]
    Q --> R[拍照确认]
    R --> S[完成配送]
    P --> T[更新配送状态]
    S --> U[返回前置仓]

    subgraph "路线优化算法"
        E1[最短路径]
        E2[时间窗口约束]
        E3[容量约束]
        E4[动态调整]
    end

    E --> E1
    E --> E2
    E --> E3
    E --> E4
```

## 3. 核心算法设计

### 3.1 需求预测算法

```java
@Service
public class DemandForecastService {
    
    /**
     * 基于时间序列的需求预测
     */
    public ForecastResult forecastDemand(Long productId, Long frontWarehouseId, int forecastDays) {
        // 1. 获取历史销售数据
        List<SalesData> historicalData = getHistoricalSalesData(productId, frontWarehouseId);
        
        // 2. 数据预处理
        List<Double> processedData = preprocessData(historicalData);
        
        // 3. 应用ARIMA模型预测
        ARIMAModel model = new ARIMAModel();
        model.fit(processedData);
        
        // 4. 生成预测结果
        List<Double> forecast = model.forecast(forecastDays);
        
        // 5. 考虑季节性因素
        forecast = adjustForSeasonality(forecast, productId);
        
        // 6. 考虑促销影响
        forecast = adjustForPromotions(forecast, productId, frontWarehouseId);
        
        return new ForecastResult(forecast, model.getConfidenceInterval());
    }
    
    /**
     * 库存分配算法
     */
    public AllocationPlan calculateAllocation(List<FrontWarehouse> warehouses, 
                                            List<Product> products, 
                                            BigDecimal totalInventory) {
        
        AllocationPlan plan = new AllocationPlan();
        
        for (Product product : products) {
            // 1. 计算各前置仓需求预测
            Map<Long, BigDecimal> demandForecast = new HashMap<>();
            for (FrontWarehouse warehouse : warehouses) {
                ForecastResult forecast = forecastDemand(product.getId(), warehouse.getId(), 7);
                demandForecast.put(warehouse.getId(), forecast.getTotalDemand());
            }
            
            // 2. 计算总需求
            BigDecimal totalDemand = demandForecast.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 3. 按比例分配库存
            BigDecimal availableInventory = getAvailableInventory(product.getId());
            
            for (FrontWarehouse warehouse : warehouses) {
                BigDecimal demand = demandForecast.get(warehouse.getId());
                BigDecimal ratio = demand.divide(totalDemand, 4, RoundingMode.HALF_UP);
                BigDecimal allocation = availableInventory.multiply(ratio);
                
                // 4. 考虑最小最大库存约束
                allocation = applyStockConstraints(allocation, warehouse, product);
                
                plan.addAllocation(warehouse.getId(), product.getId(), allocation);
            }
        }
        
        return plan;
    }
}
```

### 3.2 补货策略算法

```java
@Service
public class ReplenishmentStrategyService {
    
    /**
     * 计算补货数量
     */
    public BigDecimal calculateReplenishmentQuantity(Long productId, 
                                                   Long frontWarehouseId,
                                                   ReplenishmentStrategy strategy) {
        
        FrontWarehouseInventory inventory = getInventory(productId, frontWarehouseId);
        
        switch (strategy.getType()) {
            case FIXED_QUANTITY:
                return calculateFixedQuantityReplenishment(inventory, strategy);
                
            case FIXED_PERIOD:
                return calculateFixedPeriodReplenishment(inventory, strategy);
                
            case PREDICTIVE:
                return calculatePredictiveReplenishment(inventory, strategy);
                
            case EMERGENCY:
                return calculateEmergencyReplenishment(inventory, strategy);
                
            default:
                throw new IllegalArgumentException("Unknown strategy type: " + strategy.getType());
        }
    }
    
    private BigDecimal calculatePredictiveReplenishment(FrontWarehouseInventory inventory, 
                                                      ReplenishmentStrategy strategy) {
        
        // 1. 预测未来需求
        ForecastResult forecast = demandForecastService.forecastDemand(
            inventory.getProductId(), 
            inventory.getFrontWarehouseId(), 
            strategy.getForecastDays()
        );
        
        // 2. 计算预期库存消耗
        BigDecimal expectedConsumption = forecast.getTotalDemand();
        
        // 3. 计算当前可用库存
        BigDecimal currentStock = inventory.getAvailableQuantity();
        
        // 4. 计算安全库存
        BigDecimal safetyStock = inventory.getMinStock();
        
        // 5. 计算补货数量
        BigDecimal replenishmentQuantity = expectedConsumption
            .add(safetyStock)
            .subtract(currentStock);
        
        // 6. 确保不超过最大库存
        BigDecimal maxStock = inventory.getMaxStock();
        if (currentStock.add(replenishmentQuantity).compareTo(maxStock) > 0) {
            replenishmentQuantity = maxStock.subtract(currentStock);
        }
        
        return replenishmentQuantity.max(BigDecimal.ZERO);
    }
}
```

### 3.3 订单分配算法

```java
@Service
public class OrderAssignmentService {

    /**
     * 智能订单分配算法
     */
    public FrontWarehouse assignOrder(Order order, List<FrontWarehouse> availableWarehouses) {

        // 1. 过滤有库存的前置仓
        List<FrontWarehouse> stockAvailableWarehouses = filterByStockAvailability(order, availableWarehouses);

        if (stockAvailableWarehouses.isEmpty()) {
            throw new InsufficientStockException("No warehouse has sufficient stock for order: " + order.getId());
        }

        // 2. 计算各前置仓的评分
        Map<FrontWarehouse, Double> warehouseScores = new HashMap<>();

        for (FrontWarehouse warehouse : stockAvailableWarehouses) {
            double score = calculateWarehouseScore(order, warehouse);
            warehouseScores.put(warehouse, score);
        }

        // 3. 选择评分最高的前置仓
        return warehouseScores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElseThrow(() -> new RuntimeException("Failed to assign warehouse"));
    }

    private double calculateWarehouseScore(Order order, FrontWarehouse warehouse) {
        // 距离评分 (40%)
        double distanceScore = calculateDistanceScore(order.getDeliveryAddress(), warehouse);

        // 库存评分 (30%)
        double stockScore = calculateStockScore(order, warehouse);

        // 负载评分 (20%)
        double loadScore = calculateLoadScore(warehouse);

        // 时效评分 (10%)
        double timeScore = calculateTimeScore(order, warehouse);

        return distanceScore * 0.4 + stockScore * 0.3 + loadScore * 0.2 + timeScore * 0.1;
    }

    private double calculateDistanceScore(Address deliveryAddress, FrontWarehouse warehouse) {
        double distance = geoService.calculateDistance(
            deliveryAddress.getLatitude(), deliveryAddress.getLongitude(),
            warehouse.getLatitude(), warehouse.getLongitude()
        );

        // 距离越近评分越高，最大距离10km
        return Math.max(0, (10.0 - distance) / 10.0);
    }

    private double calculateStockScore(Order order, FrontWarehouse warehouse) {
        double totalStockRatio = 0;
        int itemCount = 0;

        for (OrderItem item : order.getItems()) {
            FrontWarehouseInventory inventory = getInventory(warehouse.getId(), item.getProductId());
            double stockRatio = Math.min(1.0, inventory.getAvailableQuantity().doubleValue() / item.getQuantity().doubleValue());
            totalStockRatio += stockRatio;
            itemCount++;
        }

        return itemCount > 0 ? totalStockRatio / itemCount : 0;
    }
}
```

### 3.4 拣选路径优化算法

```java
@Service
public class PickingPathOptimizationService {

    /**
     * 优化拣选路径
     */
    public List<PickingLocation> optimizePickingPath(List<PickingTaskItem> items, FrontWarehouse warehouse) {

        // 1. 获取商品位置信息
        List<PickingLocation> locations = getPickingLocations(items, warehouse);

        // 2. 应用TSP算法优化路径
        List<PickingLocation> optimizedPath = solveTSP(locations, warehouse.getStartLocation());

        // 3. 考虑拣选约束
        optimizedPath = applyPickingConstraints(optimizedPath);

        return optimizedPath;
    }

    private List<PickingLocation> solveTSP(List<PickingLocation> locations, Location startLocation) {
        int n = locations.size();
        if (n <= 1) return locations;

        // 使用动态规划解决TSP问题
        double[][] distances = calculateDistanceMatrix(locations, startLocation);

        // DP状态：dp[mask][i] 表示访问了mask中的点，当前在点i的最短距离
        double[][] dp = new double[1 << n][n];
        int[][] parent = new int[1 << n][n];

        // 初始化
        for (int i = 0; i < (1 << n); i++) {
            Arrays.fill(dp[i], Double.MAX_VALUE);
            Arrays.fill(parent[i], -1);
        }

        // 从起始点开始
        for (int i = 0; i < n; i++) {
            dp[1 << i][i] = distances[n][i]; // n是起始点的索引
        }

        // 动态规划
        for (int mask = 1; mask < (1 << n); mask++) {
            for (int u = 0; u < n; u++) {
                if ((mask & (1 << u)) == 0 || dp[mask][u] == Double.MAX_VALUE) continue;

                for (int v = 0; v < n; v++) {
                    if (mask & (1 << v)) continue;

                    int newMask = mask | (1 << v);
                    double newDist = dp[mask][u] + distances[u][v];

                    if (newDist < dp[newMask][v]) {
                        dp[newMask][v] = newDist;
                        parent[newMask][v] = u;
                    }
                }
            }
        }

        // 重构路径
        return reconstructPath(parent, dp, locations, n);
    }

    private List<PickingLocation> applyPickingConstraints(List<PickingLocation> path) {
        List<PickingLocation> constrainedPath = new ArrayList<>();

        // 按商品类型分组，冷冻商品最后拣选
        Map<ProductType, List<PickingLocation>> typeGroups = path.stream()
            .collect(Collectors.groupingBy(loc -> loc.getProduct().getType()));

        // 先拣选常温商品
        constrainedPath.addAll(typeGroups.getOrDefault(ProductType.NORMAL, Collections.emptyList()));

        // 再拣选冷藏商品
        constrainedPath.addAll(typeGroups.getOrDefault(ProductType.REFRIGERATED, Collections.emptyList()));

        // 最后拣选冷冻商品
        constrainedPath.addAll(typeGroups.getOrDefault(ProductType.FROZEN, Collections.emptyList()));

        return constrainedPath;
    }
}
```

### 3.5 配送路线优化算法

```java
@Service
public class DeliveryRouteOptimizationService {

    /**
     * 优化配送路线
     */
    public DeliveryRoute optimizeDeliveryRoute(List<DeliveryTask> tasks, Vehicle vehicle) {

        // 1. 考虑时间窗口约束
        List<DeliveryTask> feasibleTasks = filterByTimeWindow(tasks);

        // 2. 考虑车辆容量约束
        List<DeliveryTask> capacityFeasibleTasks = filterByCapacity(feasibleTasks, vehicle);

        // 3. 应用VRP算法优化路线
        DeliveryRoute optimizedRoute = solveVRP(capacityFeasibleTasks, vehicle);

        return optimizedRoute;
    }

    private DeliveryRoute solveVRP(List<DeliveryTask> tasks, Vehicle vehicle) {
        // 使用遗传算法解决VRP问题
        GeneticAlgorithm ga = new GeneticAlgorithm();

        // 初始化种群
        List<Route> population = ga.initializePopulation(tasks, 100);

        // 进化
        for (int generation = 0; generation < 1000; generation++) {
            // 选择
            List<Route> selected = ga.selection(population);

            // 交叉
            List<Route> offspring = ga.crossover(selected);

            // 变异
            ga.mutation(offspring);

            // 评估适应度
            ga.evaluateFitness(offspring, vehicle);

            // 更新种群
            population = ga.updatePopulation(population, offspring);

            // 检查收敛条件
            if (ga.hasConverged(population)) {
                break;
            }
        }

        // 返回最优解
        Route bestRoute = ga.getBestRoute(population);
        return convertToDeliveryRoute(bestRoute, vehicle);
    }

    private double calculateRouteFitness(Route route, Vehicle vehicle) {
        double totalDistance = 0;
        double totalTime = 0;
        double penaltyScore = 0;

        Location currentLocation = vehicle.getStartLocation();

        for (DeliveryTask task : route.getTasks()) {
            // 计算距离和时间
            double distance = geoService.calculateDistance(currentLocation, task.getLocation());
            double travelTime = distance / vehicle.getAverageSpeed();

            totalDistance += distance;
            totalTime += travelTime;

            // 检查时间窗口约束
            if (totalTime < task.getEarliestTime()) {
                totalTime = task.getEarliestTime(); // 等待
            } else if (totalTime > task.getLatestTime()) {
                penaltyScore += (totalTime - task.getLatestTime()) * 10; // 延迟惩罚
            }

            // 服务时间
            totalTime += task.getServiceTime();
            currentLocation = task.getLocation();
        }

        // 适应度 = 1 / (总距离 + 时间惩罚 + 约束惩罚)
        return 1.0 / (totalDistance + totalTime * 0.1 + penaltyScore);
    }
}
```

## 4. 数据同步机制

### 4.1 事件驱动同步

```java
@Component
public class InventorySyncEventHandler {
    
    @EventListener
    @Async
    public void handleInventoryChanged(InventoryChangedEvent event) {
        try {
            // 1. 验证事件数据
            validateEvent(event);
            
            // 2. 更新中心库存
            updateCentralInventory(event);
            
            // 3. 更新缓存
            updateInventoryCache(event);
            
            // 4. 触发相关业务逻辑
            triggerBusinessLogic(event);
            
            // 5. 发送同步确认
            sendSyncConfirmation(event);
            
        } catch (Exception e) {
            // 6. 异常处理和重试
            handleSyncError(event, e);
        }
    }
    
    private void handleSyncError(InventoryChangedEvent event, Exception e) {
        // 记录错误日志
        log.error("Inventory sync failed for event: {}", event, e);
        
        // 发送到死信队列
        deadLetterQueueService.send(event);
        
        // 触发告警
        alertService.sendAlert("Inventory sync failed", e.getMessage());
    }
}
```

### 4.2 冲突检测和处理

```java
@Service
public class ConflictResolutionService {
    
    public void resolveInventoryConflict(InventoryConflict conflict) {
        switch (conflict.getType()) {
            case VERSION_CONFLICT:
                resolveVersionConflict(conflict);
                break;
                
            case QUANTITY_MISMATCH:
                resolveQuantityMismatch(conflict);
                break;
                
            case TIMESTAMP_CONFLICT:
                resolveTimestampConflict(conflict);
                break;
                
            default:
                throw new IllegalArgumentException("Unknown conflict type: " + conflict.getType());
        }
    }
    
    private void resolveVersionConflict(InventoryConflict conflict) {
        // 1. 获取冲突的库存记录
        FrontWarehouseInventory localRecord = conflict.getLocalRecord();
        FrontWarehouseInventory remoteRecord = conflict.getRemoteRecord();
        
        // 2. 比较版本号
        if (localRecord.getVersion() > remoteRecord.getVersion()) {
            // 本地版本更新，使用本地数据
            syncToRemote(localRecord);
        } else if (localRecord.getVersion() < remoteRecord.getVersion()) {
            // 远程版本更新，使用远程数据
            syncToLocal(remoteRecord);
        } else {
            // 版本相同但数据不同，使用时间戳判断
            resolveByTimestamp(localRecord, remoteRecord);
        }
    }
}
```

## 5. 接口设计

### 5.1 前置仓管理接口

```java
@RestController
@RequestMapping("/api/v1/front-warehouses")
public class FrontWarehouseController {

    @PostMapping
    public ResponseEntity<FrontWarehouse> createFrontWarehouse(@RequestBody CreateFrontWarehouseRequest request) {
        FrontWarehouse warehouse = frontWarehouseService.create(request);
        return ResponseEntity.ok(warehouse);
    }

    @GetMapping("/{id}")
    public ResponseEntity<FrontWarehouse> getFrontWarehouse(@PathVariable Long id) {
        FrontWarehouse warehouse = frontWarehouseService.findById(id);
        return ResponseEntity.ok(warehouse);
    }

    @GetMapping
    public ResponseEntity<Page<FrontWarehouse>> listFrontWarehouses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<FrontWarehouse> warehouses = frontWarehouseService.findAll(status, pageable);
        return ResponseEntity.ok(warehouses);
    }

    @PutMapping("/{id}")
    public ResponseEntity<FrontWarehouse> updateFrontWarehouse(
            @PathVariable Long id,
            @RequestBody UpdateFrontWarehouseRequest request) {
        FrontWarehouse warehouse = frontWarehouseService.update(id, request);
        return ResponseEntity.ok(warehouse);
    }

    @GetMapping("/{id}/coverage")
    public ResponseEntity<List<FrontWarehouseCoverage>> getCoverage(@PathVariable Long id) {
        List<FrontWarehouseCoverage> coverage = frontWarehouseService.getCoverage(id);
        return ResponseEntity.ok(coverage);
    }
}
```

### 5.2 库存分配接口

```java
@RestController
@RequestMapping("/api/v1/inventory-allocation")
public class InventoryAllocationController {

    @PostMapping("/calculate")
    public ResponseEntity<AllocationPlan> calculateAllocation(@RequestBody AllocationRequest request) {
        AllocationPlan plan = allocationService.calculateAllocation(request);
        return ResponseEntity.ok(plan);
    }

    @PostMapping("/execute")
    public ResponseEntity<AllocationRecord> executeAllocation(@RequestBody ExecuteAllocationRequest request) {
        AllocationRecord record = allocationService.executeAllocation(request);
        return ResponseEntity.ok(record);
    }

    @GetMapping("/records")
    public ResponseEntity<Page<AllocationRecord>> getAllocationRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<AllocationRecord> records = allocationService.findAllocationRecords(status, pageable);
        return ResponseEntity.ok(records);
    }
}
```

### 5.3 智能补货接口

```java
@RestController
@RequestMapping("/api/v1/replenishment")
public class ReplenishmentController {

    @PostMapping("/orders")
    public ResponseEntity<ReplenishmentOrder> createReplenishmentOrder(@RequestBody CreateReplenishmentOrderRequest request) {
        ReplenishmentOrder order = replenishmentService.createOrder(request);
        return ResponseEntity.ok(order);
    }

    @GetMapping("/suggestions")
    public ResponseEntity<List<ReplenishmentSuggestion>> getReplenishmentSuggestions(
            @RequestParam Long frontWarehouseId,
            @RequestParam(required = false) Long productId) {

        List<ReplenishmentSuggestion> suggestions = replenishmentService.getSuggestions(frontWarehouseId, productId);
        return ResponseEntity.ok(suggestions);
    }

    @PostMapping("/auto-replenish")
    public ResponseEntity<List<ReplenishmentOrder>> autoReplenish(@RequestBody AutoReplenishRequest request) {
        List<ReplenishmentOrder> orders = replenishmentService.autoReplenish(request);
        return ResponseEntity.ok(orders);
    }
}
```

### 5.4 订单履约接口

```java
@RestController
@RequestMapping("/api/v1/order-fulfillment")
public class OrderFulfillmentController {

    @PostMapping("/assign")
    public ResponseEntity<OrderAssignment> assignOrder(@RequestBody AssignOrderRequest request) {
        OrderAssignment assignment = orderFulfillmentService.assignOrder(request);
        return ResponseEntity.ok(assignment);
    }

    @GetMapping("/assignments")
    public ResponseEntity<Page<OrderAssignment>> getOrderAssignments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long frontWarehouseId,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<OrderAssignment> assignments = orderFulfillmentService.getAssignments(frontWarehouseId, status, pageable);
        return ResponseEntity.ok(assignments);
    }

    @PutMapping("/assignments/{id}/confirm")
    public ResponseEntity<OrderAssignment> confirmAssignment(@PathVariable Long id) {
        OrderAssignment assignment = orderFulfillmentService.confirmAssignment(id);
        return ResponseEntity.ok(assignment);
    }
}
```

### 5.5 拣选管理接口

```java
@RestController
@RequestMapping("/api/v1/picking")
public class PickingController {

    @PostMapping("/tasks")
    public ResponseEntity<PickingTask> createPickingTask(@RequestBody CreatePickingTaskRequest request) {
        PickingTask task = pickingService.createTask(request);
        return ResponseEntity.ok(task);
    }

    @GetMapping("/tasks")
    public ResponseEntity<Page<PickingTask>> getPickingTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long frontWarehouseId,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<PickingTask> tasks = pickingService.getTasks(frontWarehouseId, status, pageable);
        return ResponseEntity.ok(tasks);
    }

    @PutMapping("/tasks/{id}/start")
    public ResponseEntity<PickingTask> startPickingTask(@PathVariable Long id, @RequestParam Long pickerId) {
        PickingTask task = pickingService.startTask(id, pickerId);
        return ResponseEntity.ok(task);
    }

    @PutMapping("/tasks/{id}/items/{itemId}/pick")
    public ResponseEntity<PickingTaskItem> pickItem(
            @PathVariable Long id,
            @PathVariable Long itemId,
            @RequestBody PickItemRequest request) {
        PickingTaskItem item = pickingService.pickItem(id, itemId, request);
        return ResponseEntity.ok(item);
    }

    @PutMapping("/tasks/{id}/complete")
    public ResponseEntity<PickingTask> completePickingTask(@PathVariable Long id) {
        PickingTask task = pickingService.completeTask(id);
        return ResponseEntity.ok(task);
    }

    @GetMapping("/tasks/{id}/path")
    public ResponseEntity<List<PickingLocation>> getOptimizedPath(@PathVariable Long id) {
        List<PickingLocation> path = pickingService.getOptimizedPath(id);
        return ResponseEntity.ok(path);
    }
}
```

### 5.6 打包发货接口

```java
@RestController
@RequestMapping("/api/v1/packing")
public class PackingController {

    @PostMapping("/tasks")
    public ResponseEntity<PackingTask> createPackingTask(@RequestBody CreatePackingTaskRequest request) {
        PackingTask task = packingService.createTask(request);
        return ResponseEntity.ok(task);
    }

    @GetMapping("/tasks")
    public ResponseEntity<Page<PackingTask>> getPackingTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long frontWarehouseId,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<PackingTask> tasks = packingService.getTasks(frontWarehouseId, status, pageable);
        return ResponseEntity.ok(tasks);
    }

    @PutMapping("/tasks/{id}/start")
    public ResponseEntity<PackingTask> startPackingTask(@PathVariable Long id, @RequestParam Long packerId) {
        PackingTask task = packingService.startTask(id, packerId);
        return ResponseEntity.ok(task);
    }

    @PostMapping("/tasks/{id}/packages")
    public ResponseEntity<Package> createPackage(@PathVariable Long id, @RequestBody CreatePackageRequest request) {
        Package pkg = packingService.createPackage(id, request);
        return ResponseEntity.ok(pkg);
    }

    @PutMapping("/tasks/{id}/complete")
    public ResponseEntity<PackingTask> completePackingTask(@PathVariable Long id) {
        PackingTask task = packingService.completeTask(id);
        return ResponseEntity.ok(task);
    }

    @GetMapping("/packaging-suggestions")
    public ResponseEntity<PackagingSuggestion> getPackagingSuggestion(@RequestParam Long pickingTaskId) {
        PackagingSuggestion suggestion = packingService.getPackagingSuggestion(pickingTaskId);
        return ResponseEntity.ok(suggestion);
    }
}
```

### 5.7 配送调度接口

```java
@RestController
@RequestMapping("/api/v1/delivery")
public class DeliveryController {

    @PostMapping("/tasks")
    public ResponseEntity<DeliveryTask> createDeliveryTask(@RequestBody CreateDeliveryTaskRequest request) {
        DeliveryTask task = deliveryService.createTask(request);
        return ResponseEntity.ok(task);
    }

    @GetMapping("/tasks")
    public ResponseEntity<Page<DeliveryTask>> getDeliveryTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long frontWarehouseId,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<DeliveryTask> tasks = deliveryService.getTasks(frontWarehouseId, status, pageable);
        return ResponseEntity.ok(tasks);
    }

    @PutMapping("/tasks/{id}/assign")
    public ResponseEntity<DeliveryTask> assignDeliveryTask(
            @PathVariable Long id,
            @RequestParam Long driverId,
            @RequestParam(required = false) Long vehicleId) {
        DeliveryTask task = deliveryService.assignTask(id, driverId, vehicleId);
        return ResponseEntity.ok(task);
    }

    @PutMapping("/tasks/{id}/start")
    public ResponseEntity<DeliveryTask> startDeliveryTask(@PathVariable Long id) {
        DeliveryTask task = deliveryService.startTask(id);
        return ResponseEntity.ok(task);
    }

    @PutMapping("/tasks/{id}/packages/{packageId}/deliver")
    public ResponseEntity<DeliveryTaskPackage> deliverPackage(
            @PathVariable Long id,
            @PathVariable Long packageId,
            @RequestBody DeliverPackageRequest request) {
        DeliveryTaskPackage delivery = deliveryService.deliverPackage(id, packageId, request);
        return ResponseEntity.ok(delivery);
    }

    @GetMapping("/tasks/{id}/route")
    public ResponseEntity<DeliveryRoute> getOptimizedRoute(@PathVariable Long id) {
        DeliveryRoute route = deliveryService.getOptimizedRoute(id);
        return ResponseEntity.ok(route);
    }

    @GetMapping("/tasks/{id}/tracking")
    public ResponseEntity<DeliveryTracking> getDeliveryTracking(@PathVariable Long id) {
        DeliveryTracking tracking = deliveryService.getTracking(id);
        return ResponseEntity.ok(tracking);
    }
}
```

## 6. 性能优化

### 6.1 缓存策略

```java
@Service
public class FrontWarehouseInventoryService {

    @Cacheable(value = "front-warehouse-inventory", key = "#frontWarehouseId + ':' + #productId")
    public FrontWarehouseInventory getInventory(Long frontWarehouseId, Long productId) {
        return inventoryRepository.findByFrontWarehouseIdAndProductId(frontWarehouseId, productId);
    }

    @CacheEvict(value = "front-warehouse-inventory", key = "#inventory.frontWarehouseId + ':' + #inventory.productId")
    public void updateInventory(FrontWarehouseInventory inventory) {
        inventoryRepository.save(inventory);

        // 发送库存变更事件
        eventPublisher.publishEvent(new InventoryChangedEvent(inventory));
    }

    @Cacheable(value = "warehouse-coverage", key = "#frontWarehouseId")
    public List<FrontWarehouseCoverage> getCoverage(Long frontWarehouseId) {
        return coverageRepository.findByFrontWarehouseIdAndIsActiveTrue(frontWarehouseId);
    }
}
```

### 6.2 数据库优化

```sql
-- 前置仓库存表分区策略
CREATE TABLE front_warehouse_inventory_2025_01 PARTITION OF front_warehouse_inventory
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE front_warehouse_inventory_2025_02 PARTITION OF front_warehouse_inventory
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 库存分配记录表分区
CREATE TABLE inventory_allocation_records_2025_01 PARTITION OF inventory_allocation_records
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 补货订单表分区
CREATE TABLE replenishment_orders_2025_01 PARTITION OF replenishment_orders
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 6.3 异步处理

```java
@Service
public class AsyncInventoryService {

    @Async("inventoryTaskExecutor")
    public CompletableFuture<Void> processInventoryAllocation(AllocationRequest request) {
        try {
            // 执行库存分配
            AllocationPlan plan = allocationService.calculateAllocation(request);
            allocationService.executeAllocation(plan);

            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("Async inventory allocation failed", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @Async("replenishmentTaskExecutor")
    public CompletableFuture<List<ReplenishmentOrder>> processAutoReplenishment(Long frontWarehouseId) {
        try {
            List<ReplenishmentOrder> orders = replenishmentService.autoReplenish(frontWarehouseId);
            return CompletableFuture.completedFuture(orders);
        } catch (Exception e) {
            log.error("Async auto replenishment failed", e);
            return CompletableFuture.failedFuture(e);
        }
    }
}
```

## 7. 监控和告警

### 7.1 关键指标监控

```java
@Component
public class FrontWarehouseMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter inventoryAllocationCounter;
    private final Timer replenishmentProcessingTime;
    private final Gauge lowStockWarehouseCount;

    public FrontWarehouseMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.inventoryAllocationCounter = Counter.builder("inventory.allocation.count")
            .description("Number of inventory allocations")
            .register(meterRegistry);

        this.replenishmentProcessingTime = Timer.builder("replenishment.processing.time")
            .description("Time taken to process replenishment")
            .register(meterRegistry);

        this.lowStockWarehouseCount = Gauge.builder("warehouse.low.stock.count")
            .description("Number of warehouses with low stock")
            .register(meterRegistry, this, FrontWarehouseMetrics::getLowStockWarehouseCount);
    }

    public void recordInventoryAllocation() {
        inventoryAllocationCounter.increment();
    }

    public void recordReplenishmentProcessingTime(Duration duration) {
        replenishmentProcessingTime.record(duration);
    }

    private double getLowStockWarehouseCount() {
        return frontWarehouseService.countLowStockWarehouses();
    }
}
```

### 7.2 告警规则

```yaml
# Prometheus告警规则
groups:
  - name: front_warehouse_alerts
    rules:
      - alert: HighInventoryAllocationFailureRate
        expr: rate(inventory_allocation_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "库存分配失败率过高"
          description: "过去5分钟内库存分配失败率超过10%"

      - alert: ReplenishmentProcessingDelay
        expr: histogram_quantile(0.95, rate(replenishment_processing_time_bucket[5m])) > 30
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "补货处理延迟过高"
          description: "95%的补货处理时间超过30秒"

      - alert: LowStockWarehouseCount
        expr: warehouse_low_stock_count > 10
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "低库存前置仓数量过多"
          description: "当前有{{ $value }}个前置仓库存不足"
```

## 8. API接口模块设计

### 8.1 API模块概述

**模块名称：** pisp-api-front-warehouse

**技术栈：** Spring Boot 3.4.7 + OpenAPI 3.0 + Bean Validation

**职责：** 定义前置仓管理相关的完整API接口，包括前置仓管理、库存分配、智能补货、订单履约、拣选打包、配送调度等功能的接口定义

**核心功能：**
- 前置仓信息管理API
- 前置仓库存管理API
- 库存分配策略API
- 智能补货API
- 覆盖区域管理API
- 订单履约管理API
- 拣选任务管理API
- 打包任务管理API
- 配送任务管理API

### 8.2 Maven模块结构

```xml
<!-- pisp-api-front-warehouse/pom.xml -->
<project>
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pisp</groupId>
        <artifactId>pisp-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>pisp-api-front-warehouse</artifactId>
    <name>PISP :: API :: Front Warehouse</name>
    <description>前置仓管理API接口定义</description>

    <dependencies>
        <dependency>
            <groupId>com.pisp</groupId>
            <artifactId>pisp-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>
</project>
```

### 8.3 核心API接口定义

#### 8.3.1 前置仓管理API

```java
@RestController
@RequestMapping("/api/v1/front-warehouses")
@Tag(name = "前置仓管理", description = "前置仓信息管理相关接口")
@Validated
public interface FrontWarehouseController {

    @Operation(summary = "创建前置仓", description = "创建新的前置仓")
    @PostMapping
    ResponseEntity<ApiResponse<FrontWarehouseVO>> createFrontWarehouse(
            @Valid @RequestBody CreateFrontWarehouseRequest request);

    @Operation(summary = "更新前置仓信息", description = "更新前置仓基本信息")
    @PutMapping("/{id}")
    ResponseEntity<ApiResponse<FrontWarehouseVO>> updateFrontWarehouse(
            @PathVariable Long id,
            @Valid @RequestBody UpdateFrontWarehouseRequest request);

    @Operation(summary = "查询前置仓详情", description = "根据ID查询前置仓详细信息")
    @GetMapping("/{id}")
    ResponseEntity<ApiResponse<FrontWarehouseVO>> getFrontWarehouse(
            @PathVariable Long id);

    @Operation(summary = "分页查询前置仓", description = "分页查询前置仓列表")
    @GetMapping
    ResponseEntity<ApiResponse<PageResult<FrontWarehouseVO>>> getFrontWarehouses(
            @Valid FrontWarehouseQueryRequest request);

    @Operation(summary = "删除前置仓", description = "删除前置仓（需要二次确认）")
    @DeleteMapping("/{id}")
    ResponseEntity<ApiResponse<Void>> deleteFrontWarehouse(
            @PathVariable Long id,
            @RequestParam String confirmToken);

    @Operation(summary = "启用/禁用前置仓", description = "启用或禁用前置仓")
    @PutMapping("/{id}/status")
    ResponseEntity<ApiResponse<Void>> updateFrontWarehouseStatus(
            @PathVariable Long id,
            @Valid @RequestBody UpdateStatusRequest request);
}
```

#### 8.3.2 前置仓库存管理API

```java
@RestController
@RequestMapping("/api/v1/front-warehouses/{warehouseId}/inventory")
@Tag(name = "前置仓库存管理", description = "前置仓库存管理相关接口")
@Validated
public interface FrontWarehouseInventoryController {

    @Operation(summary = "查询前置仓库存", description = "查询指定前置仓的库存信息")
    @GetMapping
    ResponseEntity<ApiResponse<PageResult<InventoryVO>>> getInventory(
            @PathVariable Long warehouseId,
            @Valid InventoryQueryRequest request);

    @Operation(summary = "调整库存", description = "手动调整前置仓库存")
    @PostMapping("/adjust")
    ResponseEntity<ApiResponse<Void>> adjustInventory(
            @PathVariable Long warehouseId,
            @Valid @RequestBody InventoryAdjustRequest request);

    @Operation(summary = "库存分配", description = "执行库存分配策略")
    @PostMapping("/allocate")
    ResponseEntity<ApiResponse<AllocationResultVO>> allocateInventory(
            @PathVariable Long warehouseId,
            @Valid @RequestBody AllocationRequest request);

    @Operation(summary = "库存预留", description = "为订单预留库存")
    @PostMapping("/reserve")
    ResponseEntity<ApiResponse<ReservationVO>> reserveInventory(
            @PathVariable Long warehouseId,
            @Valid @RequestBody ReservationRequest request);

    @Operation(summary = "释放预留库存", description = "释放已预留的库存")
    @PostMapping("/release")
    ResponseEntity<ApiResponse<Void>> releaseReservation(
            @PathVariable Long warehouseId,
            @Valid @RequestBody ReleaseReservationRequest request);
}
```

#### 8.3.3 智能补货管理API

```java
@RestController
@RequestMapping("/api/v1/front-warehouses/{warehouseId}/replenishment")
@Tag(name = "智能补货管理", description = "前置仓智能补货相关接口")
@Validated
public interface ReplenishmentController {

    @Operation(summary = "创建补货订单", description = "创建前置仓补货订单")
    @PostMapping("/orders")
    ResponseEntity<ApiResponse<ReplenishmentOrderVO>> createReplenishmentOrder(
            @PathVariable Long warehouseId,
            @Valid @RequestBody CreateReplenishmentOrderRequest request);

    @Operation(summary = "智能补货建议", description = "基于算法生成补货建议")
    @PostMapping("/suggestions")
    ResponseEntity<ApiResponse<List<ReplenishmentSuggestionVO>>> getReplenishmentSuggestions(
            @PathVariable Long warehouseId,
            @Valid @RequestBody ReplenishmentSuggestionRequest request);

    @Operation(summary = "补货策略管理", description = "管理前置仓补货策略")
    @GetMapping("/strategies")
    ResponseEntity<ApiResponse<List<ReplenishmentStrategyVO>>> getReplenishmentStrategies(
            @PathVariable Long warehouseId);

    @Operation(summary = "更新补货策略", description = "更新前置仓补货策略")
    @PutMapping("/strategies/{strategyId}")
    ResponseEntity<ApiResponse<ReplenishmentStrategyVO>> updateReplenishmentStrategy(
            @PathVariable Long warehouseId,
            @PathVariable Long strategyId,
            @Valid @RequestBody UpdateReplenishmentStrategyRequest request);

    @Operation(summary = "补货订单查询", description = "查询补货订单列表")
    @GetMapping("/orders")
    ResponseEntity<ApiResponse<PageResult<ReplenishmentOrderVO>>> getReplenishmentOrders(
            @PathVariable Long warehouseId,
            @Valid ReplenishmentOrderQueryRequest request);
}
```

#### 8.3.4 订单履约管理API

```java
@RestController
@RequestMapping("/api/v1/front-warehouses/{warehouseId}/fulfillment")
@Tag(name = "订单履约管理", description = "订单履约相关接口")
@Validated
public interface OrderFulfillmentController {

    @Operation(summary = "订单分配", description = "将订单分配到前置仓")
    @PostMapping("/allocate")
    ResponseEntity<ApiResponse<OrderAllocationVO>> allocateOrder(
            @PathVariable Long warehouseId,
            @Valid @RequestBody OrderAllocationRequest request);

    @Operation(summary = "创建拣选任务", description = "为订单创建拣选任务")
    @PostMapping("/picking-tasks")
    ResponseEntity<ApiResponse<PickingTaskVO>> createPickingTask(
            @PathVariable Long warehouseId,
            @Valid @RequestBody CreatePickingTaskRequest request);

    @Operation(summary = "更新拣选状态", description = "更新拣选任务状态")
    @PutMapping("/picking-tasks/{taskId}/status")
    ResponseEntity<ApiResponse<Void>> updatePickingStatus(
            @PathVariable Long warehouseId,
            @PathVariable Long taskId,
            @Valid @RequestBody UpdatePickingStatusRequest request);

    @Operation(summary = "创建打包任务", description = "为拣选完成的订单创建打包任务")
    @PostMapping("/packing-tasks")
    ResponseEntity<ApiResponse<PackingTaskVO>> createPackingTask(
            @PathVariable Long warehouseId,
            @Valid @RequestBody CreatePackingTaskRequest request);

    @Operation(summary = "创建配送任务", description = "为打包完成的订单创建配送任务")
    @PostMapping("/delivery-tasks")
    ResponseEntity<ApiResponse<DeliveryTaskVO>> createDeliveryTask(
            @PathVariable Long warehouseId,
            @Valid @RequestBody CreateDeliveryTaskRequest request);

    @Operation(summary = "查询履约状态", description = "查询订单履约状态")
    @GetMapping("/orders/{orderId}/status")
    ResponseEntity<ApiResponse<FulfillmentStatusVO>> getFulfillmentStatus(
            @PathVariable Long warehouseId,
            @PathVariable Long orderId);
}
```

### 8.4 数据传输对象(DTO)设计

#### 8.4.1 前置仓管理DTO

**前置仓信息VO：**
```java
@Data
@Schema(description = "前置仓信息视图对象")
public class FrontWarehouseVO {

    @Schema(description = "前置仓ID")
    private Long id;

    @Schema(description = "前置仓编码")
    private String warehouseCode;

    @Schema(description = "前置仓名称")
    private String warehouseName;

    @Schema(description = "所属主仓库ID")
    private Long parentWarehouseId;

    @Schema(description = "所属主仓库名称")
    private String parentWarehouseName;

    @Schema(description = "地址信息")
    private String address;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "服务半径(公里)")
    private BigDecimal serviceRadius;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "营业时间")
    private String operatingHours;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
```

**创建前置仓请求DTO：**
```java
@Data
@Schema(description = "创建前置仓请求")
public class CreateFrontWarehouseRequest {

    @NotBlank(message = "前置仓编码不能为空")
    @Schema(description = "前置仓编码")
    private String warehouseCode;

    @NotBlank(message = "前置仓名称不能为空")
    @Schema(description = "前置仓名称")
    private String warehouseName;

    @NotNull(message = "所属主仓库ID不能为空")
    @Schema(description = "所属主仓库ID")
    private Long parentWarehouseId;

    @NotBlank(message = "地址信息不能为空")
    @Schema(description = "地址信息")
    private String address;

    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度范围：-180.0 ~ 180.0")
    @DecimalMax(value = "180.0", message = "经度范围：-180.0 ~ 180.0")
    @Schema(description = "经度")
    private BigDecimal longitude;

    @NotNull(message = "纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度范围：-90.0 ~ 90.0")
    @DecimalMax(value = "90.0", message = "纬度范围：-90.0 ~ 90.0")
    @Schema(description = "纬度")
    private BigDecimal latitude;

    @NotNull(message = "服务半径不能为空")
    @DecimalMin(value = "0.1", message = "服务半径必须大于0.1公里")
    @DecimalMax(value = "50.0", message = "服务半径不能超过50公里")
    @Schema(description = "服务半径(公里)")
    private BigDecimal serviceRadius;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "营业时间")
    private String operatingHours;
}
```

### 8.5 网关路由配置

#### 8.5.1 ShenYu网关路由规则

**前置仓管理服务路由：**
```yaml
# ShenYu Admin配置
shenyu:
  routes:
    - id: pisp-front-warehouse-route
      uri: lb://pisp-front-warehouse-service
      predicates:
        - Path=/api/v1/front-warehouses/**
      filters:
        - name: StripPrefix
          args:
            parts: 0
        - name: RequestRateLimiter
          args:
            redis-rate-limiter.replenishRate: 100
            redis-rate-limiter.burstCapacity: 200
        - name: CircuitBreaker
          args:
            name: front-warehouse-cb
            fallbackUri: forward:/fallback/front-warehouse
```

#### 8.5.2 权限控制配置

**JWT认证配置：**
```yaml
shenyu:
  plugin:
    jwt:
      enabled: true
      secretKey: "pisp-front-warehouse-secret-key-2025"
      expiredTime: 86400000
      # 前置仓管理需要WAREHOUSE_MANAGER权限
      rules:
        - path: /api/v1/front-warehouses/**
          requiredRoles: ["WAREHOUSE_MANAGER", "ADMIN"]
        - path: /api/v1/front-warehouses/*/inventory/**
          requiredRoles: ["WAREHOUSE_MANAGER", "INVENTORY_MANAGER", "ADMIN"]
        - path: /api/v1/front-warehouses/*/replenishment/**
          requiredRoles: ["WAREHOUSE_MANAGER", "REPLENISHMENT_MANAGER", "ADMIN"]
        - path: /api/v1/front-warehouses/*/fulfillment/**
          requiredRoles: ["WAREHOUSE_MANAGER", "FULFILLMENT_MANAGER", "ADMIN"]
```

### 8.6 高危操作二次确认机制

#### 8.6.1 确认令牌机制

**前置仓删除接口：**
```java
@HighRiskOperation(
    operationType = "DELETE_FRONT_WAREHOUSE",
    resourceType = "FRONT_WAREHOUSE",
    confirmTokenParam = "confirmToken",
    description = "删除前置仓"
)
@Operation(summary = "删除前置仓", description = "删除前置仓（需要二次确认）")
public ResponseEntity<ApiResponse<Void>> deleteFrontWarehouse(
        @PathVariable Long id,
        @RequestParam String confirmToken) {
    // 实现逻辑
}

@PostMapping("/{id}/delete-token")
@Operation(summary = "获取删除确认令牌", description = "获取删除前置仓的确认令牌")
public ResponseEntity<ApiResponse<String>> getDeleteConfirmToken(@PathVariable Long id) {
    // 生成确认令牌
}
```

#### 8.6.2 高危操作列表

| 操作类型 | 操作描述 | 确认级别 | 令牌有效期 |
|---------|---------|---------|-----------|
| DELETE_FRONT_WAREHOUSE | 删除前置仓 | 二次确认 | 5分钟 |
| BATCH_DELETE_INVENTORY | 批量删除库存 | 二次确认 | 5分钟 |
| CLEAR_ALL_RESERVATIONS | 清空所有预留 | 二次确认 | 3分钟 |
| FORCE_ALLOCATION | 强制库存分配 | 二次确认 | 5分钟 |
| EMERGENCY_REPLENISHMENT | 紧急补货 | 二次确认 | 10分钟 |

## 9. 部署和配置

### 9.1 配置文件

```yaml
# application-front-warehouse.yml
front-warehouse:
  allocation:
    batch-size: 1000
    max-retry-attempts: 3
    retry-delay: 5000

  replenishment:
    check-interval: 300000  # 5分钟检查一次
    auto-approve-threshold: 1000
    emergency-threshold: 0.1

  sync:
    batch-size: 500
    max-queue-size: 10000
    consumer-threads: 10

  cache:
    inventory-ttl: 300  # 5分钟
    coverage-ttl: 3600  # 1小时

  monitoring:
    metrics-enabled: true
    alert-enabled: true
    health-check-interval: 60000
```

### 9.2 Docker部署

```dockerfile
FROM openjdk:17-jre-slim

COPY target/front-warehouse-service.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  front-warehouse-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DATABASE_URL=************************************
    depends_on:
      - postgres
      - redis
      - kafka
```

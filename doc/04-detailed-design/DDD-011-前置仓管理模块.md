# DDD-011 前置仓管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-011 |
| 文档名称 | 前置仓管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-07-02 |
| 最后修改 | 2025-07-02 |
| 文档状态 | 草稿 |
| 作者 | 系统架构师 |

## 1. 模块概述

前置仓管理模块负责管理分布式前置仓的库存业务，包括前置仓管理、库存分配、智能补货和库存同步等功能。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "前置仓管理模块"
        A[前置仓管理服务]
        B[库存分配服务]
        C[智能补货服务]
        D[库存同步服务]
        E[预测分析服务]
    end
    
    subgraph "核心功能"
        F[前置仓基础管理]
        G[智能库存分配]
        H[自动补货策略]
        I[实时库存同步]
        J[需求预测分析]
    end
    
    A --> F
    B --> G
    C --> H
    D --> I
    E --> J
```

## 2. 核心业务流程

### 2.1 库存分配流程

```mermaid
flowchart TD
    A[触发分配] --> B[收集数据]
    B --> C[需求预测]
    C --> D[计算分配方案]
    D --> E[方案优化]
    E --> F[生成分配单]
    F --> G[审核确认]
    G --> H{审核通过?}
    H -->|是| I[执行分配]
    H -->|否| J[调整方案]
    J --> F
    I --> K[更新库存]
    K --> L[分配完成]
    
    subgraph "数据收集"
        B1[历史销售数据]
        B2[当前库存数据]
        B3[季节性因素]
        B4[促销计划]
    end
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
```

**流程说明：**
1. **触发分配**：定时任务或手动触发库存分配
2. **收集数据**：收集历史销售、当前库存、季节性因素等数据
3. **需求预测**：基于机器学习算法预测各前置仓需求
4. **计算分配方案**：根据预测需求和库存约束计算最优分配
5. **方案优化**：考虑物流成本、库存周转等因素优化方案
6. **生成分配单**：生成详细的库存分配单据
7. **审核确认**：人工审核分配方案的合理性
8. **执行分配**：执行库存分配，更新各仓库库存

### 2.2 智能补货流程

```mermaid
flowchart TD
    A[库存监控] --> B{触发条件?}
    B -->|低于安全库存| C[计算补货需求]
    B -->|预测缺货| C
    B -->|定期补货| C
    B -->|否| A
    
    C --> D[选择补货策略]
    D --> E[计算补货数量]
    E --> F[选择补货来源]
    F --> G[生成补货订单]
    G --> H[发送补货请求]
    H --> I[跟踪补货状态]
    I --> J[确认收货]
    J --> K[更新库存]
    K --> L[补货完成]
    
    subgraph "补货策略"
        D1[定量补货]
        D2[定期补货]
        D3[预测补货]
        D4[紧急补货]
    end
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
```

**补货触发条件：**
- **库存预警**：当前库存低于安全库存
- **预测缺货**：基于销售预测可能出现缺货
- **定期补货**：按固定周期进行补货
- **紧急补货**：突发情况下的紧急补货需求

### 2.3 库存同步流程

```mermaid
sequenceDiagram
    participant FW as 前置仓
    participant MQ as 消息队列
    participant CS as 中心系统
    participant DB as 数据库
    participant Cache as 缓存

    Note over FW,Cache: 正常同步流程
    FW->>MQ: 库存变动事件
    MQ->>CS: 转发事件
    CS->>DB: 更新库存
    CS->>Cache: 更新缓存
    DB-->>CS: 确认更新
    CS->>MQ: 同步确认
    MQ->>FW: 同步完成

    Note over FW,Cache: 异常处理流程
    FW->>MQ: 库存变动事件
    MQ->>CS: 转发事件
    CS->>DB: 更新失败
    CS->>MQ: 同步失败
    MQ->>FW: 重试通知
    FW->>FW: 本地缓存
    
    Note over FW,Cache: 网络恢复后
    FW->>MQ: 批量同步事件
    MQ->>CS: 批量处理
    CS->>DB: 批量更新
    CS->>Cache: 批量更新缓存
```

**同步机制特点：**
- **实时同步**：库存变动立即同步到中心系统
- **离线支持**：网络中断时本地缓存变动
- **批量恢复**：网络恢复后批量同步数据
- **冲突处理**：自动检测和处理数据冲突

## 3. 核心算法设计

### 3.1 需求预测算法

```java
@Service
public class DemandForecastService {
    
    /**
     * 基于时间序列的需求预测
     */
    public ForecastResult forecastDemand(Long productId, Long frontWarehouseId, int forecastDays) {
        // 1. 获取历史销售数据
        List<SalesData> historicalData = getHistoricalSalesData(productId, frontWarehouseId);
        
        // 2. 数据预处理
        List<Double> processedData = preprocessData(historicalData);
        
        // 3. 应用ARIMA模型预测
        ARIMAModel model = new ARIMAModel();
        model.fit(processedData);
        
        // 4. 生成预测结果
        List<Double> forecast = model.forecast(forecastDays);
        
        // 5. 考虑季节性因素
        forecast = adjustForSeasonality(forecast, productId);
        
        // 6. 考虑促销影响
        forecast = adjustForPromotions(forecast, productId, frontWarehouseId);
        
        return new ForecastResult(forecast, model.getConfidenceInterval());
    }
    
    /**
     * 库存分配算法
     */
    public AllocationPlan calculateAllocation(List<FrontWarehouse> warehouses, 
                                            List<Product> products, 
                                            BigDecimal totalInventory) {
        
        AllocationPlan plan = new AllocationPlan();
        
        for (Product product : products) {
            // 1. 计算各前置仓需求预测
            Map<Long, BigDecimal> demandForecast = new HashMap<>();
            for (FrontWarehouse warehouse : warehouses) {
                ForecastResult forecast = forecastDemand(product.getId(), warehouse.getId(), 7);
                demandForecast.put(warehouse.getId(), forecast.getTotalDemand());
            }
            
            // 2. 计算总需求
            BigDecimal totalDemand = demandForecast.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 3. 按比例分配库存
            BigDecimal availableInventory = getAvailableInventory(product.getId());
            
            for (FrontWarehouse warehouse : warehouses) {
                BigDecimal demand = demandForecast.get(warehouse.getId());
                BigDecimal ratio = demand.divide(totalDemand, 4, RoundingMode.HALF_UP);
                BigDecimal allocation = availableInventory.multiply(ratio);
                
                // 4. 考虑最小最大库存约束
                allocation = applyStockConstraints(allocation, warehouse, product);
                
                plan.addAllocation(warehouse.getId(), product.getId(), allocation);
            }
        }
        
        return plan;
    }
}
```

### 3.2 补货策略算法

```java
@Service
public class ReplenishmentStrategyService {
    
    /**
     * 计算补货数量
     */
    public BigDecimal calculateReplenishmentQuantity(Long productId, 
                                                   Long frontWarehouseId,
                                                   ReplenishmentStrategy strategy) {
        
        FrontWarehouseInventory inventory = getInventory(productId, frontWarehouseId);
        
        switch (strategy.getType()) {
            case FIXED_QUANTITY:
                return calculateFixedQuantityReplenishment(inventory, strategy);
                
            case FIXED_PERIOD:
                return calculateFixedPeriodReplenishment(inventory, strategy);
                
            case PREDICTIVE:
                return calculatePredictiveReplenishment(inventory, strategy);
                
            case EMERGENCY:
                return calculateEmergencyReplenishment(inventory, strategy);
                
            default:
                throw new IllegalArgumentException("Unknown strategy type: " + strategy.getType());
        }
    }
    
    private BigDecimal calculatePredictiveReplenishment(FrontWarehouseInventory inventory, 
                                                      ReplenishmentStrategy strategy) {
        
        // 1. 预测未来需求
        ForecastResult forecast = demandForecastService.forecastDemand(
            inventory.getProductId(), 
            inventory.getFrontWarehouseId(), 
            strategy.getForecastDays()
        );
        
        // 2. 计算预期库存消耗
        BigDecimal expectedConsumption = forecast.getTotalDemand();
        
        // 3. 计算当前可用库存
        BigDecimal currentStock = inventory.getAvailableQuantity();
        
        // 4. 计算安全库存
        BigDecimal safetyStock = inventory.getMinStock();
        
        // 5. 计算补货数量
        BigDecimal replenishmentQuantity = expectedConsumption
            .add(safetyStock)
            .subtract(currentStock);
        
        // 6. 确保不超过最大库存
        BigDecimal maxStock = inventory.getMaxStock();
        if (currentStock.add(replenishmentQuantity).compareTo(maxStock) > 0) {
            replenishmentQuantity = maxStock.subtract(currentStock);
        }
        
        return replenishmentQuantity.max(BigDecimal.ZERO);
    }
}
```

## 4. 数据同步机制

### 4.1 事件驱动同步

```java
@Component
public class InventorySyncEventHandler {
    
    @EventListener
    @Async
    public void handleInventoryChanged(InventoryChangedEvent event) {
        try {
            // 1. 验证事件数据
            validateEvent(event);
            
            // 2. 更新中心库存
            updateCentralInventory(event);
            
            // 3. 更新缓存
            updateInventoryCache(event);
            
            // 4. 触发相关业务逻辑
            triggerBusinessLogic(event);
            
            // 5. 发送同步确认
            sendSyncConfirmation(event);
            
        } catch (Exception e) {
            // 6. 异常处理和重试
            handleSyncError(event, e);
        }
    }
    
    private void handleSyncError(InventoryChangedEvent event, Exception e) {
        // 记录错误日志
        log.error("Inventory sync failed for event: {}", event, e);
        
        // 发送到死信队列
        deadLetterQueueService.send(event);
        
        // 触发告警
        alertService.sendAlert("Inventory sync failed", e.getMessage());
    }
}
```

### 4.2 冲突检测和处理

```java
@Service
public class ConflictResolutionService {
    
    public void resolveInventoryConflict(InventoryConflict conflict) {
        switch (conflict.getType()) {
            case VERSION_CONFLICT:
                resolveVersionConflict(conflict);
                break;
                
            case QUANTITY_MISMATCH:
                resolveQuantityMismatch(conflict);
                break;
                
            case TIMESTAMP_CONFLICT:
                resolveTimestampConflict(conflict);
                break;
                
            default:
                throw new IllegalArgumentException("Unknown conflict type: " + conflict.getType());
        }
    }
    
    private void resolveVersionConflict(InventoryConflict conflict) {
        // 1. 获取冲突的库存记录
        FrontWarehouseInventory localRecord = conflict.getLocalRecord();
        FrontWarehouseInventory remoteRecord = conflict.getRemoteRecord();
        
        // 2. 比较版本号
        if (localRecord.getVersion() > remoteRecord.getVersion()) {
            // 本地版本更新，使用本地数据
            syncToRemote(localRecord);
        } else if (localRecord.getVersion() < remoteRecord.getVersion()) {
            // 远程版本更新，使用远程数据
            syncToLocal(remoteRecord);
        } else {
            // 版本相同但数据不同，使用时间戳判断
            resolveByTimestamp(localRecord, remoteRecord);
        }
    }
}
```

## 5. 接口设计

### 5.1 前置仓管理接口

```java
@RestController
@RequestMapping("/api/v1/front-warehouses")
public class FrontWarehouseController {

    @PostMapping
    public ResponseEntity<FrontWarehouse> createFrontWarehouse(@RequestBody CreateFrontWarehouseRequest request) {
        FrontWarehouse warehouse = frontWarehouseService.create(request);
        return ResponseEntity.ok(warehouse);
    }

    @GetMapping("/{id}")
    public ResponseEntity<FrontWarehouse> getFrontWarehouse(@PathVariable Long id) {
        FrontWarehouse warehouse = frontWarehouseService.findById(id);
        return ResponseEntity.ok(warehouse);
    }

    @GetMapping
    public ResponseEntity<Page<FrontWarehouse>> listFrontWarehouses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<FrontWarehouse> warehouses = frontWarehouseService.findAll(status, pageable);
        return ResponseEntity.ok(warehouses);
    }

    @PutMapping("/{id}")
    public ResponseEntity<FrontWarehouse> updateFrontWarehouse(
            @PathVariable Long id,
            @RequestBody UpdateFrontWarehouseRequest request) {
        FrontWarehouse warehouse = frontWarehouseService.update(id, request);
        return ResponseEntity.ok(warehouse);
    }

    @GetMapping("/{id}/coverage")
    public ResponseEntity<List<FrontWarehouseCoverage>> getCoverage(@PathVariable Long id) {
        List<FrontWarehouseCoverage> coverage = frontWarehouseService.getCoverage(id);
        return ResponseEntity.ok(coverage);
    }
}
```

### 5.2 库存分配接口

```java
@RestController
@RequestMapping("/api/v1/inventory-allocation")
public class InventoryAllocationController {

    @PostMapping("/calculate")
    public ResponseEntity<AllocationPlan> calculateAllocation(@RequestBody AllocationRequest request) {
        AllocationPlan plan = allocationService.calculateAllocation(request);
        return ResponseEntity.ok(plan);
    }

    @PostMapping("/execute")
    public ResponseEntity<AllocationRecord> executeAllocation(@RequestBody ExecuteAllocationRequest request) {
        AllocationRecord record = allocationService.executeAllocation(request);
        return ResponseEntity.ok(record);
    }

    @GetMapping("/records")
    public ResponseEntity<Page<AllocationRecord>> getAllocationRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status) {

        Pageable pageable = PageRequest.of(page, size);
        Page<AllocationRecord> records = allocationService.findAllocationRecords(status, pageable);
        return ResponseEntity.ok(records);
    }
}
```

### 5.3 智能补货接口

```java
@RestController
@RequestMapping("/api/v1/replenishment")
public class ReplenishmentController {

    @PostMapping("/orders")
    public ResponseEntity<ReplenishmentOrder> createReplenishmentOrder(@RequestBody CreateReplenishmentOrderRequest request) {
        ReplenishmentOrder order = replenishmentService.createOrder(request);
        return ResponseEntity.ok(order);
    }

    @GetMapping("/suggestions")
    public ResponseEntity<List<ReplenishmentSuggestion>> getReplenishmentSuggestions(
            @RequestParam Long frontWarehouseId,
            @RequestParam(required = false) Long productId) {

        List<ReplenishmentSuggestion> suggestions = replenishmentService.getSuggestions(frontWarehouseId, productId);
        return ResponseEntity.ok(suggestions);
    }

    @PostMapping("/auto-replenish")
    public ResponseEntity<List<ReplenishmentOrder>> autoReplenish(@RequestBody AutoReplenishRequest request) {
        List<ReplenishmentOrder> orders = replenishmentService.autoReplenish(request);
        return ResponseEntity.ok(orders);
    }
}
```

## 6. 性能优化

### 6.1 缓存策略

```java
@Service
public class FrontWarehouseInventoryService {

    @Cacheable(value = "front-warehouse-inventory", key = "#frontWarehouseId + ':' + #productId")
    public FrontWarehouseInventory getInventory(Long frontWarehouseId, Long productId) {
        return inventoryRepository.findByFrontWarehouseIdAndProductId(frontWarehouseId, productId);
    }

    @CacheEvict(value = "front-warehouse-inventory", key = "#inventory.frontWarehouseId + ':' + #inventory.productId")
    public void updateInventory(FrontWarehouseInventory inventory) {
        inventoryRepository.save(inventory);

        // 发送库存变更事件
        eventPublisher.publishEvent(new InventoryChangedEvent(inventory));
    }

    @Cacheable(value = "warehouse-coverage", key = "#frontWarehouseId")
    public List<FrontWarehouseCoverage> getCoverage(Long frontWarehouseId) {
        return coverageRepository.findByFrontWarehouseIdAndIsActiveTrue(frontWarehouseId);
    }
}
```

### 6.2 数据库优化

```sql
-- 前置仓库存表分区策略
CREATE TABLE front_warehouse_inventory_2025_01 PARTITION OF front_warehouse_inventory
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE front_warehouse_inventory_2025_02 PARTITION OF front_warehouse_inventory
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 库存分配记录表分区
CREATE TABLE inventory_allocation_records_2025_01 PARTITION OF inventory_allocation_records
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 补货订单表分区
CREATE TABLE replenishment_orders_2025_01 PARTITION OF replenishment_orders
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 6.3 异步处理

```java
@Service
public class AsyncInventoryService {

    @Async("inventoryTaskExecutor")
    public CompletableFuture<Void> processInventoryAllocation(AllocationRequest request) {
        try {
            // 执行库存分配
            AllocationPlan plan = allocationService.calculateAllocation(request);
            allocationService.executeAllocation(plan);

            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("Async inventory allocation failed", e);
            return CompletableFuture.failedFuture(e);
        }
    }

    @Async("replenishmentTaskExecutor")
    public CompletableFuture<List<ReplenishmentOrder>> processAutoReplenishment(Long frontWarehouseId) {
        try {
            List<ReplenishmentOrder> orders = replenishmentService.autoReplenish(frontWarehouseId);
            return CompletableFuture.completedFuture(orders);
        } catch (Exception e) {
            log.error("Async auto replenishment failed", e);
            return CompletableFuture.failedFuture(e);
        }
    }
}
```

## 7. 监控和告警

### 7.1 关键指标监控

```java
@Component
public class FrontWarehouseMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter inventoryAllocationCounter;
    private final Timer replenishmentProcessingTime;
    private final Gauge lowStockWarehouseCount;

    public FrontWarehouseMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.inventoryAllocationCounter = Counter.builder("inventory.allocation.count")
            .description("Number of inventory allocations")
            .register(meterRegistry);

        this.replenishmentProcessingTime = Timer.builder("replenishment.processing.time")
            .description("Time taken to process replenishment")
            .register(meterRegistry);

        this.lowStockWarehouseCount = Gauge.builder("warehouse.low.stock.count")
            .description("Number of warehouses with low stock")
            .register(meterRegistry, this, FrontWarehouseMetrics::getLowStockWarehouseCount);
    }

    public void recordInventoryAllocation() {
        inventoryAllocationCounter.increment();
    }

    public void recordReplenishmentProcessingTime(Duration duration) {
        replenishmentProcessingTime.record(duration);
    }

    private double getLowStockWarehouseCount() {
        return frontWarehouseService.countLowStockWarehouses();
    }
}
```

### 7.2 告警规则

```yaml
# Prometheus告警规则
groups:
  - name: front_warehouse_alerts
    rules:
      - alert: HighInventoryAllocationFailureRate
        expr: rate(inventory_allocation_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "库存分配失败率过高"
          description: "过去5分钟内库存分配失败率超过10%"

      - alert: ReplenishmentProcessingDelay
        expr: histogram_quantile(0.95, rate(replenishment_processing_time_bucket[5m])) > 30
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "补货处理延迟过高"
          description: "95%的补货处理时间超过30秒"

      - alert: LowStockWarehouseCount
        expr: warehouse_low_stock_count > 10
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "低库存前置仓数量过多"
          description: "当前有{{ $value }}个前置仓库存不足"
```

## 8. 部署和配置

### 8.1 配置文件

```yaml
# application-front-warehouse.yml
front-warehouse:
  allocation:
    batch-size: 1000
    max-retry-attempts: 3
    retry-delay: 5000

  replenishment:
    check-interval: 300000  # 5分钟检查一次
    auto-approve-threshold: 1000
    emergency-threshold: 0.1

  sync:
    batch-size: 500
    max-queue-size: 10000
    consumer-threads: 10

  cache:
    inventory-ttl: 300  # 5分钟
    coverage-ttl: 3600  # 1小时

  monitoring:
    metrics-enabled: true
    alert-enabled: true
    health-check-interval: 60000
```

### 8.2 Docker部署

```dockerfile
FROM openjdk:17-jre-slim

COPY target/front-warehouse-service.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  front-warehouse-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - DATABASE_URL=************************************
    depends_on:
      - postgres
      - redis
      - kafka
```

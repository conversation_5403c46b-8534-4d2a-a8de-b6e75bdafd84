# DDD-002 用户管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-002 |
| 文档名称 | 用户管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

### 1.1 模块职责

用户管理模块负责系统的用户认证、授权和组织架构管理，为整个ERP系统提供统一的身份认证和权限控制服务。

### 1.2 核心功能

- **用户注册登录**：用户注册、登录认证、密码管理、会话管理
- **角色权限管理**：角色定义、权限分配、访问控制、权限继承
- **组织架构管理**：部门管理、岗位管理、用户分配、层级关系
- **用户信息管理**：用户档案、个人设置、状态管理

### 1.3 模块架构图

```mermaid
graph TB
    subgraph "用户管理模块"
        A[用户注册登录]
        B[角色权限管理]
        C[组织架构管理]
        D[用户信息管理]
    end
    
    subgraph "核心实体"
        E[User用户]
        F[Role角色]
        G[Permission权限]
        H[Department部门]
    end
    
    A --> E
    B --> F
    B --> G
    C --> H
    D --> E
```

## 2. 实体设计

> **注意**：所有实体类都继承自BaseEntity基类，BaseEntity的定义请参考 [DDD-001-详细设计.md](./DDD-001-详细设计.md#52-基础实体类定义)

### 2.1 用户实体 (User)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_users")
public class User extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("username")
    private String username;

    @TableField("password")
    private String password;

    @TableField("email")
    private String email;

    @TableField("phone")
    private String phone;

    @TableField("real_name")
    private String realName;

    @TableField("avatar_url")
    private String avatarUrl;

    @TableField("status")
    @EnumValue
    private UserStatus status;

    @TableField("department_id")
    private Long departmentId;
    
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    @TableField("last_login_ip")
    private String lastLoginIp;

    @TableField("login_count")
    private Integer loginCount = 0;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void activate() {
        this.status = UserStatus.ACTIVE;
    }
    
    public void deactivate() {
        this.status = UserStatus.INACTIVE;
    }
    
    public void lock() {
        this.status = UserStatus.LOCKED;
    }
    
    public boolean isActive() {
        return UserStatus.ACTIVE.equals(this.status);
    }
    
    public void updateLoginInfo(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        this.loginCount++;
    }
    
    public Set<String> getPermissions() {
        return roles.stream()
            .flatMap(role -> role.getPermissions().stream())
            .map(Permission::getPermissionCode)
            .collect(Collectors.toSet());
    }
}

public enum UserStatus {
    ACTIVE("激活"),
    INACTIVE("未激活"),
    LOCKED("锁定"),
    DELETED("已删除");
    
    private final String description;
    
    UserStatus(String description) {
        this.description = description;
    }
}
```

### 2.1 角色实体 (Role)

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_roles")
public class Role extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("role_code")
    private String roleCode;
    
    @Column(name = "role_name", nullable = false)
    private String roleName;
    
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private RoleStatus status;
    
    @Column(name = "sort_order")
    private Integer sortOrder;
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "sys_role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
    
    // 业务方法
    public void addPermission(Permission permission) {
        this.permissions.add(permission);
    }
    
    public void removePermission(Permission permission) {
        this.permissions.remove(permission);
    }
    
    public boolean hasPermission(String permissionCode) {
        return permissions.stream()
            .anyMatch(p -> p.getPermissionCode().equals(permissionCode));
    }
}

public enum RoleStatus {
    ACTIVE("启用"),
    INACTIVE("禁用");
    
    private final String description;
    
    RoleStatus(String description) {
        this.description = description;
    }
}
```

### 2.2 权限实体 (Permission)

```java
@Entity
@Table(name = "sys_permissions")
@Data
@EqualsAndHashCode(callSuper = true)
public class Permission extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "permission_code", unique = true, nullable = false)
    private String permissionCode;
    
    @Column(name = "permission_name", nullable = false)
    private String permissionName;
    
    @Column(name = "resource_type")
    private String resourceType; // MENU, BUTTON, API
    
    @Column(name = "resource_url")
    private String resourceUrl;
    
    @Column(name = "parent_id")
    private Long parentId;
    
    @Column(name = "sort_order")
    private Integer sortOrder;
    
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PermissionStatus status;
}

public enum PermissionStatus {
    ACTIVE("启用"),
    INACTIVE("禁用");
    
    private final String description;
    
    PermissionStatus(String description) {
        this.description = description;
    }
}
```

### 2.3 部门实体 (Department)

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_departments")
public class Department extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("dept_code")
    private String deptCode;

    @TableField("dept_name")
    private String deptName;

    @TableField("parent_id")
    private Long parentId;

    @TableField("dept_level")
    private Integer deptLevel;

    @TableField("sort_order")
    private Integer sortOrder;

    @TableField("leader_id")
    private Long leaderId;

    @TableField("phone")
    private String phone;
    
    @TableField("email")
    private String email;

    @TableField("description")
    private String description;

    @TableField("status")
    @EnumValue
    private DepartmentStatus status;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public boolean isRootDepartment() {
        return this.parentId == null || this.parentId == 0;
    }
    
    public void setLeader(User user) {
        this.leaderId = user.getId();
        this.leader = user;
    }
}

public enum DepartmentStatus {
    ACTIVE("启用"),
    INACTIVE("禁用");
    
    private final String description;
    
    DepartmentStatus(String description) {
        this.description = description;
    }
}
```

## 3. 服务实现

### 3.1 用户服务 (UserService)

```java
@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public UserDTO createUser(CreateUserRequest request) {
        // 1. 验证用户名和邮箱唯一性
        if (userMapper.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        if (userMapper.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 2. 创建用户对象
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setRealName(request.getRealName());
        user.setDepartmentId(request.getDepartmentId());
        user.setStatus(UserStatus.ACTIVE);

        // 3. 保存用户
        userMapper.insert(user);

        // 4. 分配默认角色
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            assignRolesToUser(user.getId(), request.getRoleIds());
        }

        return convertToDTO(user);
    }

    @Override
    public LoginResult login(LoginRequest request) {
        // 1. 验证用户存在性
        User user = userMapper.findByUsername(request.getUsername());
        if (user == null) {
            throw new BusinessException("用户名或密码错误");
        }

        // 2. 验证用户状态
        if (!user.isActive()) {
            throw new BusinessException("用户已被禁用");
        }

        // 3. 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }

        // 4. 更新登录信息
        user.updateLoginInfo(request.getLoginIp());
        userMapper.updateById(user);

        // 5. 生成JWT Token
        String token = jwtTokenUtil.generateToken(user);

        // 6. 缓存用户信息
        String userKey = "user:session:" + user.getId();
        redisTemplate.opsForValue().set(userKey, user, Duration.ofHours(24));

        // 7. 构建登录结果
        LoginResult result = new LoginResult();
        result.setToken(token);
        result.setUser(convertToDTO(user));
        result.setPermissions(user.getPermissions());

        return result;
    }

    @Override
    public void logout(Long userId) {
        // 清除缓存的用户信息
        String userKey = "user:session:" + userId;
        redisTemplate.delete(userKey);
    }

    @Override
    public void assignRolesToUser(Long userId, List<Long> roleIds) {
        // 1. 清除用户现有角色
        userRoleMapper.deleteByUserId(userId);

        // 2. 分配新角色
        for (Long roleId : roleIds) {
            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRoleMapper.insert(userRole);
        }

        // 3. 清除用户权限缓存
        clearUserPermissionCache(userId);
    }

    private void clearUserPermissionCache(Long userId) {
        String permissionKey = "user:permissions:" + userId;
        redisTemplate.delete(permissionKey);
    }

    private UserDTO convertToDTO(User user) {
        UserDTO dto = new UserDTO();
        BeanUtils.copyProperties(user, dto);

        // 设置部门信息
        if (user.getDepartment() != null) {
            dto.setDepartmentName(user.getDepartment().getDeptName());
        }

        // 设置角色信息
        dto.setRoles(user.getRoles().stream()
            .map(this::convertRoleToDTO)
            .collect(Collectors.toList()));

        return dto;
    }
}
```

### 3.2 角色服务 (RoleService)

```java
@Service
@Transactional
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Override
    public RoleDTO createRole(CreateRoleRequest request) {
        // 1. 验证角色代码唯一性
        if (roleMapper.existsByRoleCode(request.getRoleCode())) {
            throw new BusinessException("角色代码已存在");
        }

        // 2. 创建角色
        Role role = new Role();
        role.setRoleCode(request.getRoleCode());
        role.setRoleName(request.getRoleName());
        role.setDescription(request.getDescription());
        role.setStatus(RoleStatus.ACTIVE);
        role.setSortOrder(request.getSortOrder());

        roleMapper.insert(role);

        // 3. 分配权限
        if (request.getPermissionIds() != null && !request.getPermissionIds().isEmpty()) {
            assignPermissionsToRole(role.getId(), request.getPermissionIds());
        }

        return convertToDTO(role);
    }

    @Override
    public void assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        // 1. 清除角色现有权限
        rolePermissionMapper.deleteByRoleId(roleId);

        // 2. 分配新权限
        for (Long permissionId : permissionIds) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(permissionId);
            rolePermissionMapper.insert(rolePermission);
        }

        // 3. 清除相关用户权限缓存
        clearRoleUsersPermissionCache(roleId);
    }

    private void clearRoleUsersPermissionCache(Long roleId) {
        // 获取拥有该角色的所有用户
        List<Long> userIds = userRoleMapper.getUserIdsByRoleId(roleId);

        // 清除这些用户的权限缓存
        for (Long userId : userIds) {
            String permissionKey = "user:permissions:" + userId;
            redisTemplate.delete(permissionKey);
        }
    }
}
```

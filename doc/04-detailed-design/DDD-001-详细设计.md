# DDD-001 PISP进销存+零售管理系统详细设计总览

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-001 |
| 文档名称 | PISP进销存+零售管理系统详细设计总览 |
| 版本号 | v3.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 📋 详细设计文档导航

本文档是进销存管理系统详细设计的总览文档。具体的业务模块设计请参考以下文档：

### 🏗️ 模块化设计文档

| 文档编号 | 文档名称 | 主要内容 |
|----------|----------|----------|
| **DDD-002** | [用户管理模块](./DDD-002-用户管理模块.md) | 用户注册登录、角色权限管理、组织架构管理 |
| **DDD-003** | [基础数据管理模块](./DDD-003-基础数据管理模块.md) | 商品管理、客户管理、供应商管理、仓库管理 |
| **DDD-004** | [采购管理模块](./DDD-004-采购管理模块.md) | 采购订单、采购入库、采购退货管理 |
| **DDD-005** | [销售管理模块](./DDD-005-销售管理模块.md) | 销售订单、销售出库、销售退货管理 |
| **DDD-006** | [库存管理模块](./DDD-006-库存管理模块.md) | 库存查询、库存盘点、库存预警、库存调拨 |
| **DDD-007** | [财务管理模块](./DDD-007-财务管理模块.md) | 应收应付、成本核算、财务对账 |
| **DDD-008** | [报表分析模块](./DDD-008-报表分析模块.md) | 销售报表、采购报表、库存报表、财务报表 |
| **DDD-009** | [系统管理模块](./DDD-009-系统管理模块.md) | 系统配置、数据备份、操作日志、系统监控 |
| **DDD-010** | [零售管理模块](./DDD-010-零售管理模块.md) | POS销售、会员管理、促销活动、门店管理 |
| **DDD-011** | [前置仓管理模块](./DDD-011-前置仓管理模块.md) | 前置仓管理、库存分配、智能补货、订单履约、拣选打包配送 |

### 🎯 模块关系图

```mermaid
graph TB
    subgraph "用户权限层"
        A[用户管理模块<br/>DDD-002]
    end

    subgraph "基础数据层"
        B[基础数据管理模块<br/>DDD-003]
    end

    subgraph "业务流程层"
        C[采购管理模块<br/>DDD-004]
        D[销售管理模块<br/>DDD-005]
        E[库存管理模块<br/>DDD-006]
    end

    subgraph "财务分析层"
        F[财务管理模块<br/>DDD-007]
        G[报表分析模块<br/>DDD-008]
    end

    subgraph "系统管理层"
        H[系统管理模块<br/>DDD-009]
    end

    subgraph "零售业务层"
        I[零售管理模块<br/>DDD-010]
    end

    subgraph "前置仓业务层"
        J[前置仓管理模块<br/>DDD-011<br/>包含订单履约]
    end

    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    A --> I
    A --> J

    B --> C
    B --> D
    B --> E
    B --> I
    B --> J
    C --> E
    D --> E
    D --> J
    C --> F
    D --> F
    J --> E
    E --> F
    I --> E
    I --> F
    C --> G
    D --> G
    E --> G
    F --> G
    I --> G
```

## 1. 系统架构概述

### 1.1 整体架构设计

```mermaid
graph TB
    subgraph "前端层"
        A[Vue 3 + Element Plus]
    end

    subgraph "网关层"
        B[Apache ShenYu 2.7.0.1]
    end

    subgraph "注册中心"
        NC[Nacos Server]
    end

    subgraph "微服务层"
        C[用户管理服务<br/>user-service]
        D[基础数据服务<br/>base-data-service]
        E[采购管理服务<br/>purchase-service]
        F[销售管理服务<br/>sales-service]
        G[库存管理服务<br/>inventory-service]
        H[财务管理服务<br/>finance-service]
        I[报表分析服务<br/>report-service]
        J[系统管理服务<br/>system-service]
        K[零售管理服务<br/>retail-service]
        L[前置仓管理服务<br/>front-warehouse-service]
    end

    subgraph "数据层"
        DB[PostgreSQL 17]
        CACHE[Redis 7.x]
    end

    subgraph "消息层"
        MQ[Apache RocketMQ 5.3.1]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    B --> I
    B --> J
    B --> K
    B --> L

    C --> NC
    D --> NC
    E --> NC
    F --> NC
    G --> NC
    H --> NC
    I --> NC
    J --> NC
    K --> NC
    L --> NC

    C --> DB
    D --> DB
    E --> DB
    F --> DB
    G --> DB
    H --> DB
    I --> DB
    J --> DB
    K --> DB
    L --> DB

    C --> CACHE
    D --> CACHE
    E --> CACHE
    F --> CACHE
    G --> CACHE
    H --> CACHE
    I --> CACHE
    J --> CACHE
    K --> CACHE
    L --> CACHE

    C --> MQ
    D --> MQ
    E --> MQ
    F --> MQ
    G --> MQ
    H --> MQ
    I --> MQ
    J --> MQ
    K --> MQ
    L --> MQ
```

### 1.2 设计原则

1. **领域驱动设计（DDD）**：以业务领域为核心，将复杂的业务逻辑封装在领域模型中
2. **微服务架构**：按业务边界拆分服务，每个服务独立部署和扩展
3. **CQRS模式**：读写分离，优化查询性能
4. **事件驱动架构**：通过领域事件实现服务间的松耦合
5. **六边形架构**：将业务逻辑与技术实现分离

### 1.3 技术栈

| 技术组件 | 版本 | 用途 |
|----------|------|------|
| **Java** | 21 (LTS) | 开发语言 |
| **Spring Boot** | 3.4.7 | 微服务框架 |
| **Spring Cloud** | 2024.0.1 | 微服务生态 |
| **Maven** | 3.9.x | 项目构建和依赖管理 |
| **Nacos** | 3.0.2 | 注册中心和配置中心 |
| **Apache ShenYu** | 2.7.0.1 | API网关 |
| **MyBatis-Plus** | 3.5.12 | 数据访问层 |
| **PostgreSQL** | 17 | 主数据库 |
| **Redis** | 7.x | 缓存和会话存储 |
| **Apache RocketMQ** | 5.3.1 | 消息中间件 |
| **Vue** | 3 | 前端框架 |
| **Element Plus** | 2.4.0 | UI组件库 |

## 2. Maven多模块结构

### 2.1 项目结构设计

```
pisp-system/
├── pom.xml                           # 父POM，统一管理依赖版本
├── pisp-common/                       # 公共模块
│   ├── pisp-common-core/             # 核心工具类
│   ├── pisp-common-security/         # 安全认证
│   ├── pisp-common-redis/            # Redis配置
│   ├── pisp-common-rocketmq/         # RocketMQ配置
│   └── pisp-common-web/              # Web通用配置
├── pisp-gateway/                      # ShenYu网关
├── pisp-services/                     # 业务服务模块
│   ├── pisp-user/                    # 用户管理服务
│   ├── pisp-base-data/               # 基础数据服务
│   ├── pisp-purchase/                # 采购管理服务
│   ├── pisp-sales/                   # 销售管理服务
│   ├── pisp-inventory/               # 库存管理服务
│   ├── pisp-finance/                 # 财务管理服务
│   ├── pisp-report/                  # 报表分析服务
│   ├── pisp-system/                  # 系统管理服务
│   ├── pisp-retail/                  # 零售管理服务
│   └── pisp-front-warehouse/         # 前置仓管理服务（包含订单履约）
└── pisp-api/                         # API接口定义
    ├── pisp-api-user/
    ├── pisp-api-base-data/
    ├── pisp-api-purchase/
    ├── pisp-api-sales/
    ├── pisp-api-inventory/
    ├── pisp-api-finance/
    ├── pisp-api-report/
    ├── pisp-api-system/
    ├── pisp-api-retail/
    └── pisp-api-front-warehouse/     # 前置仓管理API（包含订单履约API）
```

### 2.2 Nacos配置管理

#### 2.2.1 服务注册配置

```yaml
# application.yml (每个微服务)
spring:
  application:
    name: pisp-base-data-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: pisp-system
        group: DEFAULT_GROUP
        metadata:
          version: 1.0.0
          region: beijing
      config:
        server-addr: 127.0.0.1:8848
        namespace: pisp-system
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: datasource-config.yml
            group: COMMON_GROUP
            refresh: true
  config:
    import:
      - optional:nacos:common-config.yml
      - optional:nacos:datasource-config.yml
```

#### 2.2.2 配置文件管理

**Nacos配置中心文件结构：**
```
pisp-system (namespace)
├── COMMON_GROUP/
│   ├── common-config.yml           # 公共配置
│   ├── datasource-config.yml       # 数据源配置
│   ├── redis-config.yml            # Redis配置
│   └── rocketmq-config.yml         # RocketMQ配置
├── DEFAULT_GROUP/
│   ├── pisp-user-service.yml        # 用户管理服务配置
│   ├── pisp-base-data-service.yml   # 基础数据服务配置
│   ├── pisp-purchase-service.yml    # 采购服务配置
│   ├── pisp-sales-service.yml       # 销售服务配置
│   ├── pisp-inventory-service.yml   # 库存服务配置
│   ├── pisp-finance-service.yml     # 财务服务配置
│   ├── pisp-report-service.yml      # 报表服务配置
│   ├── pisp-system-service.yml      # 系统管理服务配置
│   └── pisp-retail-service.yml      # 零售管理服务配置
└── GATEWAY_GROUP/
    └── pisp-gateway.yml             # 网关配置
```
### 2.3 Maven依赖管理

#### 2.3.1 父POM配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bdyl.ecom.pisp</groupId>
    <artifactId>pisp-system</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 核心框架版本 -->
        <spring-boot.version>3.4.7</spring-boot.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>

        <!-- 数据库和缓存 -->
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <postgresql.version>42.7.3</postgresql.version>
        <redis.version>7.2.4</redis.version>

        <!-- 消息中间件 -->
        <rocketmq.version>5.3.1</rocketmq.version>

        <!-- 工具库 -->
        <lombok.version>1.18.30</lombok.version>
        <hutool.version>5.8.25</hutool.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud Alibaba BOM -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>pisp-common</module>
        <module>pisp-gateway</module>
        <module>pisp-services</module>
        <module>pisp-api</module>
    </modules>
</project>
```

#### 2.3.2 微服务模块配置示例

```xml
<!-- pisp-services/pisp-user/pom.xml -->
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Spring Web for HTTP Interface -->
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webflux</artifactId>
    </dependency>

    <!-- Spring Cloud LoadBalancer -->
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>

    <!-- Nacos -->
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>

    <!-- 数据库 -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus.version}</version>
    </dependency>
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
    </dependency>

    <!-- Redis -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>

    <!-- RocketMQ -->
    <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>${rocketmq.version}</version>
    </dependency>
</dependencies>
```

## 3. 业务模块概述

### 3.1 用户管理模块 (DDD-002)

**核心职责：**
- 用户注册登录：用户注册、登录认证、密码管理
- 角色权限管理：角色定义、权限分配、访问控制
- 用户信息管理：用户档案、组织架构、部门管理

**关键实体：**
- User（用户）
- Role（角色）
- Permission（权限）
- Department（部门）

**服务名称：** `pisp-user-service`

### 3.2 基础数据管理模块 (DDD-003)

**核心职责：**
- 商品信息管理：商品分类、商品档案、价格管理
- 客户信息管理：客户档案、信用管理、联系人管理
- 供应商管理：供应商档案、评级管理、合作协议
- 仓库管理：仓库设置、库位管理、仓库权限

**关键实体：**
- Product（商品）
- Customer（客户）
- Supplier（供应商）
- Warehouse（仓库）

**服务名称：** `pisp-base-data-service`

### 3.3 采购管理模块 (DDD-004)

**核心职责：**
- 采购订单管理：需求计划、询价比价、订单审批
- 采购入库管理：收货确认、质量检验、成本核算
- 采购退货管理：退货申请、退货处理、库存调整

**关键实体：**
- PurchaseOrder（采购订单）
- PurchaseReceipt（采购入库）
- PurchaseReturn（采购退货）

**服务名称：** `pisp-purchase-service`

### 3.4 销售管理模块 (DDD-005)

**核心职责：**
- 销售订单管理：客户报价、信用检查、订单审批
- 销售出库管理：发货计划、出库确认、物流跟踪
- 销售退货管理：退货申请、退货处理、库存恢复

**关键实体：**
- SalesOrder（销售订单）
- SalesShipment（销售出库）
- SalesReturn（销售退货）

**服务名称：** `pisp-sales-service`

### 3.5 库存管理模块 (DDD-006)

**核心职责：**
- 库存查询监控：实时库存、多维度统计、库存分析
- 库存盘点：定期盘点、循环盘点、差异处理
- 库存预警：低库存预警、缺货预警、超库存预警
- 库存调拨：仓库间调拨、调拨审批、调拨跟踪

**关键实体：**
- Inventory（库存）
- InventoryTransaction（库存事务）
- InventoryCheck（库存盘点）
- InventoryTransfer（库存调拨）

**服务名称：** `pisp-inventory-service`

### 3.6 财务管理模块 (DDD-007)

**核心职责：**
- 应收应付管理：应收账款、应付账款、账期管理
- 成本核算：加权平均成本、移动平均成本、成本分析
- 财务对账：自动对账、差异处理、对账报告

**关键实体：**
- AccountsReceivable（应收账款）
- AccountsPayable（应付账款）
- CostCalculation（成本核算）

**服务名称：** `pisp-finance-service`

### 3.7 报表分析模块 (DDD-008)

**核心职责：**
- 销售报表：销售汇总、趋势分析、客户分析、商品分析
- 采购报表：采购汇总、供应商分析、采购趋势
- 库存报表：库存价值、库存变动、库存周转
- 财务报表：应收应付账龄、成本分析、利润分析

**关键服务：**
- SalesReportService（销售报表）
- PurchaseReportService（采购报表）
- InventoryReportService（库存报表）
- FinancialReportService（财务报表）

**服务名称：** `pisp-report-service`

### 3.8 系统管理模块 (DDD-009)

**核心职责：**
- 系统配置管理：系统参数、业务配置、字典管理
- 数据备份恢复：定时备份、数据恢复、备份策略
- 操作日志管理：用户操作日志、系统日志、审计追踪
- 系统监控：性能监控、健康检查、告警管理

**关键实体：**
- SystemConfig（系统配置）
- DataBackup（数据备份）
- OperationLog（操作日志）
- SystemMonitor（系统监控）

**服务名称：** `pisp-system-service`

### 3.9 零售管理模块 (DDD-010)

**核心职责：**
- POS销售管理：商品扫码、价格计算、支付处理、小票打印
- 会员管理：会员注册、积分管理、等级升级、权益管理
- 促销管理：促销活动配置、优惠计算、使用统计
- 门店管理：门店信息、POS设备、营业管理

**关键实体：**
- RetailPosSale（POS销售）
- RetailMember（会员）
- RetailPromotion（促销活动）
- RetailStore（门店）

**服务名称：** `pisp-retail-service`

## 4. 微服务设计模式

### 4.1 服务注册与发现

```java
// 服务提供者配置
@SpringBootApplication
@EnableDiscoveryClient
public class BaseDataServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(BaseDataServiceApplication.class, args);
    }
}

// 服务消费者配置
@RestController
public class SalesController {

    private final BaseDataServiceClient baseDataServiceClient;

    public SalesController(BaseDataServiceClient baseDataServiceClient) {
        this.baseDataServiceClient = baseDataServiceClient;
    }

    @GetMapping("/products/{id}")
    public ProductDTO getProduct(@PathVariable Long id) {
        return baseDataServiceClient.getProduct(id);
    }

    @PostMapping("/products")
    public ProductDTO createProduct(@RequestBody CreateProductRequest request) {
        return baseDataServiceClient.createProduct(request);
    }
}
```

### 4.2 HTTP Interface客户端配置

```java
// 服务间调用接口 - 使用Spring 6 HTTP Interface
@HttpExchange("/api/v1")
public interface BaseDataServiceClient {

    @GetExchange("/products/{id}")
    ProductDTO getProduct(@PathVariable Long id);

    @GetExchange("/customers/{id}")
    CustomerDTO getCustomer(@PathVariable Long id);

    @GetExchange("/suppliers/{id}")
    SupplierDTO getSupplier(@PathVariable Long id);

    @PostExchange("/products")
    ProductDTO createProduct(@RequestBody CreateProductRequest request);

    @PutExchange("/products/{id}")
    ProductDTO updateProduct(@PathVariable Long id, @RequestBody UpdateProductRequest request);
}
```

**HTTP Interface配置类：**

```java
@Configuration
@EnableConfigurationProperties(ServiceClientProperties.class)
public class HttpInterfaceConfiguration {

    @Bean
    public BaseDataServiceClient baseDataServiceClient(
            WebClient.Builder webClientBuilder,
            ServiceClientProperties properties) {

        WebClient webClient = webClientBuilder
            .baseUrl(properties.getBaseDataService().getBaseUrl())
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024))
            .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
            .builderFor(WebClientAdapter.create(webClient))
            .build();

        return factory.createClient(BaseDataServiceClient.class);
    }

    @Bean
    public InventoryServiceClient inventoryServiceClient(
            WebClient.Builder webClientBuilder,
            ServiceClientProperties properties) {

        WebClient webClient = webClientBuilder
            .baseUrl(properties.getInventoryService().getBaseUrl())
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
            .builderFor(WebClientAdapter.create(webClient))
            .build();

        return factory.createClient(InventoryServiceClient.class);
    }

    @Bean
    public FrontWarehouseServiceClient frontWarehouseServiceClient(
            WebClient.Builder webClientBuilder,
            ServiceClientProperties properties) {

        WebClient webClient = webClientBuilder
            .baseUrl(properties.getFrontWarehouseService().getBaseUrl())
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
            .builderFor(WebClientAdapter.create(webClient))
            .build();

        return factory.createClient(FrontWarehouseServiceClient.class);
    }
}
```

**服务客户端配置属性：**

```java
@ConfigurationProperties(prefix = "pisp.service-clients")
@Data
public class ServiceClientProperties {

    private ServiceConfig baseDataService = new ServiceConfig();
    private ServiceConfig inventoryService = new ServiceConfig();
    private ServiceConfig frontWarehouseService = new ServiceConfig();
    private ServiceConfig financeService = new ServiceConfig();

    @Data
    public static class ServiceConfig {
        private String baseUrl;
        private Duration connectTimeout = Duration.ofSeconds(5);
        private Duration readTimeout = Duration.ofSeconds(30);
        private int maxRetries = 3;
        private boolean enableCircuitBreaker = true;
    }
}
```

### 4.3 配置管理

```java
// 配置类示例
@Component
@RefreshScope
@ConfigurationProperties(prefix = "pisp.business")
public class BusinessConfig {

    private String defaultCurrency = "CNY";
    private Integer orderTimeoutDays = 30;
    private BigDecimal taxRate = new BigDecimal("0.13");

    // getters and setters
}
```

### 4.4 事件驱动架构

```java
// 领域事件发布
@Service
public class SalesOrderService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public void approveSalesOrder(Long orderId, Long approverId) {
        // 业务逻辑处理
        SalesOrder order = salesOrderRepository.findById(orderId);
        order.approve(approverId);
        salesOrderRepository.save(order);

        // 发布领域事件
        SalesOrderApprovedEvent event = new SalesOrderApprovedEvent(
            orderId, approverId, LocalDateTime.now()
        );

        // 本地事件
        eventPublisher.publishEvent(event);

        // RocketMQ事件
        rocketMQTemplate.convertAndSend("sales-events:order-approved", event);
    }
}
```

### 4.5 WebClient配置和负载均衡

```yaml
# application.yml - 服务客户端配置
pisp:
  service-clients:
    base-data-service:
      base-url: http://pisp-base-data-service
      connect-timeout: 5s
      read-timeout: 30s
      max-retries: 3
      enable-circuit-breaker: true
    inventory-service:
      base-url: http://pisp-inventory-service
      connect-timeout: 5s
      read-timeout: 30s
    front-warehouse-service:
      base-url: http://pisp-front-warehouse-service
      connect-timeout: 5s
      read-timeout: 30s
    finance-service:
      base-url: http://pisp-finance-service
      connect-timeout: 5s
      read-timeout: 30s

# Spring Cloud LoadBalancer配置
spring:
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
      cache:
        enabled: true
        ttl: 35s
        capacity: 256
```

**WebClient全局配置：**

```java
@Configuration
public class WebClientConfiguration {

    @Bean
    @LoadBalanced
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder()
            .defaultHeader(HttpHeaders.USER_AGENT, "PISP-System/1.0")
            .defaultHeader("X-Request-Source", "PISP-Internal")
            .codecs(configurer -> {
                configurer.defaultCodecs().maxInMemorySize(2 * 1024 * 1024); // 2MB
                configurer.defaultCodecs().enableLoggingRequestDetails(true);
            })
            .filter(ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
                // 添加请求ID用于链路追踪
                String requestId = UUID.randomUUID().toString();
                return Mono.just(ClientRequest.from(clientRequest)
                    .header("X-Request-ID", requestId)
                    .build());
            }))
            .filter(ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
                // 响应日志记录
                if (clientResponse.statusCode().isError()) {
                    log.warn("HTTP调用失败: {} {}",
                        clientResponse.statusCode(),
                        clientResponse.request().getURI());
                }
                return Mono.just(clientResponse);
            }));
    }

    @Bean
    public ReactorLoadBalancerExchangeFilterFunction lbFunction(
            ReactiveLoadBalancer.Factory<ServiceInstance> loadBalancerFactory) {
        return new ReactorLoadBalancerExchangeFilterFunction(loadBalancerFactory);
    }
}
```

### 4.6 分布式事务处理

```java
// Seata分布式事务示例 - 使用HTTP Interface
@Service
public class OrderProcessService {

    private final SalesOrderService salesOrderService;
    private final InventoryServiceClient inventoryServiceClient;
    private final FinanceServiceClient financeServiceClient;

    public OrderProcessService(
            SalesOrderService salesOrderService,
            InventoryServiceClient inventoryServiceClient,
            FinanceServiceClient financeServiceClient) {
        this.salesOrderService = salesOrderService;
        this.inventoryServiceClient = inventoryServiceClient;
        this.financeServiceClient = financeServiceClient;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void processOrder(CreateOrderRequest request) {
        try {
            // 1. 创建销售订单
            SalesOrderDTO order = salesOrderService.createOrder(request);

            // 2. 预留库存 - 使用HTTP Interface调用
            ReserveInventoryRequest reserveRequest = ReserveInventoryRequest.builder()
                .orderId(order.getId())
                .items(request.getOrderItems())
                .build();
            inventoryServiceClient.reserveInventory(reserveRequest);

            // 3. 占用客户信用额度 - 使用HTTP Interface调用
            OccupyCreditRequest creditRequest = OccupyCreditRequest.builder()
                .customerId(request.getCustomerId())
                .amount(order.getTotalAmount())
                .orderId(order.getId())
                .build();
            financeServiceClient.occupyCredit(creditRequest);

        } catch (Exception e) {
            log.error("订单处理失败: {}", e.getMessage(), e);
            throw new OrderProcessException("订单处理失败", e);
        }
    }
}
```

## 5. 开发指南

### 5.1 微服务开发顺序

1. **第一阶段**：基础设施搭建
   - Nacos注册中心部署
   - ShenYu网关配置
   - 公共模块开发（pisp-common）

2. **第二阶段**：用户管理服务（pisp-user-service）
   - 用户注册登录、角色权限管理
   - 为其他服务提供认证授权支撑

3. **第三阶段**：基础数据服务（pisp-base-data-service）
   - 商品、客户、供应商、仓库管理
   - 为其他服务提供基础数据支撑

4. **第四阶段**：核心业务服务
   - 采购管理服务（pisp-purchase-service）
   - 销售管理服务（pisp-sales-service）
   - 库存管理服务（pisp-inventory-service）

5. **第五阶段**：财务服务（pisp-finance-service）
   - 应收应付管理
   - 成本核算

6. **第六阶段**：分析和管理服务
   - 报表分析服务（pisp-report-service）
   - 系统管理服务（pisp-system-service）

7. **第七阶段**：零售业务服务
   - 零售管理服务（pisp-retail-service）

### 5.2 团队分工建议

| 团队 | 负责服务 | 主要职责 |
|------|----------|----------|
| **基础架构团队** | pisp-common, pisp-gateway | 公共组件、网关配置、基础设施 |
| **用户管理团队** | pisp-user-service | 用户注册登录、角色权限、组织架构 |
| **基础数据团队** | pisp-base-data-service | 商品、客户、供应商、仓库管理 |
| **采购团队** | pisp-purchase-service | 采购订单、入库、退货流程 |
| **销售团队** | pisp-sales-service | 销售订单、出库、退货流程 |
| **库存团队** | pisp-inventory-service | 库存查询、盘点、预警、调拨 |
| **财务团队** | pisp-finance-service | 应收应付、成本核算、对账 |
| **数据分析团队** | pisp-report-service | 各类业务报表和分析 |
| **系统管理团队** | pisp-system-service | 系统配置、数据备份、日志管理、监控 |
| **零售业务团队** | pisp-retail-service | POS销售、会员管理、促销活动、门店管理 |

### 5.3 环境配置指南

#### 5.3.1 开发环境
- **JDK**：OpenJDK 21 (LTS)
- **Maven**：3.9.x
- **IDE**：IntelliJ IDEA 2024.x
- **Spring Boot**：3.4.7
- **Spring Cloud**：2024.0.1
- **Nacos**：3.0.2 (单机模式)
- **PostgreSQL**：17.x (本地数据库)
- **Redis**：7.x (单机模式)
- **RocketMQ**：5.3.1 (单机模式)

#### 5.3.2 测试环境
- **Nacos**：3.0.2 集群模式（3节点）
- **PostgreSQL**：17.x 主从模式
- **Redis**：7.x 哨兵模式
- **RocketMQ**：5.3.1 集群模式（3节点）
- **ShenYu**：2.7.0.1 网关集群

#### 5.3.3 生产环境
- **Nacos**：3.0.2 集群模式（3节点）
- **PostgreSQL**：17.x 主从+读写分离
- **Redis**：7.x 集群模式
- **RocketMQ**：5.3.1 集群模式（3节点）
- **负载均衡**：Nginx + ShenYu网关集群
- **监控**：Prometheus + Grafana + Micrometer

### 5.4 版本兼容性说明

#### 5.4.1 核心依赖兼容性矩阵

| 组件 | 版本 | 兼容性验证 | 说明 |
|------|------|------------|------|
| **Java** | 21 (LTS) | ✅ 官方支持 | 长期支持版本 |
| **Spring Boot** | 3.4.7 | ✅ 最新稳定版 | 2024年12月发布 |
| **Spring Cloud** | 2024.0.1 | ✅ 官方兼容 | Moorgate版本 |
| **Nacos** | 3.0.2 | ✅ 完全支持 | 支持Spring Boot 3.4.x |
| **RocketMQ** | 5.3.1 | ✅ 原生支持 | 2024年最新稳定版 |
| **PostgreSQL** | 17.x | ✅ JDBC兼容 | 最新主版本 |
| **Redis** | 7.x | ✅ 客户端兼容 | Lettuce连接池 |

#### 5.4.2 升级路径建议

1. **从旧版本升级**：
   - Spring Boot 3.2.x → 3.4.7：平滑升级
   - Spring Cloud 2023.x → 2024.0.1：配置调整
   - Nacos 2.x → 3.0.2：数据迁移

2. **依赖冲突解决**：
   - 使用BOM统一版本管理
   - 排除传递依赖冲突
   - 版本锁定策略

## 6. 系统设计图表

### 5.1 核心领域类图

```mermaid
classDiagram
    class BaseEntity {
        +Long id
        +LocalDateTime createTime
        +LocalDateTime updateTime
        +Long creatorId
        +Long updaterId
        +String additionalInfo
        +String remark
        +Integer version
    }

    class Product {
        +String productCode
        +String productName
        +Long categoryId
        +String unit
        +BigDecimal costPrice
        +BigDecimal salePrice
        +ProductStatus status
        +updatePrice()
        +activate()
        +deactivate()
    }

    class Customer {
        +String customerCode
        +String customerName
        +CustomerType customerType
        +BigDecimal creditLimit
        +BigDecimal creditUsed
        +occupyCredit()
        +releaseCredit()
        +hasAvailableCredit()
    }

    class SalesOrder {
        +String orderNumber
        +Long customerId
        +SalesOrderStatus status
        +BigDecimal totalAmount
        +BigDecimal finalAmount
        +approve()
        +cancel()
        +addOrderItem()
    }

    class SalesOrderItem {
        +Long salesOrderId
        +Long productId
        +BigDecimal quantity
        +BigDecimal unitPrice
        +BigDecimal totalAmount
        +updateQuantity()
        +updatePrice()
    }

    class Inventory {
        +Long productId
        +Long warehouseId
        +BigDecimal availableQuantity
        +BigDecimal reservedQuantity
        +BigDecimal totalQuantity
        +adjustQuantity()
        +isLowStock()
        +isOverStock()
    }

    BaseEntity <|-- Product
    BaseEntity <|-- Customer
    BaseEntity <|-- SalesOrder
    BaseEntity <|-- SalesOrderItem
    BaseEntity <|-- Inventory

    SalesOrder o-- SalesOrderItem
    Customer o-- SalesOrder
    Product --o SalesOrderItem
    Product --o Inventory
```

### 5.2 基础实体类定义

所有业务实体都继承自BaseEntity，提供统一的基础字段和功能：

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public abstract class BaseEntity {

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    @TableField(value = "updater_id", fill = FieldFill.INSERT_UPDATE)
    private Long updaterId;

    @TableField(value = "additional_info")
    private String additionalInfo; // JSON格式，保存额外信息

    @TableField(value = "remark")
    private String remark; // 备注信息
}
```

BaseEntity提供了以下统一功能：

- **自动时间戳**：创建时间和更新时间自动填充
- **操作人记录**：记录创建人和更新人
- **扩展信息**：additionalInfo字段支持JSON格式的额外信息存储
- **备注支持**：remark字段用于存储备注信息
- **MyBatis-Plus集成**：使用注解配置自动填充策略

### 5.3 销售订单处理时序图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as ShenYu网关
    participant S as 销售服务
    participant I as 库存服务
    participant F as 财务服务
    participant K as RocketMQ
    participant D as 数据库

    C->>G: 创建销售订单请求
    G->>S: 转发请求

    S->>D: 验证客户信息
    D-->>S: 返回客户信息

    S->>F: 检查客户信用额度
    F-->>S: 返回信用检查结果

    S->>I: 检查库存可用性
    I-->>S: 返回库存检查结果

    S->>D: 创建销售订单
    D-->>S: 返回订单ID

    S->>K: 发布订单创建事件
    K-->>I: 订单创建事件
    K-->>F: 订单创建事件

    I->>D: 预留库存
    F->>D: 占用信用额度

    S-->>G: 返回创建结果
    G-->>C: 返回响应

    Note over S,K: 异步事件处理
    K->>S: 库存预留完成事件
    K->>S: 信用占用完成事件
```

### 5.3 库存管理交互图

```mermaid
graph TB
    subgraph "库存管理交互流程"
        A[库存查询请求] --> B{库存服务}
        B --> C[实时库存计算]
        C --> D[缓存检查]
        D --> E{缓存命中?}
        E -->|是| F[返回缓存数据]
        E -->|否| G[查询数据库]
        G --> H[计算库存数据]
        H --> I[更新缓存]
        I --> J[返回库存信息]

        K[库存变动事件] --> L[RocketMQ消息]
        L --> M[库存事务处理]
        M --> N[更新库存表]
        N --> O[清除相关缓存]
        O --> P[发布库存更新事件]

        Q[库存预警检查] --> R[定时任务]
        R --> S[扫描低库存商品]
        S --> T{是否需要预警?}
        T -->|是| U[创建预警记录]
        T -->|否| V[继续扫描]
        U --> W[发送预警通知]
        W --> X[RocketMQ预警事件]
    end
```

### 5.4 零售管理交互图

```mermaid
graph TB
    subgraph "POS销售交互流程"
        A[POS扫码] --> B{商品服务}
        B --> C[获取商品信息]
        C --> D[显示商品价格]

        E[会员识别] --> F{会员服务}
        F --> G[查询会员信息]
        G --> H[显示会员等级]

        I[促销计算] --> J{促销服务}
        J --> K[匹配促销规则]
        K --> L[计算优惠金额]
        L --> M[应用会员折扣]

        N[支付处理] --> O{支付服务}
        O --> P[处理支付]
        P --> Q{支付成功?}
        Q -->|是| R[库存扣减]
        Q -->|否| S[支付失败]

        R --> T[更新会员积分]
        T --> U[记录财务数据]
        U --> V[打印小票]
        V --> W[发布销售事件]
    end

    subgraph "会员管理交互流程"
        X[会员注册] --> Y[验证手机号]
        Y --> Z[生成会员号]
        Z --> AA[赠送积分]
        AA --> BB[发送欢迎短信]

        CC[积分使用] --> DD[验证积分余额]
        DD --> EE[扣减积分]
        EE --> FF[记录积分日志]

        GG[等级升级] --> HH[检查消费金额]
        HH --> II[自动升级等级]
        II --> JJ[更新会员权益]
    end

    subgraph "促销管理交互流程"
        KK[促销创建] --> LL[设置促销规则]
        LL --> MM[配置适用范围]
        MM --> NN[激活促销活动]

        OO[促销应用] --> PP[匹配促销条件]
        PP --> QQ[计算优惠金额]
        QQ --> RR[更新使用次数]
        RR --> SS[记录促销效果]
    end
```

### 5.5 事件驱动架构图

```mermaid
graph LR
    subgraph "事件生产者"
        A[销售服务]
        B[采购服务]
        C[库存服务]
        D[财务服务]
        E[零售服务]
    end

    subgraph "RocketMQ集群"
        F[sales-events]
        G[purchase-events]
        H[inventory-events]
        I[finance-events]
        J[retail-events]
        K[notification-events]
    end

    subgraph "事件消费者"
        L[库存更新处理器]
        M[财务处理器]
        N[通知处理器]
        O[报表处理器]
        P[审计处理器]
        Q[会员积分处理器]
    end

    A --> F
    A --> K
    B --> G
    B --> K
    C --> H
    C --> K
    D --> I
    D --> K
    E --> J
    E --> K
    D --> H
    D --> I

    E --> J
    E --> K
    E --> M
    F --> J
    F --> K
    F --> M
    G --> L
    G --> M
    H --> L
    H --> M
    I --> L

    E --> N
    F --> N
    G --> N
    H --> N
```

### 5.5 微服务部署架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx负载均衡器]
    end

    subgraph "网关层"
        GW1[ShenYu网关-1]
        GW2[ShenYu网关-2]
    end

    subgraph "注册中心集群"
        NC1[Nacos-1]
        NC2[Nacos-2]
        NC3[Nacos-3]
    end

    subgraph "微服务集群"
        subgraph "基础数据服务"
            BD1[base-data-service-1]
            BD2[base-data-service-2]
        end

        subgraph "销售服务"
            S1[sales-service-1]
            S2[sales-service-2]
        end

        subgraph "库存服务"
            I1[inventory-service-1]
            I2[inventory-service-2]
        end

        subgraph "采购服务"
            P1[purchase-service-1]
            P2[purchase-service-2]
        end

        subgraph "财务服务"
            F1[finance-service-1]
            F2[finance-service-2]
        end

        subgraph "报表服务"
            R1[report-service-1]
            R2[report-service-2]
        end

        subgraph "零售服务"
            RT1[retail-service-1]
            RT2[retail-service-2]
        end
    end

    subgraph "中间件层"
        subgraph "RocketMQ集群"
            K1[RocketMQ-1]
            K2[RocketMQ-2]
            K3[RocketMQ-3]
        end

        subgraph "Redis集群"
            RD1[Redis-Master]
            RD2[Redis-Slave]
        end
    end

    subgraph "数据层"
        subgraph "PostgreSQL集群"
            DB1[PostgreSQL-Master]
            DB2[PostgreSQL-Slave]
        end
    end

    LB --> GW1
    LB --> GW2

    GW1 --> BD1
    GW1 --> S1
    GW1 --> I1
    GW1 --> P1
    GW1 --> F1
    GW1 --> R1
    GW1 --> RT1

    GW2 --> BD2
    GW2 --> S2
    GW2 --> I2
    GW2 --> P2
    GW2 --> F2
    GW2 --> R2
    GW2 --> RT2

    BD1 --> NC1
    BD2 --> NC2
    S1 --> NC1
    S2 --> NC2
    I1 --> NC3
    I2 --> NC1
    P1 --> NC2
    P2 --> NC3
    F1 --> NC1
    F2 --> NC2
    R1 --> NC3
    R2 --> NC1

    BD1 --> RD1
    BD2 --> RD1
    S1 --> RD1
    S2 --> RD1
    I1 --> RD1
    I2 --> RD1
    P1 --> RD1
    P2 --> RD1
    F1 --> RD1
    F2 --> RD1
    R1 --> RD1
    R2 --> RD1

    BD1 --> DB1
    BD2 --> DB1
    S1 --> DB1
    S2 --> DB1
    I1 --> DB1
    I2 --> DB1
    P1 --> DB1
    P2 --> DB1
    F1 --> DB1
    F2 --> DB1
    R1 --> DB1
    R2 --> DB1

    DB1 --> DB2
    RD1 --> RD2
    NC1 --> NC2
    NC2 --> NC3
    NC3 --> NC1
```

## 7. 前置仓管理API模块集成

### 7.1 模块概述

**模块名称：** pisp-api-front-warehouse

**技术栈：** Spring Boot 3.4.7 + OpenAPI 3.0 + Bean Validation

**职责：** 定义前置仓管理相关的完整API接口，包括前置仓管理、库存分配、智能补货、订单履约、拣选打包、配送调度等功能的接口定义

**核心功能模块：**
- 前置仓信息管理API
- 前置仓库存管理API
- 库存分配策略API
- 智能补货API
- 覆盖区域管理API
- 订单履约管理API
- 拣选任务管理API
- 打包任务管理API
- 配送任务管理API

> **详细API接口设计参考：** [DDD-011-前置仓管理模块.md](./DDD-011-前置仓管理模块.md) 第8章

#### 6.1.2 Maven模块结构

```xml
<!-- pisp-api-front-warehouse/pom.xml -->
<project>
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bdyl.ecom.pisp</groupId>
        <artifactId>pisp-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>pisp-api-front-warehouse</artifactId>
    <name>PISP :: API :: Front Warehouse</name>
    <description>前置仓管理API接口定义</description>

    <dependencies>
        <dependency>
            <groupId>com.bdyl.ecom.pisp</groupId>
            <artifactId>pisp-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
    </dependencies>
</project>
```

### 7.2 Maven模块集成

**主要API模块：**

| API模块 | 职责 | 核心接口 | 依赖服务 |
|---------|------|----------|----------|
| **pisp-api-front-warehouse** | 前置仓管理API定义 | 前置仓管理、库存管理、智能补货、订单履约、拣选打包、配送调度 | pisp-front-warehouse-service |

**API接口分类：**
- **前置仓管理API** - 前置仓基础信息的CRUD操作
- **库存管理API** - 库存查询、调整、分配、预留、同步
- **智能补货API** - 补货策略、订单、建议生成
- **订单履约API** - 订单分配、拣选、打包、配送全流程
- **拣选管理API** - 拣选任务创建、状态更新、路径优化
- **打包发货API** - 打包任务管理、发货处理
- **配送调度API** - 配送任务调度、路线优化、状态跟踪



### 7.3 系统集成配置

**网关路由集成：**
- **前置仓管理服务** - `/api/v1/front-warehouses/**` → `pisp-front-warehouse-service`
- **限流配置** - 100 QPS，突发容量 200
- **熔断保护** - 服务降级和故障转移
- **权限控制** - JWT认证 + RBAC授权

**详细网关配置参考：** [DDD-011-前置仓管理模块.md](./DDD-011-前置仓管理模块.md) 第8.5节


### 7.4 高危操作二次确认机制集成

**高危操作类型：**
- **前置仓删除** - 物理删除前置仓及相关数据
- **批量库存操作** - 大批量库存调整和删除
- **强制分配** - 跨前置仓强制库存分配
- **紧急补货** - 绕过审批流程的紧急补货

**确认机制：**
- **令牌生成** - 操作前生成确认令牌
- **时效控制** - 令牌有效期3-10分钟
- **操作审计** - 完整的操作日志记录
- **权限验证** - 多级权限验证机制

**详细确认机制设计参考：** [DDD-011-前置仓管理模块.md](./DDD-011-前置仓管理模块.md) 第8.6节

## 8. 系统架构图

### 8.1 API接口模块集成

通过新增的API接口模块，PISP系统现在完全支持前置仓管理和订单履约的完整业务流程：
**📡 新增API模块：**
- **pisp-api-front-warehouse**：前置仓管理API接口定义（包含完整的订单履约功能）

- **前置仓管理**：前置仓的创建、更新、删除和查询
- **库存管理**：前置仓库存的实时监控和管理
- **智能补货**：基于算法的自动补货建议和执行
- **订单履约**：订单分配、拣选、打包、配送的完整流程
- **拣选管理**：拣选任务的创建、分配和执行跟踪
- **打包管理**：打包任务的管理和质量控制
- **配送调度管理**：配送任务的调度和实时跟踪

> **详细业务流程和算法实现参考：** [DDD-011-前置仓管理模块.md](./DDD-011-前置仓管理模块.md)
### 8.2 技术特性总结
通过新增的API接口模块，PISP系统提供了标准化的RESTful API接口：
**📡 API特性：**
- **标准化设计**：遵循RESTful设计原则，使用OpenAPI 3.0规范

- **完整的数据验证**：Bean Validation注解确保数据完整性
- **安全机制完善**：JWT认证、角色权限、高危操作二次确认
- **高危操作保护**：完全支持物理删除策略和二次确认机制

- **服务间通信**：Spring 6 HTTP Interface支持微服务间调用
- **网关路由管理**：ShenYu网关的统一路由和安全控制

### 7.4 高危操作二次确认机制集成

前置仓管理API模块完全支持物理删除策略和高危操作二次确认机制：

**🔒 高危操作保护：**
- **确认令牌机制**：基于Redis的一次性确认令牌
- **操作审计日志**：所有高危操作的详细记录
- **用户身份验证**：确保操作者身份的合法性
- **时效性控制**：确认令牌5分钟有效期

**⚠️ 高危操作列表：**
- 删除前置仓：`DELETE_FRONT_WAREHOUSE`
- 批量删除库存：`BATCH_DELETE_INVENTORY`
- 清空补货计划：`CLEAR_REPLENISHMENT_PLAN`
- 取消配送任务：`CANCEL_DELIVERY_TASK`

> **详细实现参考：** [DDD-011-前置仓管理模块.md](./DDD-011-前置仓管理模块.md) 第8.6节

### 7.5 系统集成配置

**🔧 Maven模块集成：**
- 新增API模块：`pisp-api-front-warehouse`
- 新增服务模块：`pisp-front-warehouse-service`
- 统一依赖管理和版本控制

**🐳 Docker部署集成：**
- 容器化部署配置
- 服务发现和配置管理
- 数据库连接和缓存配置

**🌐 网关路由集成：**
- ShenYu网关路由规则配置
- 统一的认证和权限控制
- API限流和熔断保护

### 7.6 API特性总结

通过新增的API接口模块，PISP系统提供了标准化的RESTful API接口：

**📡 API特性：**
- **标准化设计**：遵循RESTful设计原则，使用OpenAPI 3.0规范
- **完整的数据验证**：Bean Validation注解确保数据完整性
- **安全机制完善**：JWT认证、角色权限、高危操作二次确认
- **高危操作保护**：完全支持物理删除策略和二次确认机制
- **网关路由管理**：ShenYu网关的统一路由和安全控制

## 9. 系统集成总结

### 9.1 前置仓管理API模块集成

通过新增的前置仓管理API模块，PISP系统现在完全支持前置仓管理和订单履约的完整业务流程：

**📡 新增API模块：**
- **pisp-api-front-warehouse**：前置仓管理API接口定义（包含完整的订单履约功能）

**🏪 支持的前置仓业务流程：**
- **前置仓信息管理**：前置仓基础信息的CRUD操作
- **库存分配管理**：智能库存分配策略和执行
- **智能补货管理**：多种补货策略的配置和执行
- **订单履约管理**：从订单分配到配送完成的全流程
- **拣选打包管理**：拣选和打包作业的管理和跟踪
- **配送调度管理**：配送任务的调度和实时跟踪

> **详细业务流程和算法实现参考：** [DDD-011-前置仓管理模块.md](./DDD-011-前置仓管理模块.md)

### 9.2 技术特性总结

通过新增的API接口模块，PISP系统提供了标准化的RESTful API接口：

**📡 API特性：**
- **标准化设计**：遵循RESTful设计原则，使用OpenAPI 3.0规范
- **完整的数据验证**：Bean Validation注解确保数据完整性
- **安全机制完善**：JWT认证、角色权限、高危操作二次确认
- **高危操作保护**：完全支持物理删除策略和二次确认机制
- **服务间通信**：Spring 6 HTTP Interface支持微服务间调用
- **网关路由管理**：ShenYu网关的统一路由和安全控制

### 9.3 系统架构演进

```mermaid
graph TB
    subgraph "PISP系统架构 v2.0"
        subgraph "API网关层"
            GW[Apache ShenYu Gateway<br/>统一入口 + 安全控制]
        end

        subgraph "API接口层"
            API1[传统业务API<br/>用户/基础数据/采购/销售/库存/财务/报表/系统/零售]
            API2[前置仓管理API<br/>前置仓管理/库存分配/智能补货/订单履约]
        end

        subgraph "业务服务层"
            BS1[传统业务服务<br/>9个微服务]
            BS2[前置仓管理服务<br/>智能算法引擎/订单履约服务]
        end

        subgraph "数据存储层"
            PG[(PostgreSQL 17<br/>10个Schema隔离)]
            RD[(Redis 7.x<br/>缓存&会话)]
            KF[(Apache RocketMQ<br/>事件消息)]
        end

        subgraph "监控运维层"
            MON[Prometheus + Grafana<br/>监控告警]
            LOG[ELK Stack<br/>日志分析]
        end
    end

    GW --> API1
    GW --> API2

    API1 --> BS1
    API2 --> BS2

    BS1 --> PG
    BS2 --> PG

    BS1 --> RD
    BS2 --> RD

    BS2 --> KF

    BS1 --> MON
    BS2 --> MON
```

### 9.4 核心价值

**💼 业务价值：**
- **提升运营效率**：智能算法优化库存分配和补货策略
- **降低运营成本**：优化拣选路径和配送路线，减少人力和物流成本
- **提高客户满意度**：快速响应订单，缩短配送时间
- **增强业务扩展性**：支持前置仓网络的快速扩张

**🔧 技术价值：**
- **架构现代化**：微服务架构支持系统的高可用和可扩展
- **算法智能化**：集成多种优化算法，提升系统智能化水平
- **数据驱动决策**：实时数据分析支持业务决策
- **安全合规性**：完善的安全机制和审计日志

通过API接口模块的完整集成，PISP系统成功扩展了前置仓管理和订单履约能力，从传统的进销存管理系统演进为现代化的智能供应链管理平台，为企业的数字化转型提供了强有力的技术支撑。前置仓管理的详细业务流程、算法实现和技术架构请参考专门的模块设计文档。

---

**注意：** 本文档为总览文档，具体的实现细节请参考各个模块的详细设计文档。

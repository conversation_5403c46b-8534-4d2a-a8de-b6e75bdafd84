# DDD-001 PISP进销存+零售管理系统详细设计总览

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-001 |
| 文档名称 | PISP进销存+零售管理系统详细设计总览 |
| 版本号 | v3.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 📋 详细设计文档导航

本文档是进销存管理系统详细设计的总览文档。具体的业务模块设计请参考以下文档：

### 🏗️ 模块化设计文档

| 文档编号 | 文档名称 | 主要内容 |
|----------|----------|----------|
| **DDD-002** | [用户管理模块](./DDD-002-用户管理模块.md) | 用户注册登录、角色权限管理、组织架构管理 |
| **DDD-003** | [基础数据管理模块](./DDD-003-基础数据管理模块.md) | 商品管理、客户管理、供应商管理、仓库管理 |
| **DDD-004** | [采购管理模块](./DDD-004-采购管理模块.md) | 采购订单、采购入库、采购退货管理 |
| **DDD-005** | [销售管理模块](./DDD-005-销售管理模块.md) | 销售订单、销售出库、销售退货管理 |
| **DDD-006** | [库存管理模块](./DDD-006-库存管理模块.md) | 库存查询、库存盘点、库存预警、库存调拨 |
| **DDD-007** | [财务管理模块](./DDD-007-财务管理模块.md) | 应收应付、成本核算、财务对账 |
| **DDD-008** | [报表分析模块](./DDD-008-报表分析模块.md) | 销售报表、采购报表、库存报表、财务报表 |
| **DDD-009** | [系统管理模块](./DDD-009-系统管理模块.md) | 系统配置、数据备份、操作日志、系统监控 |
| **DDD-010** | [零售管理模块](./DDD-010-零售管理模块.md) | POS销售、会员管理、促销活动、门店管理 |
| **DDD-011** | [前置仓管理模块](./DDD-011-前置仓管理模块.md) | 前置仓管理、库存分配、智能补货、订单履约、拣选打包配送 |

### 🎯 模块关系图

```mermaid
graph TB
    subgraph "用户权限层"
        A[用户管理模块<br/>DDD-002]
    end

    subgraph "基础数据层"
        B[基础数据管理模块<br/>DDD-003]
    end

    subgraph "业务流程层"
        C[采购管理模块<br/>DDD-004]
        D[销售管理模块<br/>DDD-005]
        E[库存管理模块<br/>DDD-006]
    end

    subgraph "财务分析层"
        F[财务管理模块<br/>DDD-007]
        G[报表分析模块<br/>DDD-008]
    end

    subgraph "系统管理层"
        H[系统管理模块<br/>DDD-009]
    end

    subgraph "零售业务层"
        I[零售管理模块<br/>DDD-010]
    end

    subgraph "前置仓业务层"
        J[前置仓管理模块<br/>DDD-011<br/>包含订单履约]
    end

    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    A --> I
    A --> J

    B --> C
    B --> D
    B --> E
    B --> I
    B --> J
    C --> E
    D --> E
    D --> J
    C --> F
    D --> F
    J --> E
    E --> F
    I --> E
    I --> F
    C --> G
    D --> G
    E --> G
    F --> G
    I --> G
```

## 1. 系统架构概述

### 1.1 整体架构设计

```mermaid
graph TB
    subgraph "前端层"
        A[Vue 3 + Element Plus]
    end

    subgraph "网关层"
        B[Apache ShenYu 2.7.0.1]
    end

    subgraph "注册中心"
        NC[Nacos Server]
    end

    subgraph "微服务层"
        C[用户管理服务<br/>user-service]
        D[基础数据服务<br/>base-data-service]
        E[采购管理服务<br/>purchase-service]
        F[销售管理服务<br/>sales-service]
        G[库存管理服务<br/>inventory-service]
        H[财务管理服务<br/>finance-service]
        I[报表分析服务<br/>report-service]
        J[系统管理服务<br/>system-service]
        K[零售管理服务<br/>retail-service]
    end

    subgraph "数据层"
        L[PostgreSQL 17]
        M[Redis 7.x]
    end

    subgraph "消息层"
        N[Apache RocketMQ 5.3.1]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    B --> I
    B --> J
    B --> K

    C --> NC
    D --> NC
    E --> NC
    F --> NC
    G --> NC
    H --> NC
    I --> NC
    J --> NC
    K --> NC

    C --> L
    D --> L
    E --> L
    F --> L
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L

    C --> M
    D --> M
    E --> M
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M

    C --> N
    D --> N
    E --> N
    F --> N
    G --> N
    H --> N
    I --> N
    J --> N
    K --> N
```

### 1.2 设计原则

1. **领域驱动设计（DDD）**：以业务领域为核心，将复杂的业务逻辑封装在领域模型中
2. **微服务架构**：按业务边界拆分服务，每个服务独立部署和扩展
3. **CQRS模式**：读写分离，优化查询性能
4. **事件驱动架构**：通过领域事件实现服务间的松耦合
5. **六边形架构**：将业务逻辑与技术实现分离

### 1.3 技术栈

| 技术组件 | 版本 | 用途 |
|----------|------|------|
| **Java** | 21 (LTS) | 开发语言 |
| **Spring Boot** | 3.4.7 | 微服务框架 |
| **Spring Cloud** | 2024.0.1 | 微服务生态 |
| **Maven** | 3.9.x | 项目构建和依赖管理 |
| **Nacos** | 3.0.2 | 注册中心和配置中心 |
| **Apache ShenYu** | 2.7.0.1 | API网关 |
| **MyBatis-Plus** | 3.5.12 | 数据访问层 |
| **PostgreSQL** | 17 | 主数据库 |
| **Redis** | 7.x | 缓存和会话存储 |
| **Apache Kafka** | 3.9.1 | 消息中间件 |
| **Vue** | 3 | 前端框架 |
| **Element Plus** | 2.4.0 | UI组件库 |

## 2. Maven多模块结构

### 2.1 项目结构设计

```
pisp-system/
├── pom.xml                           # 父POM，统一管理依赖版本
├── pisp-common/                       # 公共模块
│   ├── pisp-common-core/             # 核心工具类
│   ├── pisp-common-security/         # 安全认证
│   ├── pisp-common-redis/            # Redis配置
│   ├── pisp-common-kafka/            # Kafka配置
│   └── pisp-common-web/              # Web通用配置
├── pisp-gateway/                      # ShenYu网关
├── pisp-services/                     # 业务服务模块
│   ├── pisp-user/                    # 用户管理服务
│   ├── pisp-base-data/               # 基础数据服务
│   ├── pisp-purchase/                # 采购管理服务
│   ├── pisp-sales/                   # 销售管理服务
│   ├── pisp-inventory/               # 库存管理服务
│   ├── pisp-finance/                 # 财务管理服务
│   ├── pisp-report/                  # 报表分析服务
│   ├── pisp-system/                  # 系统管理服务
│   ├── pisp-retail/                  # 零售管理服务
│   └── pisp-front-warehouse/         # 前置仓管理服务（包含订单履约）
└── pisp-api/                         # API接口定义
    ├── pisp-api-user/
    ├── pisp-api-base-data/
    ├── pisp-api-purchase/
    ├── pisp-api-sales/
    ├── pisp-api-inventory/
    ├── pisp-api-finance/
    ├── pisp-api-report/
    ├── pisp-api-system/
    ├── pisp-api-retail/
    └── pisp-api-front-warehouse/     # 前置仓管理API（包含订单履约API）
```

### 2.2 Nacos配置管理

#### 2.2.1 服务注册配置

```yaml
# application.yml (每个微服务)
spring:
  application:
    name: pisp-base-data-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: pisp-system
        group: DEFAULT_GROUP
        metadata:
          version: 1.0.0
          region: beijing
      config:
        server-addr: 127.0.0.1:8848
        namespace: pisp-system
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: COMMON_GROUP
            refresh: true
          - data-id: datasource-config.yml
            group: COMMON_GROUP
            refresh: true
  config:
    import:
      - optional:nacos:common-config.yml
      - optional:nacos:datasource-config.yml
```

#### 2.2.2 配置文件管理

**Nacos配置中心文件结构：**
```
pisp-system (namespace)
├── COMMON_GROUP/
│   ├── common-config.yml           # 公共配置
│   ├── datasource-config.yml       # 数据源配置
│   ├── redis-config.yml            # Redis配置
│   └── kafka-config.yml            # Kafka配置
├── DEFAULT_GROUP/
│   ├── pisp-user-service.yml        # 用户管理服务配置
│   ├── pisp-base-data-service.yml   # 基础数据服务配置
│   ├── pisp-purchase-service.yml    # 采购服务配置
│   ├── pisp-sales-service.yml       # 销售服务配置
│   ├── pisp-inventory-service.yml   # 库存服务配置
│   ├── pisp-finance-service.yml     # 财务服务配置
│   ├── pisp-report-service.yml      # 报表服务配置
│   ├── pisp-system-service.yml      # 系统管理服务配置
│   └── pisp-retail-service.yml      # 零售管理服务配置
└── GATEWAY_GROUP/
    └── pisp-gateway.yml             # 网关配置
```
### 2.3 Maven依赖管理

#### 2.3.1 父POM配置

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.pisp</groupId>
    <artifactId>pisp-system</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 核心框架版本 -->
        <spring-boot.version>3.4.7</spring-boot.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>

        <!-- 数据库和缓存 -->
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <postgresql.version>42.7.3</postgresql.version>
        <redis.version>7.2.4</redis.version>

        <!-- 消息中间件 -->
        <kafka.version>3.9.1</kafka.version>

        <!-- 工具库 -->
        <lombok.version>1.18.30</lombok.version>
        <hutool.version>5.8.25</hutool.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Cloud Alibaba BOM -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>pisp-common</module>
        <module>pisp-gateway</module>
        <module>pisp-services</module>
        <module>pisp-api</module>
    </modules>
</project>
```

#### 2.3.2 微服务模块配置示例

```xml
<!-- pisp-services/pisp-user/pom.xml -->
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Spring Cloud -->
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>

    <!-- Nacos -->
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>

    <!-- 数据库 -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus.version}</version>
    </dependency>
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
    </dependency>

    <!-- Redis -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>

    <!-- Kafka -->
    <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
    </dependency>
</dependencies>
```

## 3. 业务模块概述

### 3.1 用户管理模块 (DDD-002)

**核心职责：**
- 用户注册登录：用户注册、登录认证、密码管理
- 角色权限管理：角色定义、权限分配、访问控制
- 用户信息管理：用户档案、组织架构、部门管理

**关键实体：**
- User（用户）
- Role（角色）
- Permission（权限）
- Department（部门）

**服务名称：** `pisp-user-service`

### 3.2 基础数据管理模块 (DDD-003)

**核心职责：**
- 商品信息管理：商品分类、商品档案、价格管理
- 客户信息管理：客户档案、信用管理、联系人管理
- 供应商管理：供应商档案、评级管理、合作协议
- 仓库管理：仓库设置、库位管理、仓库权限

**关键实体：**
- Product（商品）
- Customer（客户）
- Supplier（供应商）
- Warehouse（仓库）

**服务名称：** `pisp-base-data-service`

### 3.3 采购管理模块 (DDD-004)

**核心职责：**
- 采购订单管理：需求计划、询价比价、订单审批
- 采购入库管理：收货确认、质量检验、成本核算
- 采购退货管理：退货申请、退货处理、库存调整

**关键实体：**
- PurchaseOrder（采购订单）
- PurchaseReceipt（采购入库）
- PurchaseReturn（采购退货）

**服务名称：** `pisp-purchase-service`

### 3.4 销售管理模块 (DDD-005)

**核心职责：**
- 销售订单管理：客户报价、信用检查、订单审批
- 销售出库管理：发货计划、出库确认、物流跟踪
- 销售退货管理：退货申请、退货处理、库存恢复

**关键实体：**
- SalesOrder（销售订单）
- SalesShipment（销售出库）
- SalesReturn（销售退货）

**服务名称：** `pisp-sales-service`

### 3.5 库存管理模块 (DDD-006)

**核心职责：**
- 库存查询监控：实时库存、多维度统计、库存分析
- 库存盘点：定期盘点、循环盘点、差异处理
- 库存预警：低库存预警、缺货预警、超库存预警
- 库存调拨：仓库间调拨、调拨审批、调拨跟踪

**关键实体：**
- Inventory（库存）
- InventoryTransaction（库存事务）
- InventoryCheck（库存盘点）
- InventoryTransfer（库存调拨）

**服务名称：** `pisp-inventory-service`

### 3.6 财务管理模块 (DDD-007)

**核心职责：**
- 应收应付管理：应收账款、应付账款、账期管理
- 成本核算：加权平均成本、移动平均成本、成本分析
- 财务对账：自动对账、差异处理、对账报告

**关键实体：**
- AccountsReceivable（应收账款）
- AccountsPayable（应付账款）
- CostCalculation（成本核算）

**服务名称：** `pisp-finance-service`

### 3.7 报表分析模块 (DDD-008)

**核心职责：**
- 销售报表：销售汇总、趋势分析、客户分析、商品分析
- 采购报表：采购汇总、供应商分析、采购趋势
- 库存报表：库存价值、库存变动、库存周转
- 财务报表：应收应付账龄、成本分析、利润分析

**关键服务：**
- SalesReportService（销售报表）
- PurchaseReportService（采购报表）
- InventoryReportService（库存报表）
- FinancialReportService（财务报表）

**服务名称：** `pisp-report-service`

### 3.8 系统管理模块 (DDD-009)

**核心职责：**
- 系统配置管理：系统参数、业务配置、字典管理
- 数据备份恢复：定时备份、数据恢复、备份策略
- 操作日志管理：用户操作日志、系统日志、审计追踪
- 系统监控：性能监控、健康检查、告警管理

**关键实体：**
- SystemConfig（系统配置）
- DataBackup（数据备份）
- OperationLog（操作日志）
- SystemMonitor（系统监控）

**服务名称：** `pisp-system-service`

### 3.9 零售管理模块 (DDD-010)

**核心职责：**
- POS销售管理：商品扫码、价格计算、支付处理、小票打印
- 会员管理：会员注册、积分管理、等级升级、权益管理
- 促销管理：促销活动配置、优惠计算、使用统计
- 门店管理：门店信息、POS设备、营业管理

**关键实体：**
- RetailPosSale（POS销售）
- RetailMember（会员）
- RetailPromotion（促销活动）
- RetailStore（门店）

**服务名称：** `pisp-retail-service`

## 4. 微服务设计模式

### 4.1 服务注册与发现

```java
// 服务提供者配置
@SpringBootApplication
@EnableDiscoveryClient
public class BaseDataServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(BaseDataServiceApplication.class, args);
    }
}

// 服务消费者配置
@RestController
public class SalesController {

    @Autowired
    private BaseDataServiceClient baseDataServiceClient;

    @GetMapping("/products/{id}")
    public ProductDTO getProduct(@PathVariable Long id) {
        return baseDataServiceClient.getProduct(id);
    }
}
```

### 4.2 Feign客户端配置

```java
// 服务间调用接口
@FeignClient(name = "pisp-base-data-service", path = "/api/v1")
public interface BaseDataServiceClient {

    @GetMapping("/products/{id}")
    ProductDTO getProduct(@PathVariable("id") Long id);

    @GetMapping("/customers/{id}")
    CustomerDTO getCustomer(@PathVariable("id") Long id);

    @GetMapping("/suppliers/{id}")
    SupplierDTO getSupplier(@PathVariable("id") Long id);
}
```

### 4.3 配置管理

```java
// 配置类示例
@Component
@RefreshScope
@ConfigurationProperties(prefix = "pisp.business")
public class BusinessConfig {

    private String defaultCurrency = "CNY";
    private Integer orderTimeoutDays = 30;
    private BigDecimal taxRate = new BigDecimal("0.13");

    // getters and setters
}
```

### 4.4 事件驱动架构

```java
// 领域事件发布
@Service
public class SalesOrderService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    public void approveSalesOrder(Long orderId, Long approverId) {
        // 业务逻辑处理
        SalesOrder order = salesOrderRepository.findById(orderId);
        order.approve(approverId);
        salesOrderRepository.save(order);

        // 发布领域事件
        SalesOrderApprovedEvent event = new SalesOrderApprovedEvent(
            orderId, approverId, LocalDateTime.now()
        );

        // 本地事件
        eventPublisher.publishEvent(event);

        // Kafka事件
        kafkaTemplate.send("sales.events", "order.approved", event);
    }
}
```

### 4.5 分布式事务处理

```java
// Seata分布式事务示例
@Service
public class OrderProcessService {

    @Autowired
    private SalesOrderService salesOrderService;

    @Autowired
    private InventoryServiceClient inventoryServiceClient;

    @Autowired
    private FinanceServiceClient financeServiceClient;

    @GlobalTransactional(rollbackFor = Exception.class)
    public void processOrder(CreateOrderRequest request) {
        // 1. 创建销售订单
        SalesOrderDTO order = salesOrderService.createOrder(request);

        // 2. 预留库存
        inventoryServiceClient.reserveInventory(request.getOrderItems());

        // 3. 占用客户信用额度
        financeServiceClient.occupyCredit(request.getCustomerId(), order.getTotalAmount());
    }
}
```

## 5. 开发指南

### 5.1 微服务开发顺序

1. **第一阶段**：基础设施搭建
   - Nacos注册中心部署
   - ShenYu网关配置
   - 公共模块开发（pisp-common）

2. **第二阶段**：用户管理服务（pisp-user-service）
   - 用户注册登录、角色权限管理
   - 为其他服务提供认证授权支撑

3. **第三阶段**：基础数据服务（pisp-base-data-service）
   - 商品、客户、供应商、仓库管理
   - 为其他服务提供基础数据支撑

4. **第四阶段**：核心业务服务
   - 采购管理服务（pisp-purchase-service）
   - 销售管理服务（pisp-sales-service）
   - 库存管理服务（pisp-inventory-service）

5. **第五阶段**：财务服务（pisp-finance-service）
   - 应收应付管理
   - 成本核算

6. **第六阶段**：分析和管理服务
   - 报表分析服务（pisp-report-service）
   - 系统管理服务（pisp-system-service）

7. **第七阶段**：零售业务服务
   - 零售管理服务（pisp-retail-service）

### 5.2 团队分工建议

| 团队 | 负责服务 | 主要职责 |
|------|----------|----------|
| **基础架构团队** | pisp-common, pisp-gateway | 公共组件、网关配置、基础设施 |
| **用户管理团队** | pisp-user-service | 用户注册登录、角色权限、组织架构 |
| **基础数据团队** | pisp-base-data-service | 商品、客户、供应商、仓库管理 |
| **采购团队** | pisp-purchase-service | 采购订单、入库、退货流程 |
| **销售团队** | pisp-sales-service | 销售订单、出库、退货流程 |
| **库存团队** | pisp-inventory-service | 库存查询、盘点、预警、调拨 |
| **财务团队** | pisp-finance-service | 应收应付、成本核算、对账 |
| **数据分析团队** | pisp-report-service | 各类业务报表和分析 |
| **系统管理团队** | pisp-system-service | 系统配置、数据备份、日志管理、监控 |
| **零售业务团队** | pisp-retail-service | POS销售、会员管理、促销活动、门店管理 |

### 5.3 环境配置指南

#### 5.3.1 开发环境
- **JDK**：OpenJDK 21 (LTS)
- **Maven**：3.9.x
- **IDE**：IntelliJ IDEA 2024.x
- **Spring Boot**：3.4.7
- **Spring Cloud**：2024.0.1
- **Nacos**：3.0.2 (单机模式)
- **PostgreSQL**：17.x (本地数据库)
- **Redis**：7.x (单机模式)
- **Kafka**：3.9.1 (单机模式)

#### 5.3.2 测试环境
- **Nacos**：3.0.2 集群模式（3节点）
- **PostgreSQL**：17.x 主从模式
- **Redis**：7.x 哨兵模式
- **Kafka**：3.9.1 集群模式（3节点）
- **ShenYu**：2.7.0.1 网关集群

#### 5.3.3 生产环境
- **Nacos**：3.0.2 集群模式（3节点）
- **PostgreSQL**：17.x 主从+读写分离
- **Redis**：7.x 集群模式
- **Kafka**：3.9.1 集群模式（3节点）
- **负载均衡**：Nginx + ShenYu网关集群
- **监控**：Prometheus + Grafana + Micrometer

### 5.4 版本兼容性说明

#### 5.4.1 核心依赖兼容性矩阵

| 组件 | 版本 | 兼容性验证 | 说明 |
|------|------|------------|------|
| **Java** | 21 (LTS) | ✅ 官方支持 | 长期支持版本 |
| **Spring Boot** | 3.4.7 | ✅ 最新稳定版 | 2024年12月发布 |
| **Spring Cloud** | 2024.0.1 | ✅ 官方兼容 | Moorgate版本 |
| **Nacos** | 3.0.2 | ✅ 完全支持 | 支持Spring Boot 3.4.x |
| **Kafka** | 3.9.1 | ✅ 原生支持 | 2024年最新稳定版 |
| **PostgreSQL** | 17.x | ✅ JDBC兼容 | 最新主版本 |
| **Redis** | 7.x | ✅ 客户端兼容 | Lettuce连接池 |

#### 5.4.2 升级路径建议

1. **从旧版本升级**：
   - Spring Boot 3.2.x → 3.4.7：平滑升级
   - Spring Cloud 2023.x → 2024.0.1：配置调整
   - Nacos 2.x → 3.0.2：数据迁移

2. **依赖冲突解决**：
   - 使用BOM统一版本管理
   - 排除传递依赖冲突
   - 版本锁定策略

## 5. 系统设计图表

### 5.1 核心领域类图

```mermaid
classDiagram
    class BaseEntity {
        +Long id
        +LocalDateTime createTime
        +LocalDateTime updateTime
        +Long creatorId
        +Long updaterId
        +String additionalInfo
        +String remark
        +Integer version
    }

    class Product {
        +String productCode
        +String productName
        +Long categoryId
        +String unit
        +BigDecimal costPrice
        +BigDecimal salePrice
        +ProductStatus status
        +updatePrice()
        +activate()
        +deactivate()
    }

    class Customer {
        +String customerCode
        +String customerName
        +CustomerType customerType
        +BigDecimal creditLimit
        +BigDecimal creditUsed
        +occupyCredit()
        +releaseCredit()
        +hasAvailableCredit()
    }

    class SalesOrder {
        +String orderNumber
        +Long customerId
        +SalesOrderStatus status
        +BigDecimal totalAmount
        +BigDecimal finalAmount
        +approve()
        +cancel()
        +addOrderItem()
    }

    class SalesOrderItem {
        +Long salesOrderId
        +Long productId
        +BigDecimal quantity
        +BigDecimal unitPrice
        +BigDecimal totalAmount
        +updateQuantity()
        +updatePrice()
    }

    class Inventory {
        +Long productId
        +Long warehouseId
        +BigDecimal availableQuantity
        +BigDecimal reservedQuantity
        +BigDecimal totalQuantity
        +adjustQuantity()
        +isLowStock()
        +isOverStock()
    }

    BaseEntity <|-- Product
    BaseEntity <|-- Customer
    BaseEntity <|-- SalesOrder
    BaseEntity <|-- SalesOrderItem
    BaseEntity <|-- Inventory

    SalesOrder o-- SalesOrderItem
    Customer o-- SalesOrder
    Product --o SalesOrderItem
    Product --o Inventory
```

### 5.2 基础实体类定义

所有业务实体都继承自BaseEntity，提供统一的基础字段和功能：

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public abstract class BaseEntity {

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    @TableField(value = "updater_id", fill = FieldFill.INSERT_UPDATE)
    private Long updaterId;

    @TableField(value = "additional_info")
    private String additionalInfo; // JSON格式，保存额外信息

    @TableField(value = "remark")
    private String remark; // 备注信息
}
```

BaseEntity提供了以下统一功能：

- **自动时间戳**：创建时间和更新时间自动填充
- **操作人记录**：记录创建人和更新人
- **扩展信息**：additionalInfo字段支持JSON格式的额外信息存储
- **备注支持**：remark字段用于存储备注信息
- **MyBatis-Plus集成**：使用注解配置自动填充策略

### 5.3 销售订单处理时序图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as ShenYu网关
    participant S as 销售服务
    participant I as 库存服务
    participant F as 财务服务
    participant K as Kafka
    participant D as 数据库

    C->>G: 创建销售订单请求
    G->>S: 转发请求

    S->>D: 验证客户信息
    D-->>S: 返回客户信息

    S->>F: 检查客户信用额度
    F-->>S: 返回信用检查结果

    S->>I: 检查库存可用性
    I-->>S: 返回库存检查结果

    S->>D: 创建销售订单
    D-->>S: 返回订单ID

    S->>K: 发布订单创建事件
    K-->>I: 订单创建事件
    K-->>F: 订单创建事件

    I->>D: 预留库存
    F->>D: 占用信用额度

    S-->>G: 返回创建结果
    G-->>C: 返回响应

    Note over S,K: 异步事件处理
    K->>S: 库存预留完成事件
    K->>S: 信用占用完成事件
```

### 5.3 库存管理交互图

```mermaid
graph TB
    subgraph "库存管理交互流程"
        A[库存查询请求] --> B{库存服务}
        B --> C[实时库存计算]
        C --> D[缓存检查]
        D --> E{缓存命中?}
        E -->|是| F[返回缓存数据]
        E -->|否| G[查询数据库]
        G --> H[计算库存数据]
        H --> I[更新缓存]
        I --> J[返回库存信息]

        K[库存变动事件] --> L[Kafka消息]
        L --> M[库存事务处理]
        M --> N[更新库存表]
        N --> O[清除相关缓存]
        O --> P[发布库存更新事件]

        Q[库存预警检查] --> R[定时任务]
        R --> S[扫描低库存商品]
        S --> T{是否需要预警?}
        T -->|是| U[创建预警记录]
        T -->|否| V[继续扫描]
        U --> W[发送预警通知]
        W --> X[Kafka预警事件]
    end
```

### 5.4 零售管理交互图

```mermaid
graph TB
    subgraph "POS销售交互流程"
        A[POS扫码] --> B{商品服务}
        B --> C[获取商品信息]
        C --> D[显示商品价格]

        E[会员识别] --> F{会员服务}
        F --> G[查询会员信息]
        G --> H[显示会员等级]

        I[促销计算] --> J{促销服务}
        J --> K[匹配促销规则]
        K --> L[计算优惠金额]
        L --> M[应用会员折扣]

        N[支付处理] --> O{支付服务}
        O --> P[处理支付]
        P --> Q{支付成功?}
        Q -->|是| R[库存扣减]
        Q -->|否| S[支付失败]

        R --> T[更新会员积分]
        T --> U[记录财务数据]
        U --> V[打印小票]
        V --> W[发布销售事件]
    end

    subgraph "会员管理交互流程"
        X[会员注册] --> Y[验证手机号]
        Y --> Z[生成会员号]
        Z --> AA[赠送积分]
        AA --> BB[发送欢迎短信]

        CC[积分使用] --> DD[验证积分余额]
        DD --> EE[扣减积分]
        EE --> FF[记录积分日志]

        GG[等级升级] --> HH[检查消费金额]
        HH --> II[自动升级等级]
        II --> JJ[更新会员权益]
    end

    subgraph "促销管理交互流程"
        KK[促销创建] --> LL[设置促销规则]
        LL --> MM[配置适用范围]
        MM --> NN[激活促销活动]

        OO[促销应用] --> PP[匹配促销条件]
        PP --> QQ[计算优惠金额]
        QQ --> RR[更新使用次数]
        RR --> SS[记录促销效果]
    end
```

### 5.5 事件驱动架构图

```mermaid
graph LR
    subgraph "事件生产者"
        A[销售服务]
        B[采购服务]
        C[库存服务]
        D[财务服务]
        E[零售服务]
    end

    subgraph "Kafka集群"
        F[sales-events]
        G[purchase-events]
        H[inventory-events]
        I[finance-events]
        J[retail-events]
        K[notification-events]
    end

    subgraph "事件消费者"
        L[库存更新处理器]
        M[财务处理器]
        N[通知处理器]
        O[报表处理器]
        P[审计处理器]
        Q[会员积分处理器]
    end

    A --> F
    A --> K
    B --> G
    B --> K
    C --> H
    C --> K
    D --> I
    D --> K
    E --> J
    E --> K
    D --> H
    D --> I

    E --> J
    E --> K
    E --> M
    F --> J
    F --> K
    F --> M
    G --> L
    G --> M
    H --> L
    H --> M
    I --> L

    E --> N
    F --> N
    G --> N
    H --> N
```

### 5.5 微服务部署架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx负载均衡器]
    end

    subgraph "网关层"
        GW1[ShenYu网关-1]
        GW2[ShenYu网关-2]
    end

    subgraph "注册中心集群"
        NC1[Nacos-1]
        NC2[Nacos-2]
        NC3[Nacos-3]
    end

    subgraph "微服务集群"
        subgraph "基础数据服务"
            BD1[base-data-service-1]
            BD2[base-data-service-2]
        end

        subgraph "销售服务"
            S1[sales-service-1]
            S2[sales-service-2]
        end

        subgraph "库存服务"
            I1[inventory-service-1]
            I2[inventory-service-2]
        end

        subgraph "采购服务"
            P1[purchase-service-1]
            P2[purchase-service-2]
        end

        subgraph "财务服务"
            F1[finance-service-1]
            F2[finance-service-2]
        end

        subgraph "报表服务"
            R1[report-service-1]
            R2[report-service-2]
        end

        subgraph "零售服务"
            RT1[retail-service-1]
            RT2[retail-service-2]
        end
    end

    subgraph "中间件层"
        subgraph "Kafka集群"
            K1[Kafka-1]
            K2[Kafka-2]
            K3[Kafka-3]
        end

        subgraph "Redis集群"
            RD1[Redis-Master]
            RD2[Redis-Slave]
        end
    end

    subgraph "数据层"
        subgraph "PostgreSQL集群"
            DB1[PostgreSQL-Master]
            DB2[PostgreSQL-Slave]
        end
    end

    LB --> GW1
    LB --> GW2

    GW1 --> BD1
    GW1 --> S1
    GW1 --> I1
    GW1 --> P1
    GW1 --> F1
    GW1 --> R1
    GW1 --> RT1

    GW2 --> BD2
    GW2 --> S2
    GW2 --> I2
    GW2 --> P2
    GW2 --> F2
    GW2 --> R2
    GW2 --> RT2

    BD1 --> NC1
    BD2 --> NC2
    S1 --> NC1
    S2 --> NC2
    I1 --> NC3
    I2 --> NC1
    P1 --> NC2
    P2 --> NC3
    F1 --> NC1
    F2 --> NC2
    R1 --> NC3
    R2 --> NC1

    BD1 --> RD1
    BD2 --> RD1
    S1 --> RD1
    S2 --> RD1
    I1 --> RD1
    I2 --> RD1
    P1 --> RD1
    P2 --> RD1
    F1 --> RD1
    F2 --> RD1
    R1 --> RD1
    R2 --> RD1

    BD1 --> DB1
    BD2 --> DB1
    S1 --> DB1
    S2 --> DB1
    I1 --> DB1
    I2 --> DB1
    P1 --> DB1
    P2 --> DB1
    F1 --> DB1
    F2 --> DB1
    R1 --> DB1
    R2 --> DB1

    DB1 --> DB2
    RD1 --> RD2
    NC1 --> NC2
    NC2 --> NC3
    NC3 --> NC1
```

## 6. 新增API接口模块设计

### 6.1 前置仓管理API模块 (pisp-api-front-warehouse)

#### 6.1.1 模块概述

**技术栈：** Spring Boot 3.4.7 + OpenAPI 3.0 + Bean Validation

**职责：** 定义前置仓管理相关的完整API接口，包括前置仓管理、库存分配、智能补货、订单履约、拣选打包、配送调度等功能的接口定义

**核心功能：**
- 前置仓信息管理API
- 前置仓库存管理API
- 库存分配策略API
- 智能补货API
- 覆盖区域管理API
- 订单履约管理API
- 拣选任务管理API
- 打包任务管理API
- 配送任务管理API

#### 6.1.2 Maven模块结构

```xml
<!-- pisp-api-front-warehouse/pom.xml -->
<project>
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pisp</groupId>
        <artifactId>pisp-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>pisp-api-front-warehouse</artifactId>
    <name>PISP :: API :: Front Warehouse</name>
    <description>前置仓管理API接口定义</description>

    <dependencies>
        <dependency>
            <groupId>com.pisp</groupId>
            <artifactId>pisp-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>
</project>
```

#### 6.1.3 核心API接口定义

**前置仓管理API：**
```java
@RestController
@RequestMapping("/api/v1/front-warehouses")
@Tag(name = "前置仓管理", description = "前置仓信息管理相关接口")
@Validated
public interface FrontWarehouseController {

    @Operation(summary = "创建前置仓", description = "创建新的前置仓")
    @PostMapping
    ResponseEntity<ApiResponse<FrontWarehouseVO>> createFrontWarehouse(
            @Valid @RequestBody CreateFrontWarehouseRequest request);

    @Operation(summary = "更新前置仓信息", description = "更新前置仓基本信息")
    @PutMapping("/{id}")
    ResponseEntity<ApiResponse<FrontWarehouseVO>> updateFrontWarehouse(
            @PathVariable Long id,
            @Valid @RequestBody UpdateFrontWarehouseRequest request);

    @Operation(summary = "查询前置仓详情", description = "根据ID查询前置仓详细信息")
    @GetMapping("/{id}")
    ResponseEntity<ApiResponse<FrontWarehouseVO>> getFrontWarehouse(
            @PathVariable Long id);

    @Operation(summary = "分页查询前置仓", description = "分页查询前置仓列表")
    @GetMapping
    ResponseEntity<ApiResponse<PageResult<FrontWarehouseVO>>> getFrontWarehouses(
            @Valid FrontWarehouseQueryRequest request);

    @Operation(summary = "删除前置仓", description = "删除前置仓（需要二次确认）")
    @DeleteMapping("/{id}")
    ResponseEntity<ApiResponse<Void>> deleteFrontWarehouse(
            @PathVariable Long id,
            @RequestParam String confirmToken);

    @Operation(summary = "启用/禁用前置仓", description = "启用或禁用前置仓")
    @PutMapping("/{id}/status")
    ResponseEntity<ApiResponse<Void>> updateFrontWarehouseStatus(
            @PathVariable Long id,
            @Valid @RequestBody UpdateStatusRequest request);
}
```

**前置仓库存管理API：**
```java
@RestController
@RequestMapping("/api/v1/front-warehouses/{warehouseId}/inventory")
@Tag(name = "前置仓库存管理", description = "前置仓库存管理相关接口")
@Validated
public interface FrontWarehouseInventoryController {

    @Operation(summary = "查询前置仓库存", description = "查询指定前置仓的库存信息")
    @GetMapping
    ResponseEntity<ApiResponse<PageResult<FrontWarehouseInventoryVO>>> getInventory(
            @PathVariable Long warehouseId,
            @Valid InventoryQueryRequest request);

    @Operation(summary = "调整库存", description = "手动调整前置仓库存")
    @PostMapping("/adjust")
    ResponseEntity<ApiResponse<Void>> adjustInventory(
            @PathVariable Long warehouseId,
            @Valid @RequestBody InventoryAdjustRequest request);

    @Operation(summary = "库存分配", description = "为订单分配库存")
    @PostMapping("/allocate")
    ResponseEntity<ApiResponse<InventoryAllocationVO>> allocateInventory(
            @PathVariable Long warehouseId,
            @Valid @RequestBody InventoryAllocationRequest request);

    @Operation(summary = "释放库存", description = "释放已分配的库存")
    @PostMapping("/release")
    ResponseEntity<ApiResponse<Void>> releaseInventory(
            @PathVariable Long warehouseId,
            @Valid @RequestBody InventoryReleaseRequest request);

    @Operation(summary = "库存预警查询", description = "查询库存预警信息")
    @GetMapping("/alerts")
    ResponseEntity<ApiResponse<List<InventoryAlertVO>>> getInventoryAlerts(
            @PathVariable Long warehouseId);
}
```

**智能补货API：**
```java
@RestController
@RequestMapping("/api/v1/front-warehouses/{warehouseId}/replenishment")
@Tag(name = "智能补货管理", description = "前置仓智能补货相关接口")
@Validated
public interface ReplenishmentController {

    @Operation(summary = "创建补货订单", description = "创建前置仓补货订单")
    @PostMapping("/orders")
    ResponseEntity<ApiResponse<ReplenishmentOrderVO>> createReplenishmentOrder(
            @PathVariable Long warehouseId,
            @Valid @RequestBody CreateReplenishmentOrderRequest request);

    @Operation(summary = "智能补货建议", description = "基于算法生成补货建议")
    @PostMapping("/suggestions")
    ResponseEntity<ApiResponse<List<ReplenishmentSuggestionVO>>> getReplenishmentSuggestions(
            @PathVariable Long warehouseId,
            @Valid @RequestBody ReplenishmentSuggestionRequest request);

    @Operation(summary = "补货策略管理", description = "管理前置仓补货策略")
    @GetMapping("/strategies")
    ResponseEntity<ApiResponse<List<ReplenishmentStrategyVO>>> getReplenishmentStrategies(
            @PathVariable Long warehouseId);

    @Operation(summary = "更新补货策略", description = "更新前置仓补货策略")
    @PutMapping("/strategies/{strategyId}")
    ResponseEntity<ApiResponse<ReplenishmentStrategyVO>> updateReplenishmentStrategy(
            @PathVariable Long warehouseId,
            @PathVariable Long strategyId,
            @Valid @RequestBody UpdateReplenishmentStrategyRequest request);

    @Operation(summary = "补货订单查询", description = "查询补货订单列表")
    @GetMapping("/orders")
    ResponseEntity<ApiResponse<PageResult<ReplenishmentOrderVO>>> getReplenishmentOrders(
            @PathVariable Long warehouseId,
            @Valid ReplenishmentOrderQueryRequest request);
}
```

### 6.2 网关路由配置

#### 6.2.1 ShenYu网关路由规则

**前置仓管理服务路由：**
```yaml
# ShenYu Admin配置
shenyu:
  routes:
    - id: pisp-front-warehouse-route
      uri: lb://pisp-front-warehouse-service
      predicates:
        - Path=/api/v1/front-warehouses/**
      filters:
        - name: StripPrefix
          args:
            parts: 0
        - name: RequestRateLimiter
          args:
            redis-rate-limiter.replenishRate: 100
            redis-rate-limiter.burstCapacity: 200
        - name: CircuitBreaker
          args:
            name: front-warehouse-cb
            fallbackUri: forward:/fallback/front-warehouse
```

**订单履约服务路由：**
```yaml
shenyu:
  routes:
    - id: pisp-order-fulfillment-route
      uri: lb://pisp-order-fulfillment-service
      predicates:
        - Path=/api/v1/order-assignments/**,/api/v1/picking-tasks/**,/api/v1/packing-tasks/**,/api/v1/delivery-tasks/**
      filters:
        - name: StripPrefix
          args:
            parts: 0
        - name: RequestRateLimiter
          args:
            redis-rate-limiter.replenishRate: 200
            redis-rate-limiter.burstCapacity: 400
        - name: CircuitBreaker
          args:
            name: order-fulfillment-cb
            fallbackUri: forward:/fallback/order-fulfillment
```

#### 6.2.2 认证鉴权配置

**JWT认证插件配置：**
```yaml
shenyu:
  plugin:
    jwt:
      enabled: true
      secretKey: "pisp-jwt-secret-key-2024"
      expiredTime: 86400000
      # 前置仓管理需要WAREHOUSE_MANAGER权限
      rules:
        - path: /api/v1/front-warehouses/**
          requiredRoles: ["WAREHOUSE_MANAGER", "ADMIN"]
        # 订单履约需要FULFILLMENT_OPERATOR权限
        - path: /api/v1/order-assignments/**
          requiredRoles: ["FULFILLMENT_OPERATOR", "ADMIN"]
        - path: /api/v1/picking-tasks/**
          requiredRoles: ["PICKER", "FULFILLMENT_OPERATOR", "ADMIN"]
        - path: /api/v1/packing-tasks/**
          requiredRoles: ["PACKER", "FULFILLMENT_OPERATOR", "ADMIN"]
        - path: /api/v1/delivery-tasks/**
          requiredRoles: ["DRIVER", "FULFILLMENT_OPERATOR", "ADMIN"]
```

### 6.3 数据传输对象(DTO)设计

#### 6.3.1 前置仓管理DTO

**前置仓信息VO：**
```java
@Data
@Schema(description = "前置仓信息视图对象")
public class FrontWarehouseVO {

    @Schema(description = "前置仓ID")
    private Long id;

    @Schema(description = "前置仓编码")
    private String warehouseCode;

    @Schema(description = "前置仓名称")
    private String warehouseName;

    @Schema(description = "所属主仓库ID")
    private Long parentWarehouseId;

    @Schema(description = "所属主仓库名称")
    private String parentWarehouseName;

    @Schema(description = "地址信息")
    private String address;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "服务半径(公里)")
    private BigDecimal serviceRadius;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "营业时间")
    private String operatingHours;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
```

**创建前置仓请求DTO：**
```java
@Data
@Schema(description = "创建前置仓请求")
public class CreateFrontWarehouseRequest {

    @NotBlank(message = "前置仓编码不能为空")
    @Schema(description = "前置仓编码")
    private String warehouseCode;

    @NotBlank(message = "前置仓名称不能为空")
    @Schema(description = "前置仓名称")
    private String warehouseName;

    @NotNull(message = "所属主仓库ID不能为空")
    @Schema(description = "所属主仓库ID")
    private Long parentWarehouseId;

    @NotBlank(message = "地址不能为空")
    @Schema(description = "地址信息")
    private String address;

    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度范围错误")
    @DecimalMax(value = "180.0", message = "经度范围错误")
    @Schema(description = "经度")
    private BigDecimal longitude;

    @NotNull(message = "纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度范围错误")
    @DecimalMax(value = "90.0", message = "纬度范围错误")
    @Schema(description = "纬度")
    private BigDecimal latitude;

    @NotNull(message = "服务半径不能为空")
    @DecimalMin(value = "0.1", message = "服务半径必须大于0.1公里")
    @DecimalMax(value = "50.0", message = "服务半径不能超过50公里")
    @Schema(description = "服务半径(公里)")
    private BigDecimal serviceRadius;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "营业时间")
    private String operatingHours;

    @Schema(description = "备注")
    private String remarks;
}
```

#### 6.3.2 订单履约DTO

**订单分配VO：**
```java
@Data
@Schema(description = "订单分配视图对象")
public class OrderAssignmentVO {

    @Schema(description = "分配ID")
    private Long id;

    @Schema(description = "分配编号")
    private String assignmentNumber;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单编号")
    private String orderNumber;

    @Schema(description = "前置仓ID")
    private Long frontWarehouseId;

    @Schema(description = "前置仓名称")
    private String frontWarehouseName;

    @Schema(description = "分配类型")
    private String assignmentType;

    @Schema(description = "分配策略")
    private String assignmentStrategy;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "预计配送时间")
    private LocalDateTime estimatedDeliveryTime;

    @Schema(description = "分配原因")
    private String assignmentReason;

    @Schema(description = "距离(公里)")
    private BigDecimal distanceKm;

    @Schema(description = "预计成本")
    private BigDecimal estimatedCost;

    @Schema(description = "分配时间")
    private LocalDateTime assignedAt;

    @Schema(description = "确认时间")
    private LocalDateTime confirmedAt;
}
```

### 6.4 高危操作二次确认机制

#### 6.4.1 确认令牌生成

**确认令牌服务：**
```java
@Service
@Slf4j
public class ConfirmationTokenService {

    private final RedisTemplate<String, String> redisTemplate;
    private final static String TOKEN_PREFIX = "confirm_token:";
    private final static int TOKEN_EXPIRE_MINUTES = 5;

    /**
     * 生成确认令牌
     */
    public String generateConfirmToken(String operation, Long resourceId, Long userId) {
        String tokenId = UUID.randomUUID().toString();
        String tokenKey = TOKEN_PREFIX + tokenId;

        ConfirmationTokenInfo tokenInfo = ConfirmationTokenInfo.builder()
                .operation(operation)
                .resourceId(resourceId)
                .userId(userId)
                .createTime(LocalDateTime.now())
                .build();

        redisTemplate.opsForValue().set(tokenKey,
                JSON.toJSONString(tokenInfo),
                TOKEN_EXPIRE_MINUTES,
                TimeUnit.MINUTES);

        log.info("生成确认令牌: operation={}, resourceId={}, userId={}, token={}",
                operation, resourceId, userId, tokenId);

        return tokenId;
    }

    /**
     * 验证确认令牌
     */
    public boolean validateConfirmToken(String token, String operation, Long resourceId, Long userId) {
        String tokenKey = TOKEN_PREFIX + token;
        String tokenInfoJson = redisTemplate.opsForValue().get(tokenKey);

        if (StringUtils.isBlank(tokenInfoJson)) {
            log.warn("确认令牌不存在或已过期: token={}", token);
            return false;
        }

        ConfirmationTokenInfo tokenInfo = JSON.parseObject(tokenInfoJson, ConfirmationTokenInfo.class);

        boolean isValid = Objects.equals(tokenInfo.getOperation(), operation) &&
                         Objects.equals(tokenInfo.getResourceId(), resourceId) &&
                         Objects.equals(tokenInfo.getUserId(), userId);

        if (isValid) {
            // 验证成功后删除令牌（一次性使用）
            redisTemplate.delete(tokenKey);
            log.info("确认令牌验证成功: operation={}, resourceId={}, userId={}",
                    operation, resourceId, userId);
        } else {
            log.warn("确认令牌验证失败: operation={}, resourceId={}, userId={}",
                    operation, resourceId, userId);
        }

        return isValid;
    }
}
```

#### 6.4.2 高危操作注解

**高危操作注解：**
```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireConfirmation {

    /**
     * 操作类型
     */
    String operation();

    /**
     * 资源ID参数名
     */
    String resourceIdParam() default "id";

    /**
     * 确认令牌参数名
     */
    String confirmTokenParam() default "confirmToken";

    /**
     * 操作描述
     */
    String description() default "";
}
```

**高危操作切面：**
```java
@Aspect
@Component
@Slf4j
public class ConfirmationAspect {

    private final ConfirmationTokenService confirmationTokenService;
    private final UserContextService userContextService;

    @Around("@annotation(requireConfirmation)")
    public Object around(ProceedingJoinPoint joinPoint, RequireConfirmation requireConfirmation) throws Throwable {

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        String[] paramNames = getParameterNames(joinPoint);

        // 提取资源ID和确认令牌
        Long resourceId = extractResourceId(args, paramNames, requireConfirmation.resourceIdParam());
        String confirmToken = extractConfirmToken(args, paramNames, requireConfirmation.confirmTokenParam());

        // 获取当前用户ID
        Long userId = userContextService.getCurrentUserId();

        // 验证确认令牌
        if (!confirmationTokenService.validateConfirmToken(confirmToken,
                requireConfirmation.operation(), resourceId, userId)) {
            throw new BusinessException(ErrorCode.CONFIRMATION_TOKEN_INVALID,
                    "确认令牌无效或已过期，请重新获取确认令牌");
        }

        // 记录高危操作日志
        log.warn("执行高危操作: operation={}, resourceId={}, userId={}, description={}",
                requireConfirmation.operation(), resourceId, userId, requireConfirmation.description());

        return joinPoint.proceed();
    }

    private Long extractResourceId(Object[] args, String[] paramNames, String paramName) {
        for (int i = 0; i < paramNames.length; i++) {
            if (paramName.equals(paramNames[i])) {
                return (Long) args[i];
            }
        }
        throw new BusinessException(ErrorCode.PARAMETER_ERROR, "未找到资源ID参数: " + paramName);
    }

    private String extractConfirmToken(Object[] args, String[] paramNames, String paramName) {
        for (int i = 0; i < paramNames.length; i++) {
            if (paramName.equals(paramNames[i])) {
                return (String) args[i];
            }
        }
        throw new BusinessException(ErrorCode.PARAMETER_ERROR, "未找到确认令牌参数: " + paramName);
    }
}
```

#### 6.4.3 高危操作应用示例

**前置仓删除接口：**
```java
@DeleteMapping("/{id}")
@RequireConfirmation(
    operation = "DELETE_FRONT_WAREHOUSE",
    resourceIdParam = "id",
    confirmTokenParam = "confirmToken",
    description = "删除前置仓"
)
@Operation(summary = "删除前置仓", description = "删除前置仓（需要二次确认）")
public ResponseEntity<ApiResponse<Void>> deleteFrontWarehouse(
        @PathVariable Long id,
        @RequestParam String confirmToken) {

    frontWarehouseService.deleteFrontWarehouse(id);
    return ResponseEntity.ok(ApiResponse.success());
}
```

**获取删除确认令牌接口：**
```java
@PostMapping("/{id}/delete-token")
@Operation(summary = "获取删除确认令牌", description = "获取删除前置仓的确认令牌")
public ResponseEntity<ApiResponse<String>> getDeleteConfirmToken(@PathVariable Long id) {

    Long userId = userContextService.getCurrentUserId();
    String token = confirmationTokenService.generateConfirmToken("DELETE_FRONT_WAREHOUSE", id, userId);

    return ResponseEntity.ok(ApiResponse.success(token));
}
```

### 6.5 API接口模块部署配置

#### 6.5.1 Maven父项目配置更新

**更新父项目pom.xml：**
```xml
<!-- pisp-parent/pom.xml -->
<modules>
    <!-- 现有模块 -->
    <module>pisp-common</module>
    <module>pisp-gateway</module>

    <!-- API接口模块 -->
    <module>pisp-api/pisp-api-user</module>
    <module>pisp-api/pisp-api-base-data</module>
    <module>pisp-api/pisp-api-purchase</module>
    <module>pisp-api/pisp-api-sales</module>
    <module>pisp-api/pisp-api-inventory</module>
    <module>pisp-api/pisp-api-finance</module>
    <module>pisp-api/pisp-api-report</module>
    <module>pisp-api/pisp-api-system</module>
    <module>pisp-api/pisp-api-retail</module>
    <!-- 新增API模块 -->
    <module>pisp-api/pisp-api-front-warehouse</module>
    <module>pisp-api/pisp-api-order-fulfillment</module>

    <!-- 业务服务模块 -->
    <module>pisp-services/pisp-user-service</module>
    <module>pisp-services/pisp-base-data-service</module>
    <module>pisp-services/pisp-purchase-service</module>
    <module>pisp-services/pisp-sales-service</module>
    <module>pisp-services/pisp-inventory-service</module>
    <module>pisp-services/pisp-finance-service</module>
    <module>pisp-services/pisp-report-service</module>
    <module>pisp-services/pisp-system-service</module>
    <module>pisp-services/pisp-retail-service</module>
    <!-- 新增业务服务模块 -->
    <module>pisp-services/pisp-front-warehouse-service</module>
    <module>pisp-services/pisp-order-fulfillment-service</module>
    <module>pisp-services/pisp-picking-service</module>
    <module>pisp-services/pisp-packing-service</module>
    <module>pisp-services/pisp-delivery-service</module>
</modules>
```

#### 6.5.2 Docker Compose配置更新

**更新docker-compose.yml：**
```yaml
version: '3.8'
services:
  # 现有服务...

  # 前置仓管理服务
  pisp-front-warehouse-service:
    build: ./pisp-services/pisp-front-warehouse-service
    container_name: pisp-front-warehouse-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: pisp
      SPRING_DATASOURCE_PASSWORD: pisp123
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
    ports:
      - "8010:8010"
    depends_on:
      - postgres
      - nacos
      - redis
    restart: unless-stopped
    networks:
      - pisp-network

  # 订单履约服务
  pisp-order-fulfillment-service:
    build: ./pisp-services/pisp-order-fulfillment-service
    container_name: pisp-order-fulfillment-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: pisp
      SPRING_DATASOURCE_PASSWORD: pisp123
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
    ports:
      - "8011:8011"
    depends_on:
      - postgres
      - nacos
      - redis
    restart: unless-stopped
    networks:
      - pisp-network

  # 拣选服务
  pisp-picking-service:
    build: ./pisp-services/pisp-picking-service
    container_name: pisp-picking-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: pisp
      SPRING_DATASOURCE_PASSWORD: pisp123
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
    ports:
      - "8012:8012"
    depends_on:
      - postgres
      - nacos
      - redis
    restart: unless-stopped
    networks:
      - pisp-network

  # 打包服务
  pisp-packing-service:
    build: ./pisp-services/pisp-packing-service
    container_name: pisp-packing-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: pisp
      SPRING_DATASOURCE_PASSWORD: pisp123
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
    ports:
      - "8013:8013"
    depends_on:
      - postgres
      - nacos
      - redis
    restart: unless-stopped
    networks:
      - pisp-network

  # 配送服务
  pisp-delivery-service:
    build: ./pisp-services/pisp-delivery-service
    container_name: pisp-delivery-service
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: pisp
      SPRING_DATASOURCE_PASSWORD: pisp123
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: nacos:8848
    ports:
      - "8014:8014"
    depends_on:
      - postgres
      - nacos
      - redis
    restart: unless-stopped
    networks:
      - pisp-network

networks:
  pisp-network:
    driver: bridge
```

### 6.6 API接口模块总结

#### 6.6.1 新增API模块概览

| API模块 | 职责 | 核心接口 | 依赖服务 |
|---------|------|----------|----------|
| **pisp-api-front-warehouse** | 前置仓管理API定义 | 前置仓管理、库存管理、智能补货 | pisp-front-warehouse-service |
| **pisp-api-order-fulfillment** | 订单履约API定义 | 订单分配、拣选、打包、配送 | pisp-order-fulfillment-service, pisp-picking-service, pisp-packing-service, pisp-delivery-service |

#### 6.6.2 技术特性

**🔧 核心技术栈：**
- **Spring Boot 3.4.7**：统一的微服务框架
- **OpenAPI 3.0**：标准化API文档
- **Bean Validation**：统一的数据验证
- **Spring Cloud OpenFeign**：服务间调用
- **Apache ShenYu 2.7.0.1**：高性能API网关

**🛡️ 安全特性：**
- **JWT认证**：基于令牌的身份认证
- **角色权限控制**：细粒度的权限管理
- **高危操作二次确认**：物理删除等高危操作的安全机制
- **请求限流**：防止API滥用
- **熔断保护**：服务故障时的降级处理

**📊 监控特性：**
- **API调用监控**：实时监控API调用情况
- **性能指标**：响应时间、吞吐量等指标
- **错误追踪**：异常和错误的详细追踪
- **业务日志**：关键业务操作的审计日志

**🚀 性能优化：**
- **缓存策略**：热点数据Redis缓存
- **连接池优化**：数据库连接池配置
- **异步处理**：非关键路径的异步处理
- **批量操作**：减少网络调用次数

#### 6.6.3 部署架构

```mermaid
graph TB
    subgraph "API网关层"
        GW[Apache ShenYu Gateway<br/>端口: 9195]
    end

    subgraph "API接口层"
        API1[pisp-api-front-warehouse<br/>前置仓管理API]
        API2[pisp-api-order-fulfillment<br/>订单履约API]
    end

    subgraph "业务服务层"
        FWS[pisp-front-warehouse-service<br/>端口: 8010]
        OFS[pisp-order-fulfillment-service<br/>端口: 8011]
        PS[pisp-picking-service<br/>端口: 8012]
        PKS[pisp-packing-service<br/>端口: 8013]
        DS[pisp-delivery-service<br/>端口: 8014]
    end

    subgraph "数据存储层"
        PG[(PostgreSQL 17<br/>Schema隔离)]
        RD[(Redis 7.x<br/>缓存&会话)]
    end

    GW --> API1
    GW --> API2
    API1 --> FWS
    API2 --> OFS
    API2 --> PS
    API2 --> PKS
    API2 --> DS

    FWS --> PG
    OFS --> PG
    PS --> PG
    PKS --> PG
    DS --> PG

    FWS --> RD
    OFS --> RD
    PS --> RD
    PKS --> RD
    DS --> RD
```

## 7. 系统集成总结

### 7.1 API接口模块集成

通过新增的API接口模块，PISP系统现在完全支持前置仓管理和订单履约的完整业务流程：

**📡 新增API模块：**
- **pisp-api-front-warehouse**：前置仓管理API接口定义（包含完整的订单履约功能）

**🏪 支持的前置仓业务流程：**
- **前置仓信息管理**：前置仓基础信息的CRUD操作
- **库存分配管理**：智能库存分配策略和执行
- **智能补货管理**：多种补货策略的配置和执行
- **订单履约管理**：从订单分配到配送完成的全流程
- **拣选打包管理**：拣选和打包作业的管理和跟踪
- **配送调度管理**：配送任务的调度和实时跟踪

> **详细业务流程和算法实现参考：** [DDD-011-前置仓管理模块.md](./DDD-011-前置仓管理模块.md)

### 7.2 技术特性总结

通过新增的API接口模块，PISP系统提供了标准化的RESTful API接口：

**📡 API特性：**
- **标准化设计**：遵循RESTful设计原则，使用OpenAPI 3.0规范
- **完整的数据验证**：Bean Validation注解确保数据完整性
- **安全机制完善**：JWT认证、角色权限、高危操作二次确认
- **高危操作保护**：完全支持物理删除策略和二次确认机制
- **服务间通信**：Feign客户端支持微服务间调用
- **网关路由管理**：ShenYu网关的统一路由和安全控制

### 7.3 系统架构演进

```mermaid
graph TB
    subgraph "PISP系统架构 v2.0"
        subgraph "API网关层"
            GW[Apache ShenYu Gateway<br/>统一入口 + 安全控制]
        end

        subgraph "API接口层"
            API1[传统业务API<br/>用户/基础数据/采购/销售/库存/财务/报表/系统/零售]
            API2[前置仓管理API<br/>前置仓管理/库存分配/智能补货]
            API3[订单履约API<br/>订单分配/拣选/打包/配送]
        end

        subgraph "业务服务层"
            BS1[传统业务服务<br/>9个微服务]
            BS2[前置仓管理服务<br/>智能算法引擎]
            BS3[订单履约服务<br/>拣选/打包/配送服务]
        end

        subgraph "数据存储层"
            PG[(PostgreSQL 17<br/>11个Schema隔离)]
            RD[(Redis 7.x<br/>缓存&会话)]
            KF[(Apache Kafka<br/>事件消息)]
        end

        subgraph "监控运维层"
            MON[Prometheus + Grafana<br/>监控告警]
            LOG[ELK Stack<br/>日志分析]
        end
    end

    GW --> API1
    GW --> API2
    GW --> API3

    API1 --> BS1
    API2 --> BS2
    API3 --> BS3

    BS1 --> PG
    BS2 --> PG
    BS3 --> PG

    BS1 --> RD
    BS2 --> RD
    BS3 --> RD

    BS2 --> KF
    BS3 --> KF

    BS1 --> MON
    BS2 --> MON
    BS3 --> MON
```

### 7.4 核心价值

**💼 业务价值：**
- **提升运营效率**：智能算法优化库存分配和补货策略
- **降低运营成本**：优化拣选路径和配送路线，减少人力和物流成本
- **提高客户满意度**：快速响应订单，缩短配送时间
- **增强业务扩展性**：支持前置仓网络的快速扩张

**🔧 技术价值：**
- **架构现代化**：微服务架构支持系统的高可用和可扩展
- **算法智能化**：集成多种优化算法，提升系统智能化水平
- **数据驱动决策**：实时数据分析支持业务决策
- **安全合规性**：完善的安全机制和审计日志

通过API接口模块的完整集成，PISP系统成功扩展了前置仓管理和订单履约能力，从传统的进销存管理系统演进为现代化的智能供应链管理平台，为企业的数字化转型提供了强有力的技术支撑。前置仓管理的详细业务流程、算法实现和技术架构请参考专门的模块设计文档。

---

**注意：** 本文档为总览文档，具体的实现细节请参考各个模块的详细设计文档。

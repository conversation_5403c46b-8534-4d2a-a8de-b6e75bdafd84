# DDD-004 销售管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-004 |
| 文档名称 | 销售管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

销售管理模块负责管理企业的销售业务流程，包括销售订单管理、销售出库管理和销售退货管理。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "销售管理模块"
        A[销售订单服务]
        B[销售出库服务]
        C[销售退货服务]
    end
    
    subgraph "业务流程"
        D[客户询价] --> E[销售报价]
        E --> F[销售订单]
        F --> G[信用检查]
        G --> H[库存检查]
        H --> I[销售出库]
        I --> J[物流配送]
    end
    
    A --> F
    B --> I
    C --> K[退货处理]
```

## 2. 销售订单管理

### 2.1 销售订单实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sales_orders")
public class SalesOrder extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("order_number")
    private String orderNumber;

    @TableField("customer_id")
    private Long customerId;

    @TableField("warehouse_id")
    private Long warehouseId;
    
    @TableField("status")
    @EnumValue
    private SalesOrderStatus status;

    @TableField("order_date")
    private LocalDate orderDate;

    @TableField("delivery_date")
    private LocalDate deliveryDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @TableField("tax_amount")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @TableField("final_amount")
    private BigDecimal finalAmount;

    @TableField("payment_terms")
    private Integer paymentTerms;

    @TableField("delivery_address")
    private String deliveryAddress;

    @TableField("remarks")
    private String remarks;

    @TableField("approved_by")
    private Long approvedBy;

    @TableField("approved_at")
    private LocalDateTime approvedAt;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    @OneToMany(mappedBy = "salesOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<SalesOrderItem> orderItems = new ArrayList<>();
    
    // 业务方法
    public void addOrderItem(SalesOrderItem item) {
        item.setSalesOrder(this);
        this.orderItems.add(item);
        recalculateTotal();
    }
    
    public void removeOrderItem(SalesOrderItem item) {
        this.orderItems.remove(item);
        recalculateTotal();
    }
    
    public void approve(Long approverId) {
        if (this.status != SalesOrderStatus.PENDING_APPROVAL) {
            throw new BusinessException("订单状态不允许审批");
        }
        this.status = SalesOrderStatus.APPROVED;
        this.approvedBy = approverId;
        this.approvedAt = LocalDateTime.now();
    }
    
    public void cancel(String reason) {
        if (this.status == SalesOrderStatus.COMPLETED || 
            this.status == SalesOrderStatus.CANCELLED) {
            throw new BusinessException("订单状态不允许取消");
        }
        this.status = SalesOrderStatus.CANCELLED;
        this.remarks = reason;
    }
    
    private void recalculateTotal() {
        this.totalAmount = orderItems.stream()
            .map(SalesOrderItem::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.finalAmount = this.totalAmount
            .subtract(this.discountAmount)
            .add(this.taxAmount);
    }
}
```

### 2.2 销售订单项实体

```java
@Entity
@Table(name = "sales_order_items")
@Data
@EqualsAndHashCode(callSuper = true)
public class SalesOrderItem extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "sales_order_id")
    private Long salesOrderId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sales_order_id", insertable = false, updatable = false)
    private SalesOrder salesOrder;
    
    @Column(name = "product_id")
    private Long productId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;
    
    @Column(name = "quantity", precision = 10, scale = 3)
    private BigDecimal quantity;
    
    @Column(name = "unit_price", precision = 10, scale = 2)
    private BigDecimal unitPrice;
    
    @Column(name = "total_amount", precision = 12, scale = 2)
    private BigDecimal totalAmount;
    
    @Column(name = "shipped_quantity", precision = 10, scale = 3)
    private BigDecimal shippedQuantity = BigDecimal.ZERO;
    
    @Column(name = "remarks")
    private String remarks;
    
    // 业务方法
    public void updateQuantity(BigDecimal newQuantity) {
        this.quantity = newQuantity;
        this.totalAmount = this.unitPrice.multiply(newQuantity);
    }
    
    public void updatePrice(BigDecimal newPrice) {
        this.unitPrice = newPrice;
        this.totalAmount = this.quantity.multiply(newPrice);
    }
    
    public BigDecimal getPendingQuantity() {
        return this.quantity.subtract(this.shippedQuantity);
    }
    
    public boolean isFullyShipped() {
        return this.shippedQuantity.compareTo(this.quantity) >= 0;
    }
}
```

### 2.3 销售订单服务实现

```java
@Service
@Transactional
public class SalesOrderServiceImpl extends ServiceImpl<SalesOrderMapper, SalesOrder> 
    implements SalesOrderService {
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private SalesOrderNumberGenerator numberGenerator;
    
    @Override
    public SalesOrderDTO createSalesOrder(CreateSalesOrderRequest request) {
        // 1. 验证客户
        Customer customer = customerService.getById(request.getCustomerId());
        if (customer == null || customer.getStatus() != CustomerStatus.ACTIVE) {
            throw new BusinessException("客户不存在或已停用");
        }
        
        // 2. 验证客户信用额度
        BigDecimal orderAmount = calculateOrderAmount(request.getOrderItems());
        if (!customer.hasAvailableCredit(orderAmount)) {
            throw new BusinessException("客户信用额度不足");
        }
        
        // 3. 验证库存
        validateInventoryAvailability(request.getOrderItems(), request.getWarehouseId());
        
        // 4. 生成订单号
        String orderNumber = numberGenerator.generateSalesOrderNumber();
        
        // 5. 创建销售订单
        SalesOrder order = new SalesOrder();
        BeanUtils.copyProperties(request, order);
        order.setOrderNumber(orderNumber);
        order.setStatus(SalesOrderStatus.DRAFT);
        order.setOrderDate(LocalDate.now());
        
        // 6. 添加订单项
        for (CreateSalesOrderItemRequest itemRequest : request.getOrderItems()) {
            Product product = productService.getById(itemRequest.getProductId());
            if (product == null || !product.isActive()) {
                throw new BusinessException("商品不存在或已停用: " + itemRequest.getProductId());
            }
            
            SalesOrderItem item = new SalesOrderItem();
            BeanUtils.copyProperties(itemRequest, item);
            item.setTotalAmount(item.getQuantity().multiply(item.getUnitPrice()));
            order.addOrderItem(item);
        }
        
        // 7. 保存订单
        this.save(order);
        
        return convertToDTO(order);
    }
    
    @Override
    public void submitForApproval(Long orderId) {
        SalesOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("销售订单不存在");
        }
        
        if (order.getStatus() != SalesOrderStatus.DRAFT) {
            throw new BusinessException("只有草稿状态的订单才能提交审批");
        }
        
        // 验证订单项
        if (CollectionUtils.isEmpty(order.getOrderItems())) {
            throw new BusinessException("订单项不能为空");
        }
        
        order.setStatus(SalesOrderStatus.PENDING_APPROVAL);
        this.updateById(order);
        
        // 发送审批通知
        applicationEventPublisher.publishEvent(
            new SalesOrderSubmittedEvent(orderId, order.getFinalAmount())
        );
    }
    
    @Override
    public void approveSalesOrder(Long orderId, Long approverId) {
        SalesOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("销售订单不存在");
        }
        
        // 1. 审批订单
        order.approve(approverId);
        this.updateById(order);
        
        // 2. 占用客户信用额度
        Customer customer = customerService.getById(order.getCustomerId());
        customer.occupyCredit(order.getFinalAmount());
        customerService.updateById(customer);
        
        // 3. 预留库存
        reserveInventoryForOrder(order);
        
        // 4. 发布审批通过事件
        applicationEventPublisher.publishEvent(
            new SalesOrderApprovedEvent(orderId, approverId)
        );
    }
    
    private void validateInventoryAvailability(List<CreateSalesOrderItemRequest> orderItems, Long warehouseId) {
        for (CreateSalesOrderItemRequest item : orderItems) {
            BigDecimal availableQuantity = inventoryService.getAvailableQuantity(
                item.getProductId(), warehouseId
            );
            if (availableQuantity.compareTo(item.getQuantity()) < 0) {
                Product product = productService.getById(item.getProductId());
                throw new BusinessException(
                    String.format("商品 %s 库存不足，可用数量：%s，需要数量：%s", 
                        product.getProductName(), availableQuantity, item.getQuantity())
                );
            }
        }
    }
    
    private void reserveInventoryForOrder(SalesOrder order) {
        for (SalesOrderItem item : order.getOrderItems()) {
            inventoryService.reserveInventory(
                item.getProductId(),
                order.getWarehouseId(),
                item.getQuantity(),
                "销售订单预留-" + order.getOrderNumber()
            );
        }
    }
    
    private BigDecimal calculateOrderAmount(List<CreateSalesOrderItemRequest> orderItems) {
        return orderItems.stream()
            .map(item -> item.getQuantity().multiply(item.getUnitPrice()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
```

## 3. 销售出库管理

### 3.1 销售出库实体设计

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sales_shipments")
public class SalesShipment extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("shipment_number")
    private String shipmentNumber;

    @TableField("sales_order_id")
    private Long salesOrderId;

    @TableField("customer_id")
    private Long customerId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("shipment_date")
    private LocalDate shipmentDate;

    @TableField("status")
    @EnumValue
    private ShipmentStatus status;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("delivery_address")
    private String deliveryAddress;

    @TableField("tracking_number")
    private String trackingNumber;

    @TableField("shipped_by")
    private Long shippedBy;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法
    public void confirm(Long shipperId) {
        if (this.status != ShipmentStatus.PENDING) {
            throw new BusinessException("只有待确认状态的出库单才能确认");
        }
        this.status = ShipmentStatus.CONFIRMED;
        this.shippedBy = shipperId;
    }
}
```

## 4. 类图设计

### 4.1 销售管理核心类图

```mermaid
classDiagram
    class SalesOrder {
        +Long id
        +String orderNumber
        +Long customerId
        +Long warehouseId
        +SalesOrderStatus status
        +LocalDate orderDate
        +LocalDate deliveryDate
        +BigDecimal totalAmount
        +BigDecimal finalAmount
        +approve()
        +cancel()
        +addOrderItem()
        +removeOrderItem()
    }

    class SalesOrderItem {
        +Long id
        +Long salesOrderId
        +Long productId
        +BigDecimal quantity
        +BigDecimal unitPrice
        +BigDecimal totalAmount
        +BigDecimal shippedQuantity
        +updateQuantity()
        +updatePrice()
        +getPendingQuantity()
        +isFullyShipped()
    }

    class SalesShipment {
        +Long id
        +String shipmentNumber
        +Long salesOrderId
        +Long customerId
        +Long warehouseId
        +ShipmentStatus status
        +LocalDate shipmentDate
        +BigDecimal totalAmount
        +String deliveryAddress
        +String trackingNumber
        +confirm()
    }

    class SalesShipmentItem {
        +Long id
        +Long salesShipmentId
        +Long salesOrderItemId
        +Long productId
        +BigDecimal orderedQuantity
        +BigDecimal shippedQuantity
        +BigDecimal unitPrice
        +BigDecimal totalAmount
        +updateShippedQuantity()
        +isFullyShipped()
    }

    class SalesOrderService {
        +createSalesOrder()
        +submitForApproval()
        +approveSalesOrder()
        +getSalesOrders()
    }

    class SalesShipmentService {
        +createSalesShipment()
        +confirmShipment()
        +getSalesShipments()
    }

    SalesOrder --o SalesOrderItem
    SalesShipment --o SalesShipmentItem
    SalesOrder --o SalesShipment
    SalesOrderItem --o SalesShipmentItem

    SalesOrderService ..> SalesOrder
    SalesShipmentService ..> SalesShipment
```

## 5. 时序图设计

### 5.1 销售订单创建时序图

```mermaid
sequenceDiagram
    participant U as 销售员
    participant G as ShenYu网关
    participant SS as 销售服务
    participant CS as 客户服务
    participant IS as 库存服务
    participant FS as 财务服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 创建销售订单请求
    G->>SS: 转发请求

    SS->>CS: 验证客户信息
    CS-->>SS: 返回客户信息

    SS->>FS: 检查客户信用额度
    FS-->>SS: 返回信用检查结果

    SS->>IS: 检查库存可用性
    IS-->>SS: 返回库存检查结果

    SS->>SS: 生成订单号
    SS->>SS: 创建订单对象
    SS->>SS: 添加订单项

    SS->>DB: 保存销售订单
    DB-->>SS: 返回订单ID

    SS->>K: 发布订单创建事件
    K-->>SS: 确认事件发布

    SS-->>G: 返回创建结果
    G-->>U: 返回响应

    Note over K: 异步事件处理
    K->>CS: 客户订单通知事件
    K->>IS: 库存预留事件
```

### 5.2 销售出库确认时序图

```mermaid
sequenceDiagram
    participant U as 仓库管理员
    participant G as ShenYu网关
    participant SHS as 出库服务
    participant IS as 库存服务
    participant SS as 销售服务
    participant LS as 物流服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 确认出库请求
    G->>SHS: 转发请求

    SHS->>DB: 获取出库单信息
    DB-->>SHS: 返回出库单

    SHS->>SHS: 验证出库状态
    SHS->>SHS: 确认出库

    SHS->>DB: 更新出库单状态
    DB-->>SHS: 确认更新

    SHS->>K: 发布出库确认事件
    K-->>IS: 库存减少事件
    K-->>SS: 销售发货事件
    K-->>LS: 物流配送事件

    par 并行处理
        IS->>DB: 减少库存数量
        and
        SS->>DB: 更新订单发货数量
        and
        LS->>LS: 创建配送任务
    end

    SHS-->>G: 返回确认结果
    G-->>U: 返回响应

    Note over K: 异步处理完成
    IS->>K: 库存变动事件
    LS->>K: 配送任务创建事件
```

## 6. 交互图设计

### 6.1 销售订单审批流程交互图

```mermaid
graph TB
    subgraph "销售订单审批流程"
        A[销售订单创建] --> B{订单金额检查}
        B -->|≤10万| C[自动审批]
        B -->|>10万| D[提交审批]

        C --> E[审批通过]
        D --> F{审批结果}
        F -->|通过| E
        F -->|拒绝| G[审批拒绝]

        E --> H[占用客户信用]
        H --> I[预留库存]
        I --> J[订单生效]

        G --> K[订单取消]

        J --> L[可以创建出库单]
        K --> M[流程结束]
        L --> N[等待出库]
    end
```

### 6.2 库存预留释放交互图

```mermaid
graph LR
    subgraph "库存预留管理"
        A[销售订单审批] --> B[预留库存]
        B --> C{预留成功?}
        C -->|是| D[库存预留记录]
        C -->|否| E[库存不足提醒]

        D --> F[等待出库]
        F --> G{出库确认}
        G -->|确认出库| H[扣减库存]
        G -->|取消订单| I[释放预留库存]

        H --> J[库存实际减少]
        I --> K[库存恢复可用]

        E --> L[订单创建失败]
    end
```

## 7. 事件驱动设计

### 7.1 销售领域事件

```java
// 销售订单创建事件
@Data
@AllArgsConstructor
public class SalesOrderCreatedEvent extends DomainEvent {
    private Long orderId;
    private String orderNumber;
    private Long customerId;
    private BigDecimal totalAmount;
    private List<SalesOrderItemDTO> orderItems;
}

// 销售订单审批事件
@Data
@AllArgsConstructor
public class SalesOrderApprovedEvent extends DomainEvent {
    private Long orderId;
    private Long approverId;
    private LocalDateTime approvedAt;
    private BigDecimal orderAmount;
}

// 销售出库确认事件
@Data
@AllArgsConstructor
public class SalesShipmentConfirmedEvent extends DomainEvent {
    private Long shipmentId;
    private Long salesOrderId;
    private BigDecimal totalAmount;
    private List<SalesShipmentItemDTO> shipmentItems;
}
```

### 7.2 Kafka事件处理

```java
@Component
public class SalesEventPublisher {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String SALES_TOPIC = "sales.events";

    public void publishOrderCreated(SalesOrderCreatedEvent event) {
        kafkaTemplate.send(SALES_TOPIC, "order.created", event);
    }

    public void publishOrderApproved(SalesOrderApprovedEvent event) {
        kafkaTemplate.send(SALES_TOPIC, "order.approved", event);
    }

    public void publishShipmentConfirmed(SalesShipmentConfirmedEvent event) {
        kafkaTemplate.send(SALES_TOPIC, "shipment.confirmed", event);
    }
}

@Component
@KafkaListener(topics = "sales.events")
public class SalesEventHandler {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private NotificationService notificationService;

    @KafkaHandler
    public void handleOrderApproved(SalesOrderApprovedEvent event) {
        // 占用客户信用额度
        customerService.occupyCredit(event.getCustomerId(), event.getOrderAmount());

        // 预留库存
        inventoryService.reserveInventoryForOrder(event.getOrderId());

        // 发送通知
        notificationService.sendOrderApprovedNotification(event);
    }

    @KafkaHandler
    public void handleShipmentConfirmed(SalesShipmentConfirmedEvent event) {
        // 减少库存
        for (SalesShipmentItemDTO item : event.getShipmentItems()) {
            inventoryService.decreaseInventory(
                item.getProductId(),
                event.getWarehouseId(),
                item.getShippedQuantity(),
                "销售出库-" + event.getShipmentId()
            );
        }

        // 发送发货通知
        notificationService.sendShipmentNotification(event);
    }
}
```

# DDD-005 库存管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-005 |
| 文档名称 | 库存管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

库存管理模块负责管理企业的库存业务，包括库存查询、库存盘点、库存预警和库存调拨等功能。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "库存管理模块"
        A[库存查询服务]
        B[库存盘点服务]
        C[库存预警服务]
        D[库存调拨服务]
        E[库存事务服务]
    end
    
    subgraph "核心功能"
        F[实时库存查询]
        G[库存盘点管理]
        H[自动预警监控]
        I[仓库间调拨]
        J[库存事务记录]
    end
    
    A --> F
    B --> G
    C --> H
    D --> I
    E --> J
```

## 2. 库存核心实体

### 2.1 库存主表

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inventory")
public class Inventory extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("product_id")
    private Long productId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("available_quantity")
    private BigDecimal availableQuantity;

    @TableField("reserved_quantity")
    private BigDecimal reservedQuantity;

    @TableField("total_quantity")
    private BigDecimal totalQuantity;

    @TableField("unit_cost")
    private BigDecimal unitCost;

    @TableField("reorder_point")
    private BigDecimal reorderPoint;

    @TableField("max_stock_level")
    private BigDecimal maxStockLevel;
    
    @Version
    private Integer version;
    
    // 业务方法
    public void adjustQuantity(BigDecimal quantity, InventoryTransactionType type, String reason) {
        switch (type) {
            case IN:
                this.availableQuantity = this.availableQuantity.add(quantity);
                this.totalQuantity = this.totalQuantity.add(quantity);
                break;
            case OUT:
                if (this.availableQuantity.compareTo(quantity) < 0) {
                    throw new InsufficientInventoryException("库存不足");
                }
                this.availableQuantity = this.availableQuantity.subtract(quantity);
                this.totalQuantity = this.totalQuantity.subtract(quantity);
                break;
            case RESERVE:
                if (this.availableQuantity.compareTo(quantity) < 0) {
                    throw new InsufficientInventoryException("可用库存不足");
                }
                this.availableQuantity = this.availableQuantity.subtract(quantity);
                this.reservedQuantity = this.reservedQuantity.add(quantity);
                break;
            case RELEASE:
                this.availableQuantity = this.availableQuantity.add(quantity);
                this.reservedQuantity = this.reservedQuantity.subtract(quantity);
                break;
        }
    }
    
    public boolean isLowStock() {
        return this.availableQuantity.compareTo(this.reorderPoint) <= 0;
    }
    
    public boolean isOverStock() {
        return this.totalQuantity.compareTo(this.maxStockLevel) > 0;
    }
}
```

### 2.2 库存事务记录

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inventory_transactions")
public class InventoryTransaction extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("transaction_number")
    private String transactionNumber;

    @TableField("product_id")
    private Long productId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("transaction_type")
    @EnumValue
    private InventoryTransactionType transactionType;

    @TableField("quantity")
    private BigDecimal quantity;

    @TableField("unit_cost")
    private BigDecimal unitCost;

    @TableField("reference_type")
    private String referenceType; // ORDER, ADJUSTMENT, TRANSFER

    @TableField("reference_id")
    private Long referenceId;

    @TableField("reason")
    private String reason;

    @TableField("before_quantity")
    private BigDecimal beforeQuantity;

    @TableField("after_quantity")
    private BigDecimal afterQuantity;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
```

## 3. 库存查询服务

### 3.1 库存查询服务实现

```java
@Service
@Transactional(readOnly = true)
public class InventoryQueryServiceImpl implements InventoryQueryService {
    
    @Autowired
    private InventoryMapper inventoryMapper;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private WarehouseService warehouseService;
    
    @Override
    public IPage<InventoryDTO> getInventoryList(InventoryQueryRequest request) {
        Page<Inventory> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<Inventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(request.getWarehouseId() != null, 
                  Inventory::getWarehouseId, request.getWarehouseId())
               .eq(request.getProductId() != null, 
                  Inventory::getProductId, request.getProductId())
               .ge(request.getMinQuantity() != null, 
                  Inventory::getAvailableQuantity, request.getMinQuantity())
               .le(request.getMaxQuantity() != null, 
                  Inventory::getAvailableQuantity, request.getMaxQuantity())
               .apply(request.getLowStockOnly() != null && request.getLowStockOnly(), 
                     "available_quantity <= reorder_point")
               .orderByDesc(Inventory::getUpdateTime);
        
        IPage<Inventory> inventoryPage = inventoryMapper.selectPage(page, wrapper);
        return inventoryPage.convert(this::convertToDTO);
    }
    
    @Override
    public List<InventoryAlertDTO> getLowStockAlerts() {
        List<Inventory> lowStockInventories = inventoryMapper.selectList(
            new LambdaQueryWrapper<Inventory>()
                .apply("available_quantity <= reorder_point")
                .gt(Inventory::getAvailableQuantity, BigDecimal.ZERO)
        );
        
        return lowStockInventories.stream()
            .map(this::convertToAlertDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public InventoryValueReportDTO getInventoryValueReport(Long warehouseId) {
        List<Inventory> inventories;
        if (warehouseId != null) {
            inventories = inventoryMapper.selectList(
                new LambdaQueryWrapper<Inventory>()
                    .eq(Inventory::getWarehouseId, warehouseId)
            );
        } else {
            inventories = inventoryMapper.selectList(null);
        }
        
        BigDecimal totalValue = inventories.stream()
            .map(inv -> inv.getTotalQuantity().multiply(inv.getUnitCost()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        return InventoryValueReportDTO.builder()
            .totalItems(inventories.size())
            .totalQuantity(inventories.stream()
                .map(Inventory::getTotalQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add))
            .totalValue(totalValue)
            .reportDate(LocalDateTime.now())
            .build();
    }
    
    @Override
    public BigDecimal getAvailableQuantity(Long productId, Long warehouseId) {
        Inventory inventory = inventoryMapper.selectOne(
            new LambdaQueryWrapper<Inventory>()
                .eq(Inventory::getProductId, productId)
                .eq(Inventory::getWarehouseId, warehouseId)
        );
        
        return inventory != null ? inventory.getAvailableQuantity() : BigDecimal.ZERO;
    }
}
```

## 4. 库存预警服务

### 4.1 库存预警实体

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inventory_alerts")
public class InventoryAlert extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("product_id")
    private Long productId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("alert_type")
    @EnumValue
    private AlertType alertType; // LOW_STOCK, OUT_OF_STOCK, OVERSTOCK, EXPIRY

    @TableField("current_quantity")
    private BigDecimal currentQuantity;

    @TableField("threshold_quantity")
    private BigDecimal thresholdQuantity;

    @TableField("alert_message")
    private String alertMessage;

    @TableField("status")
    @EnumValue
    private AlertStatus status; // ACTIVE, RESOLVED, IGNORED

    @TableField("resolved_at")
    private LocalDateTime resolvedAt;

    @TableField("resolved_by")
    private Long resolvedBy;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void resolve(Long userId) {
        this.status = AlertStatus.RESOLVED;
        this.resolvedAt = LocalDateTime.now();
        this.resolvedBy = userId;
    }
    
    public void ignore(Long userId) {
        this.status = AlertStatus.IGNORED;
        this.resolvedAt = LocalDateTime.now();
        this.resolvedBy = userId;
    }
}
```

### 4.2 库存预警服务实现

```java
@Service
@Transactional
public class InventoryAlertServiceImpl extends ServiceImpl<InventoryAlertMapper, InventoryAlert> 
    implements InventoryAlertService {
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private NotificationService notificationService;
    
    @Override
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkInventoryAlerts() {
        List<Inventory> inventories = inventoryService.getAllInventories();
        
        for (Inventory inventory : inventories) {
            checkLowStockAlert(inventory);
            checkOutOfStockAlert(inventory);
            checkOverStockAlert(inventory);
        }
    }
    
    private void checkLowStockAlert(Inventory inventory) {
        if (inventory.isLowStock() && inventory.getAvailableQuantity().compareTo(BigDecimal.ZERO) > 0) {
            // 检查是否已存在活跃的预警
            boolean existsActiveAlert = this.exists(
                new LambdaQueryWrapper<InventoryAlert>()
                    .eq(InventoryAlert::getProductId, inventory.getProductId())
                    .eq(InventoryAlert::getWarehouseId, inventory.getWarehouseId())
                    .eq(InventoryAlert::getAlertType, AlertType.LOW_STOCK)
                    .eq(InventoryAlert::getStatus, AlertStatus.ACTIVE)
            );
            
            if (!existsActiveAlert) {
                createAlert(inventory, AlertType.LOW_STOCK, 
                    String.format("商品库存不足，当前数量：%s，安全库存：%s", 
                        inventory.getAvailableQuantity(),
                        inventory.getReorderPoint()));
            }
        }
    }
    
    private void createAlert(Inventory inventory, AlertType alertType, String message) {
        InventoryAlert alert = new InventoryAlert();
        alert.setProductId(inventory.getProductId());
        alert.setWarehouseId(inventory.getWarehouseId());
        alert.setAlertType(alertType);
        alert.setCurrentQuantity(inventory.getAvailableQuantity());
        alert.setThresholdQuantity(inventory.getReorderPoint());
        alert.setAlertMessage(message);
        alert.setStatus(AlertStatus.ACTIVE);
        
        this.save(alert);
        
        // 发送通知
        notificationService.sendInventoryAlert(alert);
        
        // 发布预警事件
        applicationEventPublisher.publishEvent(
            new InventoryAlertCreatedEvent(alert.getId(), alertType, message)
        );
    }
}
```

## 5. 库存盘点管理

### 5.1 库存盘点实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inventory_checks")
public class InventoryCheck extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("check_number")
    private String checkNumber;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("check_type")
    @EnumValue
    private CheckType checkType; // FULL, PARTIAL, CYCLE

    @TableField("check_date")
    private LocalDate checkDate;

    @TableField("status")
    @EnumValue
    private CheckStatus status;

    @TableField("total_items")
    private Integer totalItems;

    @TableField("checked_items")
    private Integer checkedItems = 0;

    @TableField("variance_items")
    private Integer varianceItems = 0;

    @TableField("checked_by")
    private Long checkedBy;

    @TableField("completed_at")
    private LocalDateTime completedAt;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法
    public void complete(Long checkerId) {
        if (this.status != CheckStatus.IN_PROGRESS) {
            throw new BusinessException("只有进行中的盘点才能完成");
        }
        this.status = CheckStatus.COMPLETED;
        this.checkedItems = checkItems.size();
        this.varianceItems = (int) checkItems.stream()
            .filter(InventoryCheckItem::hasVariance)
            .count();
    }
}
```

## 6. 类图设计

### 6.1 库存管理核心类图

```mermaid
classDiagram
    class Inventory {
        +Long id
        +Long productId
        +Long warehouseId
        +BigDecimal availableQuantity
        +BigDecimal reservedQuantity
        +BigDecimal totalQuantity
        +BigDecimal unitCost
        +BigDecimal reorderPoint
        +BigDecimal maxStockLevel
        +adjustQuantity()
        +isLowStock()
        +isOverStock()
    }

    class InventoryTransaction {
        +Long id
        +String transactionNumber
        +Long productId
        +Long warehouseId
        +InventoryTransactionType transactionType
        +BigDecimal quantity
        +BigDecimal unitCost
        +String referenceType
        +Long referenceId
        +String reason
        +BigDecimal beforeQuantity
        +BigDecimal afterQuantity
    }

    class InventoryAlert {
        +Long id
        +Long productId
        +Long warehouseId
        +AlertType alertType
        +BigDecimal currentQuantity
        +BigDecimal thresholdQuantity
        +String alertMessage
        +AlertStatus status
        +resolve()
        +ignore()
    }

    class InventoryCheck {
        +Long id
        +String checkNumber
        +Long warehouseId
        +CheckType checkType
        +LocalDate checkDate
        +CheckStatus status
        +Integer totalItems
        +Integer checkedItems
        +Integer varianceItems
        +complete()
    }

    class InventoryCheckItem {
        +Long id
        +Long inventoryCheckId
        +Long productId
        +BigDecimal bookQuantity
        +BigDecimal actualQuantity
        +BigDecimal varianceQuantity
        +BigDecimal unitCost
        +BigDecimal varianceAmount
        +calculateVariance()
        +hasVariance()
    }

    class InventoryService {
        +getAvailableQuantity()
        +adjustQuantity()
        +reserveInventory()
        +releaseInventory()
    }

    Inventory --o InventoryTransaction
    Inventory --o InventoryAlert
    InventoryCheck --o InventoryCheckItem

    InventoryService ..> Inventory
    InventoryService ..> InventoryTransaction
```

## 7. 时序图设计

### 7.1 库存调整时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as ShenYu网关
    participant IS as 库存服务
    participant K as Kafka
    participant DB as 数据库
    participant AS as 预警服务
    participant NS as 通知服务

    U->>G: 库存调整请求
    G->>IS: 转发请求

    IS->>DB: 获取当前库存
    DB-->>IS: 返回库存信息

    IS->>IS: 验证调整数量
    IS->>IS: 计算调整后库存

    IS->>DB: 更新库存数量
    IS->>DB: 创建库存事务记录
    DB-->>IS: 确认更新

    IS->>K: 发布库存变动事件
    K-->>AS: 库存变动事件
    K-->>NS: 库存变动事件

    IS-->>G: 返回调整结果
    G-->>U: 返回响应

    Note over AS,NS: 异步处理
    AS->>AS: 检查库存预警
    AS->>DB: 创建预警记录
    NS->>NS: 发送变动通知
```

### 7.2 库存预警检查时序图

```mermaid
sequenceDiagram
    participant T as 定时任务
    participant AS as 预警服务
    participant DB as 数据库
    participant K as Kafka
    participant NS as 通知服务
    participant U as 用户

    T->>AS: 触发预警检查
    AS->>DB: 查询所有库存
    DB-->>AS: 返回库存列表

    loop 遍历库存
        AS->>AS: 检查库存水平
        alt 低库存
            AS->>DB: 创建低库存预警
            AS->>K: 发布预警事件
        else 缺货
            AS->>DB: 创建缺货预警
            AS->>K: 发布预警事件
        else 超库存
            AS->>DB: 创建超库存预警
            AS->>K: 发布预警事件
        end
    end

    K-->>NS: 预警事件
    NS->>U: 发送预警通知

    Note over T,U: 每5分钟执行一次
```

## 8. 交互图设计

### 8.1 库存预留释放交互图

```mermaid
graph TB
    subgraph "库存预留管理流程"
        A[销售订单创建] --> B[检查可用库存]
        B --> C{库存充足?}
        C -->|是| D[预留库存]
        C -->|否| E[库存不足]

        D --> F[减少可用库存]
        F --> G[增加预留库存]
        G --> H[创建预留记录]

        H --> I{后续操作}
        I -->|确认出库| J[扣减预留库存]
        I -->|取消订单| K[释放预留库存]

        J --> L[减少总库存]
        K --> M[恢复可用库存]

        E --> N[订单创建失败]
    end
```

### 8.2 库存盘点流程交互图

```mermaid
graph LR
    subgraph "库存盘点流程"
        A[创建盘点计划] --> B[生成盘点单]
        B --> C[分配盘点任务]
        C --> D[执行盘点]

        D --> E[录入实际数量]
        E --> F[计算差异]
        F --> G{有差异?}

        G -->|是| H[差异分析]
        G -->|否| I[盘点完成]

        H --> J[差异审核]
        J --> K{审核通过?}
        K -->|是| L[调整库存]
        K -->|否| M[重新盘点]

        L --> N[生成盘点报告]
        M --> D
        I --> N
    end
```

## 9. 事件驱动设计

### 9.1 库存领域事件

```java
// 库存变动事件
@Data
@AllArgsConstructor
public class InventoryChangedEvent extends DomainEvent {
    private Long productId;
    private Long warehouseId;
    private BigDecimal oldQuantity;
    private BigDecimal newQuantity;
    private BigDecimal changeQuantity;
    private InventoryTransactionType transactionType;
    private String reason;
}

// 库存预警事件
@Data
@AllArgsConstructor
public class InventoryAlertCreatedEvent extends DomainEvent {
    private Long alertId;
    private Long productId;
    private Long warehouseId;
    private AlertType alertType;
    private BigDecimal currentQuantity;
    private BigDecimal thresholdQuantity;
    private String alertMessage;
}

// 库存盘点完成事件
@Data
@AllArgsConstructor
public class InventoryCheckCompletedEvent extends DomainEvent {
    private Long checkId;
    private String checkNumber;
    private Long warehouseId;
    private Integer totalItems;
    private Integer varianceItems;
    private List<InventoryCheckItemDTO> checkItems;
}
```

### 9.2 Kafka事件处理

```java
@Component
public class InventoryEventPublisher {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String INVENTORY_TOPIC = "inventory.events";

    public void publishInventoryChanged(InventoryChangedEvent event) {
        kafkaTemplate.send(INVENTORY_TOPIC, "inventory.changed", event);
    }

    public void publishAlertCreated(InventoryAlertCreatedEvent event) {
        kafkaTemplate.send(INVENTORY_TOPIC, "alert.created", event);
    }

    public void publishCheckCompleted(InventoryCheckCompletedEvent event) {
        kafkaTemplate.send(INVENTORY_TOPIC, "check.completed", event);
    }
}

@Component
@KafkaListener(topics = "inventory.events")
public class InventoryEventHandler {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ReportService reportService;

    @KafkaHandler
    public void handleInventoryChanged(InventoryChangedEvent event) {
        // 更新库存报表缓存
        reportService.updateInventoryReportCache(event.getProductId(), event.getWarehouseId());
    }

    @KafkaHandler
    public void handleAlertCreated(InventoryAlertCreatedEvent event) {
        // 发送预警通知
        notificationService.sendInventoryAlert(event);
    }

    @KafkaHandler
    public void handleCheckCompleted(InventoryCheckCompletedEvent event) {
        // 生成盘点报告
        reportService.generateInventoryCheckReport(event);

        // 发送盘点完成通知
        notificationService.sendCheckCompletedNotification(event);
    }
}
```

## 8. 数据流向设计

### 8.1 整体数据流架构

基于DDD-012业务流程设计完善，库存管理模块的数据流向设计如下：

```mermaid
graph TB
    subgraph "数据源层"
        A[前置仓本地数据]
        B[中心仓库数据]
        C[订单系统数据]
        D[外部系统数据]
    end

    subgraph "数据处理层"
        E[数据收集服务]
        F[数据验证服务]
        G[数据转换服务]
        H[数据同步服务]
        I[冲突解决服务]
    end

    subgraph "业务逻辑层"
        J[库存管理服务]
        K[库存分配服务]
        L[智能补货服务]
        M[库存同步服务]
    end

    subgraph "数据存储层"
        N[(主数据库)]
        O[(本地缓存)]
        P[(消息队列)]
        Q[(数据仓库)]
    end

    A --> E
    B --> E
    C --> E
    D --> E

    E --> F
    F --> G
    G --> H
    H --> I

    I --> J
    I --> K
    I --> L
    I --> M

    J --> N
    K --> N
    L --> N
    M --> N

    J --> O
    K --> O
    L --> O
    M --> O

    J --> P
    K --> P
    L --> P
    M --> P
```

### 8.2 库存同步机制

**实时同步流程设计：**

```mermaid
sequenceDiagram
    participant FW as 前置仓
    participant MQ as RocketMQ
    participant IS as 库存同步服务
    participant DB as 中心数据库
    participant Cache as Redis缓存
    participant AS as 告警服务

    Note over FW,AS: 正常同步流程
    FW->>MQ: 库存变动事件
    MQ->>IS: 消费事件
    IS->>IS: 数据验证
    IS->>IS: 冲突检测
    IS->>DB: 更新中心库存
    IS->>Cache: 更新缓存
    IS->>MQ: 发送确认事件
    MQ->>FW: 同步确认

    Note over FW,AS: 冲突处理流程
    FW->>MQ: 库存变动事件
    MQ->>IS: 消费事件
    IS->>IS: 检测到冲突
    IS->>IS: 执行冲突解决策略
    IS->>DB: 更新解决后的数据
    IS->>MQ: 发送冲突解决通知
    MQ->>FW: 冲突解决结果

    Note over FW,AS: 异常处理流程
    FW->>MQ: 库存变动事件
    MQ->>IS: 消费事件
    IS->>IS: 处理失败
    IS->>AS: 发送告警
    IS->>MQ: 发送到死信队列
    IS->>IS: 记录失败日志
    IS->>MQ: 触发重试机制
```

### 8.3 数据一致性保证机制

**分布式事务处理：**

```java
@Service
@Transactional
public class InventoryDistributedTransactionService {

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 分布式事务处理 - 库存分配
     */
    public AllocationResult executeInventoryAllocation(AllocationRequest request) {
        // 1. 预检查阶段
        PreCheckResult preCheck = preCheckAllocation(request);
        if (!preCheck.isValid()) {
            throw new AllocationException("Pre-check failed: " + preCheck.getErrorMessage());
        }

        // 2. 本地事务处理
        AllocationRecord record = transactionTemplate.execute(status -> {
            try {
                // 2.1 锁定中心库存
                boolean locked = inventoryService.lockCentralInventory(request);
                if (!locked) {
                    throw new AllocationException("Failed to lock central inventory");
                }

                // 2.2 创建分配记录
                AllocationRecord allocationRecord = allocationService.createRecord(request);

                // 2.3 发送事务消息
                SendResult sendResult = rocketMQTemplate.sendMessageInTransaction(
                    "inventory-allocation-topic",
                    MessageBuilder.withPayload(allocationRecord).build(),
                    request
                );

                if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                    throw new AllocationException("Failed to send transaction message");
                }

                return allocationRecord;

            } catch (Exception e) {
                status.setRollbackOnly();
                throw new AllocationException("Local transaction failed", e);
            }
        });

        // 3. 异步验证结果
        CompletableFuture<Boolean> verificationFuture = CompletableFuture.supplyAsync(() -> {
            return verifyAllocationResult(request, record);
        });

        return AllocationResult.builder()
            .record(record)
            .verificationFuture(verificationFuture)
            .build();
    }

    /**
     * 事务消息监听器
     */
    @RocketMQTransactionListener
    public class AllocationTransactionListener implements RocketMQLocalTransactionListener {

        @Override
        public RocketMQLocalTransactionState executeLocalTransaction(Message msg, Object arg) {
            try {
                AllocationRequest request = (AllocationRequest) arg;

                // 执行前置仓库存更新
                boolean success = frontWarehouseService.updateInventory(request);

                if (success) {
                    // 记录事务状态
                    transactionService.recordTransactionState(
                        msg.getKeys(),
                        TransactionState.COMMITTED
                    );
                    return RocketMQLocalTransactionState.COMMIT;
                } else {
                    transactionService.recordTransactionState(
                        msg.getKeys(),
                        TransactionState.ROLLBACK
                    );
                    return RocketMQLocalTransactionState.ROLLBACK;
                }

            } catch (Exception e) {
                log.error("Local transaction execution failed", e);
                transactionService.recordTransactionState(
                    msg.getKeys(),
                    TransactionState.ROLLBACK
                );
                return RocketMQLocalTransactionState.ROLLBACK;
            }
        }

        @Override
        public RocketMQLocalTransactionState checkLocalTransaction(Message msg) {
            // 检查本地事务状态
            String transactionId = msg.getKeys();
            TransactionState state = transactionService.getTransactionState(transactionId);

            switch (state) {
                case COMMITTED:
                    return RocketMQLocalTransactionState.COMMIT;
                case ROLLBACK:
                    return RocketMQLocalTransactionState.ROLLBACK;
                case UNKNOWN:
                default:
                    return RocketMQLocalTransactionState.UNKNOWN;
            }
        }
    }
}
```

## 9. 总结

库存管理模块通过合理的架构设计和完善的功能实现，为PISP系统提供了可靠的库存管理能力。模块采用微服务架构，支持高并发访问和水平扩展，能够满足企业级应用的需求。

通过实时库存查询、智能预警监控、灵活的调拨管理和完整的事务记录，结合完善的数据流向设计和分布式事务处理机制，模块能够帮助企业实现精细化的库存管理，提高库存周转率，降低库存成本。

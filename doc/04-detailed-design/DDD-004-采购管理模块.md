# DDD-003 采购管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-003 |
| 文档名称 | 采购管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

采购管理模块负责管理企业的采购业务流程，包括采购订单管理、采购入库管理和采购退货管理。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "采购管理模块"
        A[采购订单服务]
        B[采购入库服务]
        C[采购退货服务]
    end
    
    subgraph "业务流程"
        D[需求计划] --> E[采购申请]
        E --> F[采购订单]
        F --> G[采购入库]
        G --> H[质量检验]
        H --> I[库存更新]
    end
    
    A --> F
    B --> G
    C --> J[退货处理]
```

## 2. 采购订单管理

### 2.1 采购订单实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_orders")
public class PurchaseOrder extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("order_number")
    private String orderNumber;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("status")
    @EnumValue
    private PurchaseOrderStatus status;

    @TableField("order_date")
    private LocalDate orderDate;

    @TableField("expected_date")
    private LocalDate expectedDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @TableField("tax_amount")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @TableField("final_amount")
    private BigDecimal finalAmount;

    @TableField("payment_terms")
    private Integer paymentTerms;

    @TableField("remarks")
    private String remarks;

    @TableField("approved_by")
    private Long approvedBy;

    @TableField("approved_at")
    private LocalDateTime approvedAt;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void addOrderItem(PurchaseOrderItem item) {
        item.setPurchaseOrder(this);
        this.orderItems.add(item);
        recalculateTotal();
    }
    
    public void removeOrderItem(PurchaseOrderItem item) {
        this.orderItems.remove(item);
        recalculateTotal();
    }
    
    public void approve(Long approverId) {
        if (this.status != PurchaseOrderStatus.PENDING_APPROVAL) {
            throw new BusinessException("订单状态不允许审批");
        }
        this.status = PurchaseOrderStatus.APPROVED;
        this.approvedBy = approverId;
        this.approvedAt = LocalDateTime.now();
    }
    
    public void cancel(String reason) {
        if (this.status == PurchaseOrderStatus.COMPLETED || 
            this.status == PurchaseOrderStatus.CANCELLED) {
            throw new BusinessException("订单状态不允许取消");
        }
        this.status = PurchaseOrderStatus.CANCELLED;
        this.remarks = reason;
    }
    
    private void recalculateTotal() {
        this.totalAmount = orderItems.stream()
            .map(PurchaseOrderItem::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.finalAmount = this.totalAmount
            .subtract(this.discountAmount)
            .add(this.taxAmount);
    }
}
```

### 2.2 采购订单项实体

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_order_items")
public class PurchaseOrderItem extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("product_id")
    private Long productId;

    @TableField("quantity")
    private BigDecimal quantity;

    @TableField("unit_price")
    private BigDecimal unitPrice;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("received_quantity")
    private BigDecimal receivedQuantity = BigDecimal.ZERO;

    @TableField("remarks")
    private String remarks;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void updateQuantity(BigDecimal newQuantity) {
        this.quantity = newQuantity;
        this.totalAmount = this.unitPrice.multiply(newQuantity);
    }
    
    public void updatePrice(BigDecimal newPrice) {
        this.unitPrice = newPrice;
        this.totalAmount = this.quantity.multiply(newPrice);
    }
    
    public BigDecimal getPendingQuantity() {
        return this.quantity.subtract(this.receivedQuantity);
    }
    
    public boolean isFullyReceived() {
        return this.receivedQuantity.compareTo(this.quantity) >= 0;
    }
}
```

### 2.3 采购订单服务实现

```java
@Service
@Transactional
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder> 
    implements PurchaseOrderService {
    
    @Autowired
    private SupplierService supplierService;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private WarehouseService warehouseService;
    
    @Autowired
    private PurchaseOrderNumberGenerator numberGenerator;
    
    @Override
    public PurchaseOrderDTO createPurchaseOrder(CreatePurchaseOrderRequest request) {
        // 1. 验证供应商
        Supplier supplier = supplierService.getById(request.getSupplierId());
        if (supplier == null || !supplier.isActive()) {
            throw new BusinessException("供应商不存在或已停用");
        }
        
        // 2. 验证仓库
        Warehouse warehouse = warehouseService.getById(request.getWarehouseId());
        if (warehouse == null || !warehouse.isActive()) {
            throw new BusinessException("仓库不存在或已停用");
        }
        
        // 3. 生成订单号
        String orderNumber = numberGenerator.generatePurchaseOrderNumber();
        
        // 4. 创建采购订单
        PurchaseOrder order = new PurchaseOrder();
        BeanUtils.copyProperties(request, order);
        order.setOrderNumber(orderNumber);
        order.setStatus(PurchaseOrderStatus.DRAFT);
        order.setOrderDate(LocalDate.now());
        
        // 5. 添加订单项
        for (CreatePurchaseOrderItemRequest itemRequest : request.getOrderItems()) {
            // 验证商品
            Product product = productService.getById(itemRequest.getProductId());
            if (product == null || !product.isActive()) {
                throw new BusinessException("商品不存在或已停用: " + itemRequest.getProductId());
            }
            
            PurchaseOrderItem item = new PurchaseOrderItem();
            BeanUtils.copyProperties(itemRequest, item);
            item.setTotalAmount(item.getQuantity().multiply(item.getUnitPrice()));
            order.addOrderItem(item);
        }
        
        // 6. 保存订单
        this.save(order);
        
        return convertToDTO(order);
    }
    
    @Override
    public void submitForApproval(Long orderId) {
        PurchaseOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("采购订单不存在");
        }
        
        if (order.getStatus() != PurchaseOrderStatus.DRAFT) {
            throw new BusinessException("只有草稿状态的订单才能提交审批");
        }
        
        // 验证订单项
        if (CollectionUtils.isEmpty(order.getOrderItems())) {
            throw new BusinessException("订单项不能为空");
        }
        
        order.setStatus(PurchaseOrderStatus.PENDING_APPROVAL);
        this.updateById(order);
        
        // 发送审批通知
        applicationEventPublisher.publishEvent(
            new PurchaseOrderSubmittedEvent(orderId, order.getTotalAmount())
        );
    }
    
    @Override
    public void approvePurchaseOrder(Long orderId, Long approverId) {
        PurchaseOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("采购订单不存在");
        }
        
        order.approve(approverId);
        this.updateById(order);
        
        // 发布审批通过事件
        applicationEventPublisher.publishEvent(
            new PurchaseOrderApprovedEvent(orderId, approverId)
        );
    }
    
    @Override
    public IPage<PurchaseOrderDTO> getPurchaseOrders(PurchaseOrderQueryRequest request) {
        Page<PurchaseOrder> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<PurchaseOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(request.getOrderNumber()), 
                    PurchaseOrder::getOrderNumber, request.getOrderNumber())
               .eq(request.getSupplierId() != null, 
                   PurchaseOrder::getSupplierId, request.getSupplierId())
               .eq(request.getStatus() != null, 
                   PurchaseOrder::getStatus, request.getStatus())
               .between(request.getStartDate() != null && request.getEndDate() != null,
                       PurchaseOrder::getOrderDate, request.getStartDate(), request.getEndDate())
               .orderByDesc(PurchaseOrder::getCreateTime);
        
        IPage<PurchaseOrder> orderPage = this.page(page, wrapper);
        return orderPage.convert(this::convertToDTO);
    }
}
```

## 3. 采购入库管理

### 3.1 采购入库实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_receipts")
public class PurchaseReceipt extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("receipt_number")
    private String receiptNumber;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("receipt_date")
    private LocalDate receiptDate;

    @TableField("status")
    @EnumValue
    private ReceiptStatus status;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("remarks")
    private String remarks;

    @TableField("received_by")
    private Long receivedBy;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法
    public void confirm(Long receiverId) {
        if (this.status != ReceiptStatus.PENDING) {
            throw new BusinessException("只有待确认状态的入库单才能确认");
        }
        this.status = ReceiptStatus.CONFIRMED;
        this.receivedBy = receiverId;
    }
}
```

## 4. 类图设计

### 4.1 采购管理核心类图

```mermaid
classDiagram
    class PurchaseOrder {
        +Long id
        +String orderNumber
        +Long supplierId
        +Long warehouseId
        +PurchaseOrderStatus status
        +LocalDate orderDate
        +LocalDate expectedDate
        +BigDecimal totalAmount
        +BigDecimal finalAmount
        +approve()
        +cancel()
        +addOrderItem()
        +removeOrderItem()
    }

    class PurchaseOrderItem {
        +Long id
        +Long purchaseOrderId
        +Long productId
        +BigDecimal quantity
        +BigDecimal unitPrice
        +BigDecimal totalAmount
        +BigDecimal receivedQuantity
        +updateQuantity()
        +updatePrice()
        +getPendingQuantity()
        +isFullyReceived()
    }

    class PurchaseReceipt {
        +Long id
        +String receiptNumber
        +Long purchaseOrderId
        +Long supplierId
        +Long warehouseId
        +ReceiptStatus status
        +LocalDate receiptDate
        +BigDecimal totalAmount
        +confirm()
    }

    class PurchaseReceiptItem {
        +Long id
        +Long purchaseReceiptId
        +Long purchaseOrderItemId
        +Long productId
        +BigDecimal orderedQuantity
        +BigDecimal receivedQuantity
        +BigDecimal unitPrice
        +QualityStatus qualityStatus
        +updateReceivedQuantity()
        +isFullyReceived()
    }

    class PurchaseOrderService {
        +createPurchaseOrder()
        +submitForApproval()
        +approvePurchaseOrder()
        +getPurchaseOrders()
    }

    class PurchaseReceiptService {
        +createPurchaseReceipt()
        +confirmReceipt()
        +getPurchaseReceipts()
    }

    PurchaseOrder --o PurchaseOrderItem
    PurchaseReceipt --o PurchaseReceiptItem
    PurchaseOrder --o PurchaseReceipt
    PurchaseOrderItem --o PurchaseReceiptItem

    PurchaseOrderService ..> PurchaseOrder
    PurchaseReceiptService ..> PurchaseReceipt
```

## 5. 时序图设计

### 5.1 采购订单创建时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as ShenYu网关
    participant PS as 采购服务
    participant SS as 供应商服务
    participant WS as 仓库服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 创建采购订单请求
    G->>PS: 转发请求

    PS->>SS: 验证供应商信息
    SS-->>PS: 返回供应商信息

    PS->>WS: 验证仓库信息
    WS-->>PS: 返回仓库信息

    PS->>PS: 生成订单号
    PS->>PS: 创建订单对象
    PS->>PS: 添加订单项

    PS->>DB: 保存采购订单
    DB-->>PS: 返回订单ID

    PS->>K: 发布订单创建事件
    K-->>PS: 确认事件发布

    PS-->>G: 返回创建结果
    G-->>U: 返回响应

    Note over K: 异步事件处理
    K->>PS: 订单创建通知事件
    K->>SS: 供应商通知事件
```

### 5.2 采购入库确认时序图

```mermaid
sequenceDiagram
    participant U as 仓库管理员
    participant G as ShenYu网关
    participant RS as 入库服务
    participant IS as 库存服务
    participant PS as 采购服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 确认入库请求
    G->>RS: 转发请求

    RS->>DB: 获取入库单信息
    DB-->>RS: 返回入库单

    RS->>RS: 验证入库状态
    RS->>RS: 确认入库

    RS->>DB: 更新入库单状态
    DB-->>RS: 确认更新

    RS->>K: 发布入库确认事件
    K-->>IS: 库存更新事件
    K-->>PS: 采购完成事件

    par 并行处理
        IS->>DB: 更新库存数量
        and
        PS->>DB: 更新订单收货数量
    end

    RS-->>G: 返回确认结果
    G-->>U: 返回响应

    Note over K: 异步库存更新
    IS->>K: 库存变动事件
    K->>RS: 库存更新完成通知
```

## 6. 事件驱动设计

### 6.1 采购领域事件

```java
// 采购订单创建事件
@Data
@AllArgsConstructor
public class PurchaseOrderCreatedEvent extends DomainEvent {
    private Long orderId;
    private String orderNumber;
    private Long supplierId;
    private BigDecimal totalAmount;
    private List<PurchaseOrderItemDTO> orderItems;
}

// 采购订单审批事件
@Data
@AllArgsConstructor
public class PurchaseOrderApprovedEvent extends DomainEvent {
    private Long orderId;
    private Long approverId;
    private LocalDateTime approvedAt;
}

// 采购入库确认事件
@Data
@AllArgsConstructor
public class PurchaseReceiptConfirmedEvent extends DomainEvent {
    private Long receiptId;
    private Long purchaseOrderId;
    private BigDecimal totalAmount;
    private List<PurchaseReceiptItemDTO> receiptItems;
}
```

### 6.2 Kafka事件处理

```java
@Component
public class PurchaseEventPublisher {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String PURCHASE_TOPIC = "purchase.events";

    public void publishOrderCreated(PurchaseOrderCreatedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "order.created", event);
    }

    public void publishOrderApproved(PurchaseOrderApprovedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "order.approved", event);
    }

    public void publishReceiptConfirmed(PurchaseReceiptConfirmedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "receipt.confirmed", event);
    }
}

@Component
@KafkaListener(topics = "purchase.events")
public class PurchaseEventHandler {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private NotificationService notificationService;

    @KafkaHandler
    public void handleOrderCreated(PurchaseOrderCreatedEvent event) {
        // 发送供应商通知
        notificationService.sendPurchaseOrderNotification(event);
    }

    @KafkaHandler
    public void handleReceiptConfirmed(PurchaseReceiptConfirmedEvent event) {
        // 更新库存
        for (PurchaseReceiptItemDTO item : event.getReceiptItems()) {
            inventoryService.increaseInventory(
                item.getProductId(),
                event.getWarehouseId(),
                item.getReceivedQuantity(),
                item.getUnitPrice(),
                "采购入库-" + event.getReceiptId()
            );
        }
    }
}
```

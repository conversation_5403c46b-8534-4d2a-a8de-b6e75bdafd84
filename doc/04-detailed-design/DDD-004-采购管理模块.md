# DDD-004 采购管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-004 |
| 文档名称 | 采购管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

采购管理模块负责管理企业的采购业务流程，包括采购订单管理、采购入库管理和采购退货管理。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "采购管理模块"
        A[采购订单服务]
        B[采购入库服务]
        C[采购退货服务]
    end
    
    subgraph "业务流程"
        D[需求计划] --> E[采购申请]
        E --> F[采购订单]
        F --> G[采购入库]
        G --> H[质量检验]
        H --> I[库存更新]
    end
    
    A --> F
    B --> G
    C --> J[退货处理]
```

## 2. 采购订单管理

### 2.1 采购订单实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_orders")
public class PurchaseOrder extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("order_number")
    private String orderNumber;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("status")
    @EnumValue
    private PurchaseOrderStatus status;

    @TableField("order_date")
    private LocalDate orderDate;

    @TableField("expected_date")
    private LocalDate expectedDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @TableField("tax_amount")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @TableField("final_amount")
    private BigDecimal finalAmount;

    @TableField("payment_terms")
    private Integer paymentTerms;

    @TableField("remarks")
    private String remarks;

    @TableField("approved_by")
    private Long approvedBy;

    @TableField("approved_at")
    private LocalDateTime approvedAt;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void addOrderItem(PurchaseOrderItem item) {
        item.setPurchaseOrder(this);
        this.orderItems.add(item);
        recalculateTotal();
    }
    
    public void removeOrderItem(PurchaseOrderItem item) {
        this.orderItems.remove(item);
        recalculateTotal();
    }
    
    public void approve(Long approverId) {
        if (this.status != PurchaseOrderStatus.PENDING_APPROVAL) {
            throw new BusinessException("订单状态不允许审批");
        }
        this.status = PurchaseOrderStatus.APPROVED;
        this.approvedBy = approverId;
        this.approvedAt = LocalDateTime.now();
    }
    
    public void cancel(String reason) {
        if (this.status == PurchaseOrderStatus.COMPLETED || 
            this.status == PurchaseOrderStatus.CANCELLED) {
            throw new BusinessException("订单状态不允许取消");
        }
        this.status = PurchaseOrderStatus.CANCELLED;
        this.remarks = reason;
    }
    
    private void recalculateTotal() {
        this.totalAmount = orderItems.stream()
            .map(PurchaseOrderItem::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.finalAmount = this.totalAmount
            .subtract(this.discountAmount)
            .add(this.taxAmount);
    }
}
```

### 2.2 采购订单项实体

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_order_items")
public class PurchaseOrderItem extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("product_id")
    private Long productId;

    @TableField("quantity")
    private BigDecimal quantity;

    @TableField("unit_price")
    private BigDecimal unitPrice;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("received_quantity")
    private BigDecimal receivedQuantity = BigDecimal.ZERO;

    @TableField("remarks")
    private String remarks;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void updateQuantity(BigDecimal newQuantity) {
        this.quantity = newQuantity;
        this.totalAmount = this.unitPrice.multiply(newQuantity);
    }
    
    public void updatePrice(BigDecimal newPrice) {
        this.unitPrice = newPrice;
        this.totalAmount = this.quantity.multiply(newPrice);
    }
    
    public BigDecimal getPendingQuantity() {
        return this.quantity.subtract(this.receivedQuantity);
    }
    
    public boolean isFullyReceived() {
        return this.receivedQuantity.compareTo(this.quantity) >= 0;
    }
}
```

### 2.3 采购订单服务实现

```java
@Service
@Transactional
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder> 
    implements PurchaseOrderService {
    
    @Autowired
    private SupplierService supplierService;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private WarehouseService warehouseService;
    
    @Autowired
    private PurchaseOrderNumberGenerator numberGenerator;
    
    @Override
    public PurchaseOrderDTO createPurchaseOrder(CreatePurchaseOrderRequest request) {
        // 1. 验证供应商
        Supplier supplier = supplierService.getById(request.getSupplierId());
        if (supplier == null || !supplier.isActive()) {
            throw new BusinessException("供应商不存在或已停用");
        }
        
        // 2. 验证仓库
        Warehouse warehouse = warehouseService.getById(request.getWarehouseId());
        if (warehouse == null || !warehouse.isActive()) {
            throw new BusinessException("仓库不存在或已停用");
        }
        
        // 3. 生成订单号
        String orderNumber = numberGenerator.generatePurchaseOrderNumber();
        
        // 4. 创建采购订单
        PurchaseOrder order = new PurchaseOrder();
        BeanUtils.copyProperties(request, order);
        order.setOrderNumber(orderNumber);
        order.setStatus(PurchaseOrderStatus.DRAFT);
        order.setOrderDate(LocalDate.now());
        
        // 5. 添加订单项
        for (CreatePurchaseOrderItemRequest itemRequest : request.getOrderItems()) {
            // 验证商品
            Product product = productService.getById(itemRequest.getProductId());
            if (product == null || !product.isActive()) {
                throw new BusinessException("商品不存在或已停用: " + itemRequest.getProductId());
            }
            
            PurchaseOrderItem item = new PurchaseOrderItem();
            BeanUtils.copyProperties(itemRequest, item);
            item.setTotalAmount(item.getQuantity().multiply(item.getUnitPrice()));
            order.addOrderItem(item);
        }
        
        // 6. 保存订单
        this.save(order);
        
        return convertToDTO(order);
    }
    
    @Override
    public void submitForApproval(Long orderId) {
        PurchaseOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("采购订单不存在");
        }
        
        if (order.getStatus() != PurchaseOrderStatus.DRAFT) {
            throw new BusinessException("只有草稿状态的订单才能提交审批");
        }
        
        // 验证订单项
        if (CollectionUtils.isEmpty(order.getOrderItems())) {
            throw new BusinessException("订单项不能为空");
        }
        
        order.setStatus(PurchaseOrderStatus.PENDING_APPROVAL);
        this.updateById(order);
        
        // 发送审批通知
        applicationEventPublisher.publishEvent(
            new PurchaseOrderSubmittedEvent(orderId, order.getTotalAmount())
        );
    }
    
    @Override
    public void approvePurchaseOrder(Long orderId, Long approverId) {
        PurchaseOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("采购订单不存在");
        }
        
        order.approve(approverId);
        this.updateById(order);
        
        // 发布审批通过事件
        applicationEventPublisher.publishEvent(
            new PurchaseOrderApprovedEvent(orderId, approverId)
        );
    }
    
    @Override
    public IPage<PurchaseOrderDTO> getPurchaseOrders(PurchaseOrderQueryRequest request) {
        Page<PurchaseOrder> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<PurchaseOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(request.getOrderNumber()), 
                    PurchaseOrder::getOrderNumber, request.getOrderNumber())
               .eq(request.getSupplierId() != null, 
                   PurchaseOrder::getSupplierId, request.getSupplierId())
               .eq(request.getStatus() != null, 
                   PurchaseOrder::getStatus, request.getStatus())
               .between(request.getStartDate() != null && request.getEndDate() != null,
                       PurchaseOrder::getOrderDate, request.getStartDate(), request.getEndDate())
               .orderByDesc(PurchaseOrder::getCreateTime);
        
        IPage<PurchaseOrder> orderPage = this.page(page, wrapper);
        return orderPage.convert(this::convertToDTO);
    }
}
```

## 3. 采购入库管理

### 3.1 采购入库实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_receipts")
public class PurchaseReceipt extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("receipt_number")
    private String receiptNumber;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("receipt_date")
    private LocalDate receiptDate;

    @TableField("status")
    @EnumValue
    private ReceiptStatus status;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("remarks")
    private String remarks;

    @TableField("received_by")
    private Long receivedBy;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法
    public void confirm(Long receiverId) {
        if (this.status != ReceiptStatus.PENDING) {
            throw new BusinessException("只有待确认状态的入库单才能确认");
        }
        this.status = ReceiptStatus.CONFIRMED;
        this.receivedBy = receiverId;
    }
}
```

## 4. 采购退货服务

### 4.1 退货服务概述

采购退货服务是采购管理模块的重要组成部分，负责处理采购商品的退货业务流程，包括退货申请、审批、处理、取货确认和退款等环节。

### 4.2 退货实体设计

#### 4.2.1 退货主表

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_returns")
public class PurchaseReturn extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("return_number")
    private String returnNumber;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("purchase_receipt_id")
    private Long purchaseReceiptId;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("return_type")
    @EnumValue
    private ReturnType returnType;

    @TableField("return_reason")
    @EnumValue
    private ReturnReason returnReason;

    @TableField("status")
    @EnumValue
    private ReturnStatus status;

    @TableField("return_date")
    private LocalDate returnDate;

    @TableField("expected_pickup_date")
    private LocalDate expectedPickupDate;

    @TableField("actual_pickup_date")
    private LocalDate actualPickupDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @TableField("handling_fee")
    private BigDecimal handlingFee = BigDecimal.ZERO;

    @TableField("description")
    private String description;

    @TableField("quality_report")
    private String qualityReport;

    @TableField("approved_by")
    private Long approvedBy;

    @TableField("approved_at")
    private LocalDateTime approvedAt;

    @TableField("processed_by")
    private Long processedBy;

    @TableField("processed_at")
    private LocalDateTime processedAt;

    @TableField("remarks")
    private String remarks;
}
```

#### 4.2.2 退货明细表

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_return_items")
public class PurchaseReturnItem extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("return_id")
    private Long returnId;

    @TableField("purchase_order_item_id")
    private Long purchaseOrderItemId;

    @TableField("purchase_receipt_item_id")
    private Long purchaseReceiptItemId;

    @TableField("product_id")
    private Long productId;

    @TableField("return_quantity")
    private BigDecimal returnQuantity;

    @TableField("unit_price")
    private BigDecimal unitPrice;

    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @TableField("batch_number")
    private String batchNumber;

    @TableField("production_date")
    private LocalDate productionDate;

    @TableField("expiry_date")
    private LocalDate expiryDate;

    @TableField("quality_status")
    @EnumValue
    private QualityStatus qualityStatus;

    @TableField("return_reason")
    private String returnReason;

    @TableField("quality_remarks")
    private String qualityRemarks;

    @TableField("remarks")
    private String remarks;
}
```

#### 4.2.3 退货相关枚举

```java
/**
 * 退货类型
 */
public enum ReturnType {
    QUALITY_ISSUE("质量问题"),
    QUANTITY_MISMATCH("数量不符"),
    SPECIFICATION_MISMATCH("规格不符"),
    TRANSPORT_DAMAGE("运输损坏"),
    COMMERCIAL_RETURN("商业退货"),
    EXPIRED_PRODUCT("过期商品"),
    OTHER("其他");

    private final String description;

    ReturnType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

/**
 * 退货原因
 */
public enum ReturnReason {
    DEFECTIVE_PRODUCT("产品缺陷"),
    WRONG_SPECIFICATION("规格错误"),
    DAMAGED_PACKAGING("包装损坏"),
    QUANTITY_ERROR("数量错误"),
    LATE_DELIVERY("交货延迟"),
    WRONG_PRODUCT("错误商品"),
    QUALITY_BELOW_STANDARD("质量不达标"),
    BUSINESS_DECISION("商业决策"),
    OTHER("其他原因");

    private final String description;

    ReturnReason(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

/**
 * 退货状态
 */
public enum ReturnStatus {
    DRAFT("草稿"),
    PENDING_APPROVAL("待审批"),
    APPROVED("已审批"),
    REJECTED("已拒绝"),
    IN_PROCESS("处理中"),
    PICKED_UP("已取货"),
    REFUNDED("已退款"),
    COMPLETED("已完成"),
    CANCELLED("已取消");

    private final String description;

    ReturnStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

/**
 * 质量状态
 */
public enum QualityStatus {
    GOOD("良好"),
    DAMAGED("损坏"),
    DEFECTIVE("缺陷"),
    EXPIRED("过期"),
    CONTAMINATED("污染"),
    UNUSABLE("不可用");

    private final String description;

    QualityStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
```

### 4.3 退货服务实现

```java
@Service
@Transactional
public class PurchaseReturnService {

    @Autowired
    private PurchaseReturnMapper returnMapper;

    @Autowired
    private PurchaseReturnItemMapper returnItemMapper;

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    @Autowired
    private PurchaseReceiptService purchaseReceiptService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private PurchaseEventPublisher eventPublisher;

    /**
     * 创建退货申请
     */
    public PurchaseReturn createReturn(CreateReturnRequest request) {
        // 1. 验证业务规则
        validateReturnRequest(request);

        // 2. 生成退货单号
        String returnNumber = generateReturnNumber();

        // 3. 创建退货主记录
        PurchaseReturn purchaseReturn = PurchaseReturn.builder()
            .returnNumber(returnNumber)
            .purchaseOrderId(request.getPurchaseOrderId())
            .purchaseReceiptId(request.getPurchaseReceiptId())
            .supplierId(request.getSupplierId())
            .warehouseId(request.getWarehouseId())
            .returnType(request.getReturnType())
            .returnReason(request.getReturnReason())
            .returnDate(request.getReturnDate())
            .expectedPickupDate(request.getExpectedPickupDate())
            .description(request.getDescription())
            .status(ReturnStatus.DRAFT)
            .build();

        returnMapper.insert(purchaseReturn);

        // 4. 创建退货明细
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CreateReturnItemRequest itemRequest : request.getItems()) {
            PurchaseReturnItem returnItem = createReturnItem(purchaseReturn.getId(), itemRequest);
            returnItemMapper.insert(returnItem);
            totalAmount = totalAmount.add(returnItem.getRefundAmount());
        }

        // 5. 更新总金额
        purchaseReturn.setTotalAmount(totalAmount);
        returnMapper.updateById(purchaseReturn);

        // 6. 发布创建事件
        eventPublisher.publishReturnEvent("RETURN_CREATED", purchaseReturn);

        return purchaseReturn;
    }

    /**
     * 提交审批
     */
    public void submitForApproval(Long returnId) {
        PurchaseReturn purchaseReturn = getById(returnId);

        // 验证状态
        if (purchaseReturn.getStatus() != ReturnStatus.DRAFT) {
            throw new BusinessException("只有草稿状态的退货单才能提交审批");
        }

        // 启动审批流程
        String processInstanceId = workflowService.startProcess(
            "purchase-return-approval",
            returnId.toString(),
            Map.of(
                "returnId", returnId,
                "totalAmount", purchaseReturn.getTotalAmount(),
                "returnType", purchaseReturn.getReturnType().name()
            )
        );

        // 更新状态
        purchaseReturn.setStatus(ReturnStatus.PENDING_APPROVAL);
        returnMapper.updateById(purchaseReturn);

        // 发布提交事件
        eventPublisher.publishReturnEvent("RETURN_SUBMITTED", purchaseReturn);
    }

    /**
     * 审批退货
     */
    public void approveReturn(Long returnId, Long approverId, String comments) {
        PurchaseReturn purchaseReturn = getById(returnId);

        // 验证状态
        if (purchaseReturn.getStatus() != ReturnStatus.PENDING_APPROVAL) {
            throw new BusinessException("只有待审批状态的退货单才能审批");
        }

        // 更新审批信息
        purchaseReturn.setStatus(ReturnStatus.APPROVED);
        purchaseReturn.setApprovedBy(approverId);
        purchaseReturn.setApprovedAt(LocalDateTime.now());
        purchaseReturn.setRemarks(comments);
        returnMapper.updateById(purchaseReturn);

        // 发布审批通过事件
        eventPublisher.publishReturnEvent("RETURN_APPROVED", purchaseReturn);
    }

    /**
     * 拒绝退货
     */
    public void rejectReturn(Long returnId, Long approverId, String reason) {
        PurchaseReturn purchaseReturn = getById(returnId);

        // 验证状态
        if (purchaseReturn.getStatus() != ReturnStatus.PENDING_APPROVAL) {
            throw new BusinessException("只有待审批状态的退货单才能拒绝");
        }

        // 更新拒绝信息
        purchaseReturn.setStatus(ReturnStatus.REJECTED);
        purchaseReturn.setApprovedBy(approverId);
        purchaseReturn.setApprovedAt(LocalDateTime.now());
        purchaseReturn.setRemarks(reason);
        returnMapper.updateById(purchaseReturn);

        // 发布拒绝事件
        eventPublisher.publishReturnEvent("RETURN_REJECTED", purchaseReturn);
    }

    /**
     * 处理退货
     */
    public void processReturn(Long returnId, ProcessReturnRequest request) {
        PurchaseReturn purchaseReturn = getById(returnId);

        // 验证状态
        if (purchaseReturn.getStatus() != ReturnStatus.APPROVED) {
            throw new BusinessException("只有已审批的退货单才能处理");
        }

        // 更新处理信息
        purchaseReturn.setStatus(ReturnStatus.IN_PROCESS);
        purchaseReturn.setProcessedBy(request.getProcessedBy());
        purchaseReturn.setProcessedAt(LocalDateTime.now());
        purchaseReturn.setQualityReport(request.getQualityReport());
        returnMapper.updateById(purchaseReturn);

        // 更新明细质量状态
        if (request.getItemQualities() != null) {
            for (ReturnItemQualityRequest qualityRequest : request.getItemQualities()) {
                PurchaseReturnItem returnItem = returnItemMapper.selectById(qualityRequest.getReturnItemId());
                returnItem.setQualityStatus(qualityRequest.getQualityStatus());
                returnItem.setQualityRemarks(qualityRequest.getQualityRemarks());
                returnItemMapper.updateById(returnItem);
            }
        }

        // 执行库存出库
        executeInventoryOutbound(purchaseReturn);

        // 发布处理事件
        eventPublisher.publishReturnEvent("RETURN_PROCESSED", purchaseReturn);
    }

    /**
     * 确认取货
     */
    public void confirmPickup(Long returnId, LocalDate actualPickupDate) {
        PurchaseReturn purchaseReturn = getById(returnId);

        // 验证状态
        if (purchaseReturn.getStatus() != ReturnStatus.IN_PROCESS) {
            throw new BusinessException("只有处理中的退货单才能确认取货");
        }

        // 更新取货信息
        purchaseReturn.setStatus(ReturnStatus.PICKED_UP);
        purchaseReturn.setActualPickupDate(actualPickupDate);
        returnMapper.updateById(purchaseReturn);

        // 发布取货事件
        eventPublisher.publishReturnEvent("RETURN_PICKED_UP", purchaseReturn);
    }

    /**
     * 处理退款
     */
    public void processRefund(Long returnId, ProcessRefundRequest request) {
        PurchaseReturn purchaseReturn = getById(returnId);

        // 验证状态
        if (purchaseReturn.getStatus() != ReturnStatus.PICKED_UP) {
            throw new BusinessException("只有已取货的退货单才能处理退款");
        }

        // 计算退款金额
        BigDecimal refundAmount = calculateRefundAmount(purchaseReturn, request);

        // 更新退款信息
        purchaseReturn.setStatus(ReturnStatus.REFUNDED);
        purchaseReturn.setRefundAmount(refundAmount);
        purchaseReturn.setHandlingFee(request.getHandlingFee());
        returnMapper.updateById(purchaseReturn);

        // 更新明细退款金额
        if (request.getItemRefunds() != null) {
            for (ReturnItemRefundRequest refundRequest : request.getItemRefunds()) {
                PurchaseReturnItem returnItem = returnItemMapper.selectById(refundRequest.getReturnItemId());
                returnItem.setRefundAmount(refundRequest.getRefundAmount());
                returnItem.setRemarks(refundRequest.getRefundRemarks());
                returnItemMapper.updateById(returnItem);
            }
        }

        // 创建财务退款记录
        financeService.createRefundRecord(purchaseReturn);

        // 发布退款事件
        eventPublisher.publishReturnEvent("RETURN_REFUNDED", purchaseReturn);
    }

    /**
     * 完成退货
     */
    public void completeReturn(Long returnId) {
        PurchaseReturn purchaseReturn = getById(returnId);

        // 验证状态
        if (purchaseReturn.getStatus() != ReturnStatus.REFUNDED) {
            throw new BusinessException("只有已退款的退货单才能完成");
        }

        // 更新状态
        purchaseReturn.setStatus(ReturnStatus.COMPLETED);
        returnMapper.updateById(purchaseReturn);

        // 发布完成事件
        eventPublisher.publishReturnEvent("RETURN_COMPLETED", purchaseReturn);
    }

    /**
     * 根据ID获取退货单
     */
    public PurchaseReturn getById(Long id) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(id);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }
        return purchaseReturn;
    }

    /**
     * 分页查询退货单
     */
    public PageResult<PurchaseReturn> queryReturns(ReturnQueryRequest request) {
        LambdaQueryWrapper<PurchaseReturn> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(request.getReturnNumber())) {
            queryWrapper.like(PurchaseReturn::getReturnNumber, request.getReturnNumber());
        }
        if (request.getSupplierId() != null) {
            queryWrapper.eq(PurchaseReturn::getSupplierId, request.getSupplierId());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq(PurchaseReturn::getStatus, request.getStatus());
        }
        if (request.getStartDate() != null) {
            queryWrapper.ge(PurchaseReturn::getReturnDate, request.getStartDate());
        }
        if (request.getEndDate() != null) {
            queryWrapper.le(PurchaseReturn::getReturnDate, request.getEndDate());
        }

        Page<PurchaseReturn> page = new Page<>(request.getPageNum(), request.getPageSize());
        Page<PurchaseReturn> returnPage = returnMapper.selectPage(page, queryWrapper);

        return PageResult.<PurchaseReturn>builder()
            .records(returnPage.getRecords())
            .total(returnPage.getTotal())
            .pageNum(request.getPageNum())
            .pageSize(request.getPageSize())
            .build();
    }

    // 私有辅助方法
    private void validateReturnRequest(CreateReturnRequest request) {
        // 验证采购订单是否存在
        PurchaseOrder purchaseOrder = purchaseOrderService.getById(request.getPurchaseOrderId());
        if (purchaseOrder == null) {
            throw new BusinessException("采购订单不存在");
        }

        // 验证采购入库单是否存在
        PurchaseReceipt purchaseReceipt = purchaseReceiptService.getById(request.getPurchaseReceiptId());
        if (purchaseReceipt == null) {
            throw new BusinessException("采购入库单不存在");
        }

        // 验证退货数量
        for (CreateReturnItemRequest itemRequest : request.getItems()) {
            validateReturnQuantity(itemRequest);
        }
    }

    private void validateReturnQuantity(CreateReturnItemRequest itemRequest) {
        // 获取已入库数量
        BigDecimal receivedQuantity = purchaseReceiptService.getReceivedQuantity(
            itemRequest.getPurchaseReceiptItemId()
        );

        // 获取已退货数量
        BigDecimal returnedQuantity = getReturnedQuantity(
            itemRequest.getPurchaseOrderItemId()
        );

        // 可退货数量 = 已入库数量 - 已退货数量
        BigDecimal availableQuantity = receivedQuantity.subtract(returnedQuantity);

        if (itemRequest.getReturnQuantity().compareTo(availableQuantity) > 0) {
            throw new BusinessException("退货数量超过可退货数量");
        }
    }

    private BigDecimal getReturnedQuantity(Long purchaseOrderItemId) {
        LambdaQueryWrapper<PurchaseReturnItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseReturnItem::getPurchaseOrderItemId, purchaseOrderItemId);

        List<PurchaseReturnItem> returnItems = returnItemMapper.selectList(queryWrapper);
        return returnItems.stream()
            .map(PurchaseReturnItem::getReturnQuantity)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private PurchaseReturnItem createReturnItem(Long returnId, CreateReturnItemRequest request) {
        BigDecimal refundAmount = request.getReturnQuantity().multiply(request.getUnitPrice());

        return PurchaseReturnItem.builder()
            .returnId(returnId)
            .purchaseOrderItemId(request.getPurchaseOrderItemId())
            .purchaseReceiptItemId(request.getPurchaseReceiptItemId())
            .productId(request.getProductId())
            .returnQuantity(request.getReturnQuantity())
            .unitPrice(request.getUnitPrice())
            .refundAmount(refundAmount)
            .batchNumber(request.getBatchNumber())
            .productionDate(request.getProductionDate())
            .expiryDate(request.getExpiryDate())
            .returnReason(request.getReturnReason())
            .remarks(request.getRemarks())
            .build();
    }

    private String generateReturnNumber() {
        return "RT" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))
            + String.format("%06d", System.currentTimeMillis() % 1000000);
    }

    private void executeInventoryOutbound(PurchaseReturn purchaseReturn) {
        List<PurchaseReturnItem> returnItems = getReturnItems(purchaseReturn.getId());

        for (PurchaseReturnItem item : returnItems) {
            inventoryService.outbound(InventoryOutboundRequest.builder()
                .warehouseId(purchaseReturn.getWarehouseId())
                .productId(item.getProductId())
                .quantity(item.getReturnQuantity())
                .batchNumber(item.getBatchNumber())
                .businessType("PURCHASE_RETURN")
                .businessId(purchaseReturn.getId())
                .build());
        }
    }

    private List<PurchaseReturnItem> getReturnItems(Long returnId) {
        LambdaQueryWrapper<PurchaseReturnItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseReturnItem::getReturnId, returnId);
        return returnItemMapper.selectList(queryWrapper);
    }

    private BigDecimal calculateRefundAmount(PurchaseReturn purchaseReturn, ProcessRefundRequest request) {
        BigDecimal totalRefund = BigDecimal.ZERO;

        if (request.getItemRefunds() != null) {
            totalRefund = request.getItemRefunds().stream()
                .map(ReturnItemRefundRequest::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            totalRefund = purchaseReturn.getTotalAmount();
        }

        return totalRefund.subtract(request.getHandlingFee());
    }
}
```

### 4.4 退货控制器

```java
@RestController
@RequestMapping("/api/purchase/returns")
@Validated
public class PurchaseReturnController {

    @Autowired
    private PurchaseReturnService returnService;

    /**
     * 创建退货申请
     */
    @PostMapping
    public Result<PurchaseReturn> createReturn(@Valid @RequestBody CreateReturnRequest request) {
        PurchaseReturn purchaseReturn = returnService.createReturn(request);
        return Result.success(purchaseReturn);
    }

    /**
     * 提交审批
     */
    @PostMapping("/{id}/submit")
    public Result<Void> submitForApproval(@PathVariable Long id) {
        returnService.submitForApproval(id);
        return Result.success();
    }

    /**
     * 审批退货
     */
    @PostMapping("/{id}/approve")
    public Result<Void> approveReturn(
            @PathVariable Long id,
            @Valid @RequestBody ApprovalRequest request) {
        returnService.approveReturn(id, request.getApproverId(), request.getComments());
        return Result.success();
    }

    /**
     * 拒绝退货
     */
    @PostMapping("/{id}/reject")
    public Result<Void> rejectReturn(
            @PathVariable Long id,
            @Valid @RequestBody RejectionRequest request) {
        returnService.rejectReturn(id, request.getApproverId(), request.getReason());
        return Result.success();
    }

    /**
     * 处理退货
     */
    @PostMapping("/{id}/process")
    public Result<Void> processReturn(
            @PathVariable Long id,
            @Valid @RequestBody ProcessReturnRequest request) {
        returnService.processReturn(id, request);
        return Result.success();
    }

    /**
     * 确认取货
     */
    @PostMapping("/{id}/pickup")
    public Result<Void> confirmPickup(
            @PathVariable Long id,
            @Valid @RequestBody PickupRequest request) {
        returnService.confirmPickup(id, request.getActualPickupDate());
        return Result.success();
    }

    /**
     * 处理退款
     */
    @PostMapping("/{id}/refund")
    public Result<Void> processRefund(
            @PathVariable Long id,
            @Valid @RequestBody ProcessRefundRequest request) {
        returnService.processRefund(id, request);
        return Result.success();
    }

    /**
     * 完成退货
     */
    @PostMapping("/{id}/complete")
    public Result<Void> completeReturn(@PathVariable Long id) {
        returnService.completeReturn(id);
        return Result.success();
    }

    /**
     * 查询退货单详情
     */
    @GetMapping("/{id}")
    public Result<PurchaseReturn> getReturn(@PathVariable Long id) {
        PurchaseReturn purchaseReturn = returnService.getById(id);
        return Result.success(purchaseReturn);
    }

    /**
     * 分页查询退货单
     */
    @GetMapping
    public Result<PageResult<PurchaseReturn>> queryReturns(
            @Valid ReturnQueryRequest request) {
        PageResult<PurchaseReturn> result = returnService.queryReturns(request);
        return Result.success(result);
    }
}
```

### 4.5 数据传输对象

```java
/**
 * 创建退货申请请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateReturnRequest {

    @NotNull(message = "采购订单ID不能为空")
    private Long purchaseOrderId;

    @NotNull(message = "采购入库单ID不能为空")
    private Long purchaseReceiptId;

    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @NotNull(message = "退货类型不能为空")
    private ReturnType returnType;

    @NotNull(message = "退货原因不能为空")
    private ReturnReason returnReason;

    @NotNull(message = "退货日期不能为空")
    private LocalDate returnDate;

    private LocalDate expectedPickupDate;

    @Size(max = 500, message = "描述长度不能超过500字符")
    private String description;

    @NotEmpty(message = "退货明细不能为空")
    @Valid
    private List<CreateReturnItemRequest> items;
}

/**
 * 创建退货明细请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateReturnItemRequest {

    @NotNull(message = "采购订单明细ID不能为空")
    private Long purchaseOrderItemId;

    @NotNull(message = "采购入库明细ID不能为空")
    private Long purchaseReceiptItemId;

    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @NotNull(message = "退货数量不能为空")
    @DecimalMin(value = "0.001", message = "退货数量必须大于0")
    private BigDecimal returnQuantity;

    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0", message = "单价不能为负数")
    private BigDecimal unitPrice;

    private String batchNumber;

    private LocalDate productionDate;

    private LocalDate expiryDate;

    @Size(max = 200, message = "退货原因长度不能超过200字符")
    private String returnReason;

    @Size(max = 200, message = "备注长度不能超过200字符")
    private String remarks;
}

/**
 * 审批请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalRequest {

    @NotNull(message = "审批人不能为空")
    private Long approverId;

    @Size(max = 500, message = "审批意见长度不能超过500字符")
    private String comments;
}

/**
 * 拒绝请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RejectionRequest {

    @NotNull(message = "审批人不能为空")
    private Long approverId;

    @NotBlank(message = "拒绝原因不能为空")
    @Size(max = 500, message = "拒绝原因长度不能超过500字符")
    private String reason;
}

/**
 * 处理退货请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessReturnRequest {

    @NotNull(message = "处理人不能为空")
    private Long processedBy;

    @Size(max = 1000, message = "质量报告长度不能超过1000字符")
    private String qualityReport;

    @Valid
    private List<ReturnItemQualityRequest> itemQualities;
}

/**
 * 退货明细质量状态请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnItemQualityRequest {

    @NotNull(message = "退货明细ID不能为空")
    private Long returnItemId;

    @NotNull(message = "质量状态不能为空")
    private QualityStatus qualityStatus;

    @Size(max = 200, message = "质量备注长度不能超过200字符")
    private String qualityRemarks;
}

/**
 * 取货请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PickupRequest {

    @NotNull(message = "实际取货日期不能为空")
    private LocalDate actualPickupDate;
}

/**
 * 处理退款请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessRefundRequest {

    @DecimalMin(value = "0", message = "手续费不能为负数")
    private BigDecimal handlingFee = BigDecimal.ZERO;

    @Size(max = 200, message = "退款备注长度不能超过200字符")
    private String refundRemarks;

    @Valid
    private List<ReturnItemRefundRequest> itemRefunds;
}

/**
 * 退货明细退款请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnItemRefundRequest {

    @NotNull(message = "退货明细ID不能为空")
    private Long returnItemId;

    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0", message = "退款金额不能为负数")
    private BigDecimal refundAmount;

    @Size(max = 200, message = "退款备注长度不能超过200字符")
    private String refundRemarks;
}

/**
 * 退货查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnQueryRequest extends PageRequest {

    private String returnNumber;

    private Long supplierId;

    private ReturnStatus status;

    private LocalDate startDate;

    private LocalDate endDate;
}
```

### 4.6 退货事件处理

```java
/**
 * 退货事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnEvent {
    private String eventType;
    private Long returnId;
    private String returnNumber;
    private Long supplierId;
    private Long warehouseId;
    private ReturnStatus status;
    private BigDecimal totalAmount;
    private BigDecimal refundAmount;
    private LocalDateTime eventTime;
}

/**
 * 退货事件发布器
 */
@Component
public class PurchaseEventPublisher {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    public void publishReturnEvent(String eventType, PurchaseReturn purchaseReturn) {
        ReturnEvent event = ReturnEvent.builder()
            .eventType(eventType)
            .returnId(purchaseReturn.getId())
            .returnNumber(purchaseReturn.getReturnNumber())
            .supplierId(purchaseReturn.getSupplierId())
            .warehouseId(purchaseReturn.getWarehouseId())
            .status(purchaseReturn.getStatus())
            .totalAmount(purchaseReturn.getTotalAmount())
            .refundAmount(purchaseReturn.getRefundAmount())
            .eventTime(LocalDateTime.now())
            .build();

        rocketMQTemplate.convertAndSend("purchase-return-topic", event);
    }
}

/**
 * 退货事件监听器
 */
@Component
@RocketMQMessageListener(
    topic = "purchase-return-topic",
    consumerGroup = "purchase-return-consumer"
)
public class ReturnEventListener implements RocketMQListener<ReturnEvent> {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private ReportService reportService;

    @Override
    public void onMessage(ReturnEvent event) {
        try {
            switch (event.getEventType()) {
                case "RETURN_CREATED":
                    handleReturnCreated(event);
                    break;
                case "RETURN_SUBMITTED":
                    handleReturnSubmitted(event);
                    break;
                case "RETURN_APPROVED":
                    handleReturnApproved(event);
                    break;
                case "RETURN_REJECTED":
                    handleReturnRejected(event);
                    break;
                case "RETURN_PROCESSED":
                    handleReturnProcessed(event);
                    break;
                case "RETURN_PICKED_UP":
                    handleReturnPickedUp(event);
                    break;
                case "RETURN_REFUNDED":
                    handleReturnRefunded(event);
                    break;
                case "RETURN_COMPLETED":
                    handleReturnCompleted(event);
                    break;
                default:
                    log.warn("Unknown return event type: {}", event.getEventType());
            }
        } catch (Exception e) {
            log.error("Failed to handle return event: {}", event, e);
        }
    }

    private void handleReturnCreated(ReturnEvent event) {
        // 发送创建通知
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_CREATED")
                .title("退货申请已创建")
                .content(String.format("退货单 %s 已创建，等待提交审批", event.getReturnNumber()))
                .recipients(getReturnStakeholders(event.getReturnId()))
                .build()
        );

        // 更新统计数据
        reportService.updateReturnStatistics("CREATED", event);
    }

    private void handleReturnSubmitted(ReturnEvent event) {
        // 通知审批人
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_APPROVAL_REQUIRED")
                .title("退货申请待审批")
                .content(String.format("退货单 %s 已提交，请及时审批", event.getReturnNumber()))
                .recipients(getApprovers())
                .priority(NotificationPriority.HIGH)
                .build()
        );
    }

    private void handleReturnApproved(ReturnEvent event) {
        // 通知申请人
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_APPROVED")
                .title("退货申请已审批通过")
                .content(String.format("退货单 %s 已审批通过，可以开始处理", event.getReturnNumber()))
                .recipients(getReturnStakeholders(event.getReturnId()))
                .build()
        );

        // 通知供应商
        supplierService.notifySupplier(
            event.getSupplierId(),
            "退货通知",
            String.format("您的退货申请 %s 已审批通过，请安排取货", event.getReturnNumber())
        );
    }

    private void handleReturnCompleted(ReturnEvent event) {
        // 发送完成通知
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_COMPLETED")
                .title("退货流程已完成")
                .content(String.format("退货单 %s 已完成，退款金额：%s",
                    event.getReturnNumber(),
                    event.getRefundAmount()))
                .recipients(getReturnStakeholders(event.getReturnId()))
                .build()
        );

        // 更新供应商评价
        supplierService.updateSupplierRating(event.getSupplierId(), "RETURN_COMPLETED");

        // 生成退货报告
        reportService.generateReturnReport(event.getReturnId());
    }

    private List<String> getReturnStakeholders(Long returnId) {
        return Arrays.asList("<EMAIL>", "<EMAIL>");
    }

    private List<String> getApprovers() {
        return Arrays.asList("<EMAIL>");
    }
}
```

### 4.7 退货流程图

#### 4.7.1 退货状态流转图

```mermaid
stateDiagram-v2
    [*] --> DRAFT: 创建退货申请
    DRAFT --> PENDING_APPROVAL: 提交审批
    DRAFT --> CANCELLED: 取消申请

    PENDING_APPROVAL --> APPROVED: 审批通过
    PENDING_APPROVAL --> REJECTED: 审批拒绝
    PENDING_APPROVAL --> CANCELLED: 取消申请

    APPROVED --> IN_PROCESS: 开始处理
    APPROVED --> CANCELLED: 取消申请

    IN_PROCESS --> PICKED_UP: 确认取货

    PICKED_UP --> REFUNDED: 处理退款

    REFUNDED --> COMPLETED: 完成退货

    REJECTED --> [*]
    CANCELLED --> [*]
    COMPLETED --> [*]
```

#### 4.7.2 退货业务流程图

```mermaid
flowchart TD
    A[创建退货申请] --> B{验证业务规则}
    B -->|验证失败| C[返回错误信息]
    B -->|验证通过| D[生成退货单]

    D --> E[提交审批]
    E --> F{审批流程}
    F -->|审批通过| G[通知相关人员]
    F -->|审批拒绝| H[结束流程]

    G --> I[开始处理退货]
    I --> J[质量检验]
    J --> K[库存出库]
    K --> L[通知供应商取货]

    L --> M[确认取货]
    M --> N[计算退款金额]
    N --> O[处理退款]
    O --> P[完成退货]

    C --> Q[结束]
    H --> Q
    P --> Q
```

## 5. 类图设计

### 5.1 采购管理核心类图

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as ShenYu网关
    participant PS as 采购服务
    participant SS as 供应商服务
    participant WS as 仓库服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 创建采购订单请求
    G->>PS: 转发请求

    PS->>SS: 验证供应商信息
    SS-->>PS: 返回供应商信息

    PS->>WS: 验证仓库信息
    WS-->>PS: 返回仓库信息

    PS->>PS: 生成订单号
    PS->>PS: 创建订单对象
    PS->>PS: 添加订单项

    PS->>DB: 保存采购订单
    DB-->>PS: 返回订单ID

    PS->>K: 发布订单创建事件
    K-->>PS: 确认事件发布

    PS-->>G: 返回创建结果
    G-->>U: 返回响应

    Note over K: 异步事件处理
    K->>PS: 订单创建通知事件
    K->>SS: 供应商通知事件
```

### 5.2 采购入库确认时序图

```mermaid
sequenceDiagram
    participant U as 仓库管理员
    participant G as ShenYu网关
    participant RS as 入库服务
    participant IS as 库存服务
    participant PS as 采购服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 确认入库请求
    G->>RS: 转发请求

    RS->>DB: 获取入库单信息
    DB-->>RS: 返回入库单

    RS->>RS: 验证入库状态
    RS->>RS: 确认入库

    RS->>DB: 更新入库单状态
    DB-->>RS: 确认更新

    RS->>K: 发布入库确认事件
    K-->>IS: 库存更新事件
    K-->>PS: 采购完成事件

    par 并行处理
        IS->>DB: 更新库存数量
        and
        PS->>DB: 更新订单收货数量
    end

    RS-->>G: 返回确认结果
    G-->>U: 返回响应

    Note over K: 异步库存更新
    IS->>K: 库存变动事件
    K->>RS: 库存更新完成通知
```

## 6. 时序图设计

### 6.1 采购订单创建时序图

```java
// 采购订单创建事件
@Data
@AllArgsConstructor
public class PurchaseOrderCreatedEvent extends DomainEvent {
    private Long orderId;
    private String orderNumber;
    private Long supplierId;
    private BigDecimal totalAmount;
    private List<PurchaseOrderItemDTO> orderItems;
}

// 采购订单审批事件
@Data
@AllArgsConstructor
public class PurchaseOrderApprovedEvent extends DomainEvent {
    private Long orderId;
    private Long approverId;
    private LocalDateTime approvedAt;
}

// 采购入库确认事件
@Data
@AllArgsConstructor
public class PurchaseReceiptConfirmedEvent extends DomainEvent {
    private Long receiptId;
    private Long purchaseOrderId;
    private BigDecimal totalAmount;
    private List<PurchaseReceiptItemDTO> receiptItems;
}
```

### 6.2 Kafka事件处理

```java
@Component
public class PurchaseEventPublisher {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String PURCHASE_TOPIC = "purchase.events";

    public void publishOrderCreated(PurchaseOrderCreatedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "order.created", event);
    }

    public void publishOrderApproved(PurchaseOrderApprovedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "order.approved", event);
    }

    public void publishReceiptConfirmed(PurchaseReceiptConfirmedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "receipt.confirmed", event);
    }
}

@Component
@KafkaListener(topics = "purchase.events")
public class PurchaseEventHandler {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private NotificationService notificationService;

    @KafkaHandler
    public void handleOrderCreated(PurchaseOrderCreatedEvent event) {
        // 发送供应商通知
        notificationService.sendPurchaseOrderNotification(event);
    }

    @KafkaHandler
    public void handleReceiptConfirmed(PurchaseReceiptConfirmedEvent event) {
        // 更新库存
        for (PurchaseReceiptItemDTO item : event.getReceiptItems()) {
            inventoryService.increaseInventory(
                item.getProductId(),
                event.getWarehouseId(),
                item.getReceivedQuantity(),
                item.getUnitPrice(),
                "采购入库-" + event.getReceiptId()
            );
        }
    }
}
```

## 7. 事件驱动设计

### 7.1 采购领域事件

```java
// 采购订单创建事件
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderCreatedEvent {
    private Long orderId;
    private String orderNumber;
    private Long supplierId;
    private Long warehouseId;
    private BigDecimal totalAmount;
    private LocalDateTime eventTime;
}

// 采购订单审批事件
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderApprovedEvent {
    private Long orderId;
    private String orderNumber;
    private Long approverId;
    private LocalDateTime approvedTime;
}

// 采购入库事件
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseReceiptCreatedEvent {
    private Long receiptId;
    private String receiptNumber;
    private Long orderId;
    private Long warehouseId;
    private BigDecimal totalQuantity;
    private LocalDateTime eventTime;
}

// 退货事件
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnEvent {
    private String eventType;
    private Long returnId;
    private String returnNumber;
    private Long supplierId;
    private Long warehouseId;
    private ReturnStatus status;
    private BigDecimal totalAmount;
    private BigDecimal refundAmount;
    private LocalDateTime eventTime;
}
```

### 7.2 事件发布器

```java
@Component
public class PurchaseEventPublisher {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 发布采购订单创建事件
     */
    public void publishOrderCreated(PurchaseOrder order) {
        PurchaseOrderCreatedEvent event = PurchaseOrderCreatedEvent.builder()
            .orderId(order.getId())
            .orderNumber(order.getOrderNumber())
            .supplierId(order.getSupplierId())
            .warehouseId(order.getWarehouseId())
            .totalAmount(order.getFinalAmount())
            .eventTime(LocalDateTime.now())
            .build();

        rocketMQTemplate.convertAndSend("purchase-order-topic", event);
    }

    /**
     * 发布采购订单审批事件
     */
    public void publishOrderApproved(PurchaseOrder order) {
        PurchaseOrderApprovedEvent event = PurchaseOrderApprovedEvent.builder()
            .orderId(order.getId())
            .orderNumber(order.getOrderNumber())
            .approverId(order.getApprovedBy())
            .approvedTime(order.getApprovedAt())
            .build();

        rocketMQTemplate.convertAndSend("purchase-approval-topic", event);
    }

    /**
     * 发布采购入库事件
     */
    public void publishReceiptCreated(PurchaseReceipt receipt) {
        PurchaseReceiptCreatedEvent event = PurchaseReceiptCreatedEvent.builder()
            .receiptId(receipt.getId())
            .receiptNumber(receipt.getReceiptNumber())
            .orderId(receipt.getPurchaseOrderId())
            .warehouseId(receipt.getWarehouseId())
            .totalQuantity(receipt.getTotalQuantity())
            .eventTime(LocalDateTime.now())
            .build();

        rocketMQTemplate.convertAndSend("purchase-receipt-topic", event);
    }

    /**
     * 发布退货事件
     */
    public void publishReturnEvent(String eventType, PurchaseReturn purchaseReturn) {
        ReturnEvent event = ReturnEvent.builder()
            .eventType(eventType)
            .returnId(purchaseReturn.getId())
            .returnNumber(purchaseReturn.getReturnNumber())
            .supplierId(purchaseReturn.getSupplierId())
            .warehouseId(purchaseReturn.getWarehouseId())
            .status(purchaseReturn.getStatus())
            .totalAmount(purchaseReturn.getTotalAmount())
            .refundAmount(purchaseReturn.getRefundAmount())
            .eventTime(LocalDateTime.now())
            .build();

        rocketMQTemplate.convertAndSend("purchase-return-topic", event);
    }
}
```

### 7.3 事件监听器

```java
@Component
@RocketMQMessageListener(
    topic = "purchase-order-topic",
    consumerGroup = "purchase-order-consumer"
)
public class PurchaseOrderEventListener implements RocketMQListener<PurchaseOrderCreatedEvent> {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private NotificationService notificationService;

    @Override
    public void onMessage(PurchaseOrderCreatedEvent event) {
        try {
            // 预留库存
            inventoryService.reserveInventory(event.getOrderId());

            // 发送通知
            notificationService.sendOrderCreatedNotification(event);

            log.info("Purchase order created event processed: {}", event.getOrderNumber());
        } catch (Exception e) {
            log.error("Failed to process purchase order created event: {}", event, e);
        }
    }
}
```

## 8. 总结

采购管理模块作为PISP系统的核心业务模块，提供了完整的采购业务流程管理能力：

### 8.1 核心功能特性

1. **采购订单管理**：
   - 完整的订单生命周期管理
   - 多级审批工作流
   - 供应商协同管理
   - 订单状态实时跟踪

2. **采购入库管理**：
   - 精确的入库数量控制
   - 质量检验流程
   - 批次和有效期管理
   - 库存实时更新

3. **采购退货服务**：
   - 多种退货类型支持
   - 智能退货决策流程
   - 完整的退款处理
   - 供应商协调机制

### 8.2 技术架构优势

1. **分层架构设计**：
   - 清晰的职责分离
   - 高内聚低耦合
   - 易于维护和扩展

2. **事件驱动架构**：
   - 异步事件处理
   - 系统解耦
   - 高可用性保障

3. **Spring 6技术栈**：
   - HTTP Interface声明式客户端
   - RestClient同步HTTP调用
   - 现代化的技术架构

4. **RocketMQ消息中间件**：
   - 可靠的消息传递
   - 事务消息支持
   - 高性能异步处理

### 8.3 业务价值

通过完善的采购管理模块设计，PISP系统能够：

1. **提高采购效率**：标准化的采购流程，减少人工干预
2. **降低采购成本**：智能化的供应商管理和价格控制
3. **保证商品质量**：完整的质量检验和退货处理机制
4. **增强数据透明度**：实时的采购数据分析和报表
5. **提升供应商关系**：协同化的供应商管理平台

这些设计使得采购管理模块具备了企业级的采购管理能力，能够有效支持复杂的采购业务场景和合规要求。

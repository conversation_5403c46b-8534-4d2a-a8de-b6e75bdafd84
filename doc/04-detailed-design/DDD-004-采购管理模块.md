# DDD-004 采购管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-004 |
| 文档名称 | 采购管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

采购管理模块负责管理企业的采购业务流程，包括采购订单管理、采购入库管理和采购退货管理。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "采购管理模块"
        A[采购订单服务]
        B[采购入库服务]
        C[采购退货服务]
    end
    
    subgraph "业务流程"
        D[需求计划] --> E[采购申请]
        E --> F[采购订单]
        F --> G[采购入库]
        G --> H[质量检验]
        H --> I[库存更新]
    end
    
    A --> F
    B --> G
    C --> J[退货处理]
```

## 2. 采购订单管理

### 2.1 采购订单实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_orders")
public class PurchaseOrder extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("order_number")
    private String orderNumber;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("status")
    @EnumValue
    private PurchaseOrderStatus status;

    @TableField("order_date")
    private LocalDate orderDate;

    @TableField("expected_date")
    private LocalDate expectedDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @TableField("tax_amount")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @TableField("final_amount")
    private BigDecimal finalAmount;

    @TableField("payment_terms")
    private Integer paymentTerms;

    @TableField("remarks")
    private String remarks;

    @TableField("approved_by")
    private Long approvedBy;

    @TableField("approved_at")
    private LocalDateTime approvedAt;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void addOrderItem(PurchaseOrderItem item) {
        item.setPurchaseOrder(this);
        this.orderItems.add(item);
        recalculateTotal();
    }
    
    public void removeOrderItem(PurchaseOrderItem item) {
        this.orderItems.remove(item);
        recalculateTotal();
    }
    
    public void approve(Long approverId) {
        if (this.status != PurchaseOrderStatus.PENDING_APPROVAL) {
            throw new BusinessException("订单状态不允许审批");
        }
        this.status = PurchaseOrderStatus.APPROVED;
        this.approvedBy = approverId;
        this.approvedAt = LocalDateTime.now();
    }
    
    public void cancel(String reason) {
        if (this.status == PurchaseOrderStatus.COMPLETED || 
            this.status == PurchaseOrderStatus.CANCELLED) {
            throw new BusinessException("订单状态不允许取消");
        }
        this.status = PurchaseOrderStatus.CANCELLED;
        this.remarks = reason;
    }
    
    private void recalculateTotal() {
        this.totalAmount = orderItems.stream()
            .map(PurchaseOrderItem::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.finalAmount = this.totalAmount
            .subtract(this.discountAmount)
            .add(this.taxAmount);
    }
}
```

### 2.2 采购订单项实体

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_order_items")
public class PurchaseOrderItem extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("product_id")
    private Long productId;

    @TableField("quantity")
    private BigDecimal quantity;

    @TableField("unit_price")
    private BigDecimal unitPrice;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("received_quantity")
    private BigDecimal receivedQuantity = BigDecimal.ZERO;

    @TableField("remarks")
    private String remarks;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // 业务方法
    public void updateQuantity(BigDecimal newQuantity) {
        this.quantity = newQuantity;
        this.totalAmount = this.unitPrice.multiply(newQuantity);
    }
    
    public void updatePrice(BigDecimal newPrice) {
        this.unitPrice = newPrice;
        this.totalAmount = this.quantity.multiply(newPrice);
    }
    
    public BigDecimal getPendingQuantity() {
        return this.quantity.subtract(this.receivedQuantity);
    }
    
    public boolean isFullyReceived() {
        return this.receivedQuantity.compareTo(this.quantity) >= 0;
    }
}
```

### 2.3 采购订单服务实现

```java
@Service
@Transactional
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder> 
    implements PurchaseOrderService {
    
    @Autowired
    private SupplierService supplierService;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private WarehouseService warehouseService;
    
    @Autowired
    private PurchaseOrderNumberGenerator numberGenerator;
    
    @Override
    public PurchaseOrderDTO createPurchaseOrder(CreatePurchaseOrderRequest request) {
        // 1. 验证供应商
        Supplier supplier = supplierService.getById(request.getSupplierId());
        if (supplier == null || !supplier.isActive()) {
            throw new BusinessException("供应商不存在或已停用");
        }
        
        // 2. 验证仓库
        Warehouse warehouse = warehouseService.getById(request.getWarehouseId());
        if (warehouse == null || !warehouse.isActive()) {
            throw new BusinessException("仓库不存在或已停用");
        }
        
        // 3. 生成订单号
        String orderNumber = numberGenerator.generatePurchaseOrderNumber();
        
        // 4. 创建采购订单
        PurchaseOrder order = new PurchaseOrder();
        BeanUtils.copyProperties(request, order);
        order.setOrderNumber(orderNumber);
        order.setStatus(PurchaseOrderStatus.DRAFT);
        order.setOrderDate(LocalDate.now());
        
        // 5. 添加订单项
        for (CreatePurchaseOrderItemRequest itemRequest : request.getOrderItems()) {
            // 验证商品
            Product product = productService.getById(itemRequest.getProductId());
            if (product == null || !product.isActive()) {
                throw new BusinessException("商品不存在或已停用: " + itemRequest.getProductId());
            }
            
            PurchaseOrderItem item = new PurchaseOrderItem();
            BeanUtils.copyProperties(itemRequest, item);
            item.setTotalAmount(item.getQuantity().multiply(item.getUnitPrice()));
            order.addOrderItem(item);
        }
        
        // 6. 保存订单
        this.save(order);
        
        return convertToDTO(order);
    }
    
    @Override
    public void submitForApproval(Long orderId) {
        PurchaseOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("采购订单不存在");
        }
        
        if (order.getStatus() != PurchaseOrderStatus.DRAFT) {
            throw new BusinessException("只有草稿状态的订单才能提交审批");
        }
        
        // 验证订单项
        if (CollectionUtils.isEmpty(order.getOrderItems())) {
            throw new BusinessException("订单项不能为空");
        }
        
        order.setStatus(PurchaseOrderStatus.PENDING_APPROVAL);
        this.updateById(order);
        
        // 发送审批通知
        applicationEventPublisher.publishEvent(
            new PurchaseOrderSubmittedEvent(orderId, order.getTotalAmount())
        );
    }
    
    @Override
    public void approvePurchaseOrder(Long orderId, Long approverId) {
        PurchaseOrder order = this.getById(orderId);
        if (order == null) {
            throw new BusinessException("采购订单不存在");
        }
        
        order.approve(approverId);
        this.updateById(order);
        
        // 发布审批通过事件
        applicationEventPublisher.publishEvent(
            new PurchaseOrderApprovedEvent(orderId, approverId)
        );
    }
    
    @Override
    public IPage<PurchaseOrderDTO> getPurchaseOrders(PurchaseOrderQueryRequest request) {
        Page<PurchaseOrder> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<PurchaseOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(request.getOrderNumber()), 
                    PurchaseOrder::getOrderNumber, request.getOrderNumber())
               .eq(request.getSupplierId() != null, 
                   PurchaseOrder::getSupplierId, request.getSupplierId())
               .eq(request.getStatus() != null, 
                   PurchaseOrder::getStatus, request.getStatus())
               .between(request.getStartDate() != null && request.getEndDate() != null,
                       PurchaseOrder::getOrderDate, request.getStartDate(), request.getEndDate())
               .orderByDesc(PurchaseOrder::getCreateTime);
        
        IPage<PurchaseOrder> orderPage = this.page(page, wrapper);
        return orderPage.convert(this::convertToDTO);
    }
}
```

## 3. 采购入库管理

### 3.1 采购入库实体设计

```java
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_receipts")
public class PurchaseReceipt extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("receipt_number")
    private String receiptNumber;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("receipt_date")
    private LocalDate receiptDate;

    @TableField("status")
    @EnumValue
    private ReceiptStatus status;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("remarks")
    private String remarks;

    @TableField("received_by")
    private Long receivedBy;

    @Version
    private Integer version;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 业务方法
    public void confirm(Long receiverId) {
        if (this.status != ReceiptStatus.PENDING) {
            throw new BusinessException("只有待确认状态的入库单才能确认");
        }
        this.status = ReceiptStatus.CONFIRMED;
        this.receivedBy = receiverId;
    }
}
```

## 4. 类图设计

### 4.1 采购管理核心类图

```mermaid
classDiagram
    class PurchaseOrder {
        +Long id
        +String orderNumber
        +Long supplierId
        +Long warehouseId
        +PurchaseOrderStatus status
        +LocalDate orderDate
        +LocalDate expectedDate
        +BigDecimal totalAmount
        +BigDecimal finalAmount
        +approve()
        +cancel()
        +addOrderItem()
        +removeOrderItem()
    }

    class PurchaseOrderItem {
        +Long id
        +Long purchaseOrderId
        +Long productId
        +BigDecimal quantity
        +BigDecimal unitPrice
        +BigDecimal totalAmount
        +BigDecimal receivedQuantity
        +updateQuantity()
        +updatePrice()
        +getPendingQuantity()
        +isFullyReceived()
    }

    class PurchaseReceipt {
        +Long id
        +String receiptNumber
        +Long purchaseOrderId
        +Long supplierId
        +Long warehouseId
        +ReceiptStatus status
        +LocalDate receiptDate
        +BigDecimal totalAmount
        +confirm()
    }

    class PurchaseReceiptItem {
        +Long id
        +Long purchaseReceiptId
        +Long purchaseOrderItemId
        +Long productId
        +BigDecimal orderedQuantity
        +BigDecimal receivedQuantity
        +BigDecimal unitPrice
        +QualityStatus qualityStatus
        +updateReceivedQuantity()
        +isFullyReceived()
    }

    class PurchaseOrderService {
        +createPurchaseOrder()
        +submitForApproval()
        +approvePurchaseOrder()
        +getPurchaseOrders()
    }

    class PurchaseReceiptService {
        +createPurchaseReceipt()
        +confirmReceipt()
        +getPurchaseReceipts()
    }

    PurchaseOrder --o PurchaseOrderItem
    PurchaseReceipt --o PurchaseReceiptItem
    PurchaseOrder --o PurchaseReceipt
    PurchaseOrderItem --o PurchaseReceiptItem

    PurchaseOrderService ..> PurchaseOrder
    PurchaseReceiptService ..> PurchaseReceipt
```

## 5. 时序图设计

### 5.1 采购订单创建时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as ShenYu网关
    participant PS as 采购服务
    participant SS as 供应商服务
    participant WS as 仓库服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 创建采购订单请求
    G->>PS: 转发请求

    PS->>SS: 验证供应商信息
    SS-->>PS: 返回供应商信息

    PS->>WS: 验证仓库信息
    WS-->>PS: 返回仓库信息

    PS->>PS: 生成订单号
    PS->>PS: 创建订单对象
    PS->>PS: 添加订单项

    PS->>DB: 保存采购订单
    DB-->>PS: 返回订单ID

    PS->>K: 发布订单创建事件
    K-->>PS: 确认事件发布

    PS-->>G: 返回创建结果
    G-->>U: 返回响应

    Note over K: 异步事件处理
    K->>PS: 订单创建通知事件
    K->>SS: 供应商通知事件
```

### 5.2 采购入库确认时序图

```mermaid
sequenceDiagram
    participant U as 仓库管理员
    participant G as ShenYu网关
    participant RS as 入库服务
    participant IS as 库存服务
    participant PS as 采购服务
    participant K as Kafka
    participant DB as 数据库

    U->>G: 确认入库请求
    G->>RS: 转发请求

    RS->>DB: 获取入库单信息
    DB-->>RS: 返回入库单

    RS->>RS: 验证入库状态
    RS->>RS: 确认入库

    RS->>DB: 更新入库单状态
    DB-->>RS: 确认更新

    RS->>K: 发布入库确认事件
    K-->>IS: 库存更新事件
    K-->>PS: 采购完成事件

    par 并行处理
        IS->>DB: 更新库存数量
        and
        PS->>DB: 更新订单收货数量
    end

    RS-->>G: 返回确认结果
    G-->>U: 返回响应

    Note over K: 异步库存更新
    IS->>K: 库存变动事件
    K->>RS: 库存更新完成通知
```

## 6. 事件驱动设计

### 6.1 采购领域事件

```java
// 采购订单创建事件
@Data
@AllArgsConstructor
public class PurchaseOrderCreatedEvent extends DomainEvent {
    private Long orderId;
    private String orderNumber;
    private Long supplierId;
    private BigDecimal totalAmount;
    private List<PurchaseOrderItemDTO> orderItems;
}

// 采购订单审批事件
@Data
@AllArgsConstructor
public class PurchaseOrderApprovedEvent extends DomainEvent {
    private Long orderId;
    private Long approverId;
    private LocalDateTime approvedAt;
}

// 采购入库确认事件
@Data
@AllArgsConstructor
public class PurchaseReceiptConfirmedEvent extends DomainEvent {
    private Long receiptId;
    private Long purchaseOrderId;
    private BigDecimal totalAmount;
    private List<PurchaseReceiptItemDTO> receiptItems;
}
```

### 6.2 Kafka事件处理

```java
@Component
public class PurchaseEventPublisher {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String PURCHASE_TOPIC = "purchase.events";

    public void publishOrderCreated(PurchaseOrderCreatedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "order.created", event);
    }

    public void publishOrderApproved(PurchaseOrderApprovedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "order.approved", event);
    }

    public void publishReceiptConfirmed(PurchaseReceiptConfirmedEvent event) {
        kafkaTemplate.send(PURCHASE_TOPIC, "receipt.confirmed", event);
    }
}

@Component
@KafkaListener(topics = "purchase.events")
public class PurchaseEventHandler {

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private NotificationService notificationService;

    @KafkaHandler
    public void handleOrderCreated(PurchaseOrderCreatedEvent event) {
        // 发送供应商通知
        notificationService.sendPurchaseOrderNotification(event);
    }

    @KafkaHandler
    public void handleReceiptConfirmed(PurchaseReceiptConfirmedEvent event) {
        // 更新库存
        for (PurchaseReceiptItemDTO item : event.getReceiptItems()) {
            inventoryService.increaseInventory(
                item.getProductId(),
                event.getWarehouseId(),
                item.getReceivedQuantity(),
                item.getUnitPrice(),
                "采购入库-" + event.getReceiptId()
            );
        }
    }
}
```

## 4. 采购退货管理

### 4.1 退货实体设计

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_returns")
public class PurchaseReturn extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("return_number")
    private String returnNumber;

    @TableField("purchase_order_id")
    private Long purchaseOrderId;

    @TableField("purchase_receipt_id")
    private Long purchaseReceiptId;

    @TableField("supplier_id")
    private Long supplierId;

    @TableField("warehouse_id")
    private Long warehouseId;

    @TableField("return_type")
    @EnumValue
    private ReturnType returnType;

    @TableField("return_reason")
    @EnumValue
    private ReturnReason returnReason;

    @TableField("status")
    @EnumValue
    private ReturnStatus status;

    @TableField("return_date")
    private LocalDate returnDate;

    @TableField("expected_pickup_date")
    private LocalDate expectedPickupDate;

    @TableField("actual_pickup_date")
    private LocalDate actualPickupDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @TableField("handling_fee")
    private BigDecimal handlingFee = BigDecimal.ZERO;

    @TableField("description")
    private String description;

    @TableField("quality_report")
    private String qualityReport;

    @TableField("approved_by")
    private Long approvedBy;

    @TableField("approved_at")
    private LocalDateTime approvedAt;

    @TableField("processed_by")
    private Long processedBy;

    @TableField("processed_at")
    private LocalDateTime processedAt;

    @TableField(exist = false)
    private List<PurchaseReturnItem> items;

    @TableField(exist = false)
    private Supplier supplier;

    @TableField(exist = false)
    private Warehouse warehouse;
}

/**
 * 退货类型
 */
public enum ReturnType {
    QUALITY_ISSUE("质量问题"),
    QUANTITY_DISCREPANCY("数量不符"),
    SPECIFICATION_MISMATCH("规格不符"),
    DAMAGE_IN_TRANSIT("运输损坏"),
    EXPIRED_GOODS("过期商品"),
    WRONG_DELIVERY("错误发货"),
    COMMERCIAL_RETURN("商业退货"),
    OTHER("其他");

    private final String description;
}

/**
 * 退货原因
 */
public enum ReturnReason {
    DEFECTIVE_PRODUCT("产品缺陷"),
    INCORRECT_ITEM("商品错误"),
    DAMAGED_PACKAGING("包装损坏"),
    LATE_DELIVERY("延迟交货"),
    OVERSTOCK("库存过剩"),
    PRICE_CHANGE("价格变动"),
    SUPPLIER_RECALL("供应商召回"),
    REGULATORY_ISSUE("法规问题");

    private final String description;
}

/**
 * 退货状态
 */
public enum ReturnStatus {
    DRAFT("草稿"),
    PENDING_APPROVAL("待审批"),
    APPROVED("已审批"),
    REJECTED("已拒绝"),
    IN_PROCESS("处理中"),
    PICKED_UP("已取货"),
    REFUNDED("已退款"),
    COMPLETED("已完成"),
    CANCELLED("已取消");

    private final String description;
}
```

### 4.2 退货明细实体

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("purchase_return_items")
public class PurchaseReturnItem extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("return_id")
    private Long returnId;

    @TableField("purchase_order_item_id")
    private Long purchaseOrderItemId;

    @TableField("purchase_receipt_item_id")
    private Long purchaseReceiptItemId;

    @TableField("product_id")
    private Long productId;

    @TableField("product_sku")
    private String productSku;

    @TableField("product_name")
    private String productName;

    @TableField("specification")
    private String specification;

    @TableField("unit")
    private String unit;

    @TableField("original_quantity")
    private BigDecimal originalQuantity;

    @TableField("return_quantity")
    private BigDecimal returnQuantity;

    @TableField("unit_price")
    private BigDecimal unitPrice;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @TableField("batch_number")
    private String batchNumber;

    @TableField("production_date")
    private LocalDate productionDate;

    @TableField("expiry_date")
    private LocalDate expiryDate;

    @TableField("return_reason")
    private String returnReason;

    @TableField("quality_status")
    @EnumValue
    private QualityStatus qualityStatus;

    @TableField("remarks")
    private String remarks;

    @TableField(exist = false)
    private Product product;
}

/**
 * 质量状态
 */
public enum QualityStatus {
    GOOD("良品"),
    DEFECTIVE("次品"),
    DAMAGED("损坏"),
    EXPIRED("过期"),
    CONTAMINATED("污染"),
    UNKNOWN("未知");

    private final String description;
}
```

### 4.3 退货服务实现

```java
@Service
@Transactional
public class PurchaseReturnService {

    @Autowired
    private PurchaseReturnMapper returnMapper;

    @Autowired
    private PurchaseReturnItemMapper returnItemMapper;

    @Autowired
    private PurchaseReceiptService receiptService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private FinanceService financeService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 创建退货申请
     */
    public PurchaseReturn createReturn(CreateReturnRequest request) {
        // 1. 验证退货申请
        validateReturnRequest(request);

        // 2. 生成退货单号
        String returnNumber = generateReturnNumber();

        // 3. 创建退货主记录
        PurchaseReturn purchaseReturn = PurchaseReturn.builder()
            .returnNumber(returnNumber)
            .purchaseOrderId(request.getPurchaseOrderId())
            .purchaseReceiptId(request.getPurchaseReceiptId())
            .supplierId(request.getSupplierId())
            .warehouseId(request.getWarehouseId())
            .returnType(request.getReturnType())
            .returnReason(request.getReturnReason())
            .status(ReturnStatus.DRAFT)
            .returnDate(request.getReturnDate())
            .expectedPickupDate(request.getExpectedPickupDate())
            .description(request.getDescription())
            .build();

        returnMapper.insert(purchaseReturn);

        // 4. 创建退货明细
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CreateReturnItemRequest itemRequest : request.getItems()) {
            PurchaseReturnItem item = createReturnItem(purchaseReturn.getId(), itemRequest);
            returnItemMapper.insert(item);
            totalAmount = totalAmount.add(item.getTotalAmount());
        }

        // 5. 更新总金额
        purchaseReturn.setTotalAmount(totalAmount);
        returnMapper.updateById(purchaseReturn);

        // 6. 发送创建事件
        publishReturnEvent("RETURN_CREATED", purchaseReturn);

        return purchaseReturn;
    }

    /**
     * 提交退货申请审批
     */
    public void submitForApproval(Long returnId) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }

        if (purchaseReturn.getStatus() != ReturnStatus.DRAFT) {
            throw new BusinessException("只有草稿状态的退货单才能提交审批");
        }

        // 1. 更新状态
        purchaseReturn.setStatus(ReturnStatus.PENDING_APPROVAL);
        returnMapper.updateById(purchaseReturn);

        // 2. 启动审批流程
        workflowService.startProcess("purchase_return_approval", returnId);

        // 3. 发送提交事件
        publishReturnEvent("RETURN_SUBMITTED", purchaseReturn);
    }

    /**
     * 审批退货申请
     */
    public void approveReturn(Long returnId, Long approverId, String approvalComments) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }

        if (purchaseReturn.getStatus() != ReturnStatus.PENDING_APPROVAL) {
            throw new BusinessException("退货单状态不正确");
        }

        // 1. 更新审批信息
        purchaseReturn.setStatus(ReturnStatus.APPROVED);
        purchaseReturn.setApprovedBy(approverId);
        purchaseReturn.setApprovedAt(LocalDateTime.now());
        returnMapper.updateById(purchaseReturn);

        // 2. 计算退款金额
        calculateRefundAmount(purchaseReturn);

        // 3. 预留库存（如果需要）
        if (needsInventoryReservation(purchaseReturn)) {
            reserveInventoryForReturn(purchaseReturn);
        }

        // 4. 通知供应商
        notifySupplierForReturn(purchaseReturn);

        // 5. 发送审批事件
        publishReturnEvent("RETURN_APPROVED", purchaseReturn);
    }

    /**
     * 拒绝退货申请
     */
    public void rejectReturn(Long returnId, Long approverId, String rejectionReason) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }

        // 1. 更新状态
        purchaseReturn.setStatus(ReturnStatus.REJECTED);
        purchaseReturn.setApprovedBy(approverId);
        purchaseReturn.setApprovedAt(LocalDateTime.now());
        purchaseReturn.setDescription(purchaseReturn.getDescription() + "\n拒绝原因：" + rejectionReason);
        returnMapper.updateById(purchaseReturn);

        // 2. 发送拒绝事件
        publishReturnEvent("RETURN_REJECTED", purchaseReturn);
    }

    /**
     * 处理退货（出库）
     */
    public void processReturn(Long returnId, ProcessReturnRequest request) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }

        if (purchaseReturn.getStatus() != ReturnStatus.APPROVED) {
            throw new BusinessException("只有已审批的退货单才能处理");
        }

        // 1. 更新状态
        purchaseReturn.setStatus(ReturnStatus.IN_PROCESS);
        purchaseReturn.setProcessedBy(request.getProcessedBy());
        purchaseReturn.setProcessedAt(LocalDateTime.now());
        returnMapper.updateById(purchaseReturn);

        // 2. 处理库存出库
        List<PurchaseReturnItem> items = returnItemMapper.selectByReturnId(returnId);
        for (PurchaseReturnItem item : items) {
            // 减少库存
            inventoryService.decreaseInventory(
                item.getProductId(),
                purchaseReturn.getWarehouseId(),
                item.getReturnQuantity(),
                "采购退货-" + purchaseReturn.getReturnNumber()
            );

            // 更新明细状态
            item.setQualityStatus(getQualityStatusFromRequest(request, item.getId()));
            returnItemMapper.updateById(item);
        }

        // 3. 生成质量报告
        if (request.getQualityReport() != null) {
            purchaseReturn.setQualityReport(request.getQualityReport());
            returnMapper.updateById(purchaseReturn);
        }

        // 4. 发送处理事件
        publishReturnEvent("RETURN_PROCESSED", purchaseReturn);
    }

    /**
     * 确认取货
     */
    public void confirmPickup(Long returnId, LocalDate actualPickupDate) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }

        // 1. 更新取货信息
        purchaseReturn.setStatus(ReturnStatus.PICKED_UP);
        purchaseReturn.setActualPickupDate(actualPickupDate);
        returnMapper.updateById(purchaseReturn);

        // 2. 发送取货事件
        publishReturnEvent("RETURN_PICKED_UP", purchaseReturn);
    }

    /**
     * 处理退款
     */
    public void processRefund(Long returnId, ProcessRefundRequest request) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }

        if (purchaseReturn.getStatus() != ReturnStatus.PICKED_UP) {
            throw new BusinessException("只有已取货的退货单才能处理退款");
        }

        // 1. 计算最终退款金额
        BigDecimal finalRefundAmount = calculateFinalRefundAmount(purchaseReturn, request);

        // 2. 更新退款信息
        purchaseReturn.setRefundAmount(finalRefundAmount);
        purchaseReturn.setHandlingFee(request.getHandlingFee());
        purchaseReturn.setStatus(ReturnStatus.REFUNDED);
        returnMapper.updateById(purchaseReturn);

        // 3. 创建财务退款记录
        financeService.createRefund(RefundRequest.builder()
            .sourceType("PURCHASE_RETURN")
            .sourceId(returnId)
            .supplierId(purchaseReturn.getSupplierId())
            .amount(finalRefundAmount)
            .reason("采购退货退款")
            .build());

        // 4. 发送退款事件
        publishReturnEvent("RETURN_REFUNDED", purchaseReturn);
    }

    /**
     * 完成退货流程
     */
    public void completeReturn(Long returnId) {
        PurchaseReturn purchaseReturn = returnMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new BusinessException("退货单不存在");
        }

        if (purchaseReturn.getStatus() != ReturnStatus.REFUNDED) {
            throw new BusinessException("只有已退款的退货单才能完成");
        }

        // 1. 更新状态
        purchaseReturn.setStatus(ReturnStatus.COMPLETED);
        returnMapper.updateById(purchaseReturn);

        // 2. 发送完成事件
        publishReturnEvent("RETURN_COMPLETED", purchaseReturn);
    }

    /**
     * 验证退货申请
     */
    private void validateReturnRequest(CreateReturnRequest request) {
        // 1. 验证采购入库单是否存在
        PurchaseReceipt receipt = receiptService.getById(request.getPurchaseReceiptId());
        if (receipt == null) {
            throw new BusinessException("采购入库单不存在");
        }

        // 2. 验证退货数量
        for (CreateReturnItemRequest itemRequest : request.getItems()) {
            PurchaseReceiptItem receiptItem = receiptService.getReceiptItem(itemRequest.getPurchaseReceiptItemId());
            if (receiptItem == null) {
                throw new BusinessException("入库明细不存在");
            }

            // 检查已退货数量
            BigDecimal returnedQuantity = getReturnedQuantity(itemRequest.getPurchaseReceiptItemId());
            BigDecimal availableQuantity = receiptItem.getReceivedQuantity().subtract(returnedQuantity);

            if (itemRequest.getReturnQuantity().compareTo(availableQuantity) > 0) {
                throw new BusinessException("退货数量超过可退数量");
            }
        }

        // 3. 验证退货时间
        if (request.getReturnDate().isBefore(receipt.getReceiptDate())) {
            throw new BusinessException("退货日期不能早于入库日期");
        }
    }

    /**
     * 计算退款金额
     */
    private void calculateRefundAmount(PurchaseReturn purchaseReturn) {
        List<PurchaseReturnItem> items = returnItemMapper.selectByReturnId(purchaseReturn.getId());
        BigDecimal refundAmount = BigDecimal.ZERO;

        for (PurchaseReturnItem item : items) {
            BigDecimal itemRefund = item.getUnitPrice().multiply(item.getReturnQuantity());
            item.setRefundAmount(itemRefund);
            returnItemMapper.updateById(item);
            refundAmount = refundAmount.add(itemRefund);
        }

        purchaseReturn.setRefundAmount(refundAmount);
        returnMapper.updateById(purchaseReturn);
    }

    /**
     * 发送退货事件
     */
    private void publishReturnEvent(String eventType, PurchaseReturn purchaseReturn) {
        ReturnEvent event = ReturnEvent.builder()
            .eventType(eventType)
            .returnId(purchaseReturn.getId())
            .returnNumber(purchaseReturn.getReturnNumber())
            .supplierId(purchaseReturn.getSupplierId())
            .warehouseId(purchaseReturn.getWarehouseId())
            .status(purchaseReturn.getStatus())
            .totalAmount(purchaseReturn.getTotalAmount())
            .refundAmount(purchaseReturn.getRefundAmount())
            .eventTime(LocalDateTime.now())
            .build();

        rocketMQTemplate.convertAndSend("purchase-return-topic", event);
    }
}
```

### 4.4 退货流程设计

```mermaid
sequenceDiagram
    participant User as 用户
    participant ReturnService as 退货服务
    participant WorkflowService as 工作流服务
    participant InventoryService as 库存服务
    participant FinanceService as 财务服务
    participant SupplierService as 供应商服务
    participant MQ as 消息队列

    User->>ReturnService: 创建退货申请
    ReturnService->>ReturnService: 验证退货申请
    ReturnService->>ReturnService: 生成退货单
    ReturnService->>MQ: 发送创建事件

    User->>ReturnService: 提交审批
    ReturnService->>WorkflowService: 启动审批流程
    ReturnService->>MQ: 发送提交事件

    WorkflowService->>ReturnService: 审批通过
    ReturnService->>ReturnService: 计算退款金额
    ReturnService->>InventoryService: 预留库存
    ReturnService->>SupplierService: 通知供应商
    ReturnService->>MQ: 发送审批事件

    User->>ReturnService: 处理退货（出库）
    ReturnService->>InventoryService: 减少库存
    ReturnService->>ReturnService: 生成质量报告
    ReturnService->>MQ: 发送处理事件

    SupplierService->>ReturnService: 确认取货
    ReturnService->>ReturnService: 更新取货状态
    ReturnService->>MQ: 发送取货事件

    User->>ReturnService: 处理退款
    ReturnService->>FinanceService: 创建退款记录
    ReturnService->>ReturnService: 更新退款状态
    ReturnService->>MQ: 发送退款事件

    User->>ReturnService: 完成退货
    ReturnService->>ReturnService: 更新完成状态
    ReturnService->>MQ: 发送完成事件
```

### 4.5 退货控制器

```java
@RestController
@RequestMapping("/api/purchase/returns")
@Validated
public class PurchaseReturnController {

    @Autowired
    private PurchaseReturnService returnService;

    /**
     * 创建退货申请
     */
    @PostMapping
    public Result<PurchaseReturn> createReturn(@Valid @RequestBody CreateReturnRequest request) {
        PurchaseReturn purchaseReturn = returnService.createReturn(request);
        return Result.success(purchaseReturn);
    }

    /**
     * 提交审批
     */
    @PostMapping("/{id}/submit")
    public Result<Void> submitForApproval(@PathVariable Long id) {
        returnService.submitForApproval(id);
        return Result.success();
    }

    /**
     * 审批退货
     */
    @PostMapping("/{id}/approve")
    public Result<Void> approveReturn(
            @PathVariable Long id,
            @Valid @RequestBody ApprovalRequest request) {
        returnService.approveReturn(id, request.getApproverId(), request.getComments());
        return Result.success();
    }

    /**
     * 拒绝退货
     */
    @PostMapping("/{id}/reject")
    public Result<Void> rejectReturn(
            @PathVariable Long id,
            @Valid @RequestBody RejectionRequest request) {
        returnService.rejectReturn(id, request.getApproverId(), request.getReason());
        return Result.success();
    }

    /**
     * 处理退货
     */
    @PostMapping("/{id}/process")
    public Result<Void> processReturn(
            @PathVariable Long id,
            @Valid @RequestBody ProcessReturnRequest request) {
        returnService.processReturn(id, request);
        return Result.success();
    }

    /**
     * 确认取货
     */
    @PostMapping("/{id}/pickup")
    public Result<Void> confirmPickup(
            @PathVariable Long id,
            @Valid @RequestBody PickupRequest request) {
        returnService.confirmPickup(id, request.getActualPickupDate());
        return Result.success();
    }

    /**
     * 处理退款
     */
    @PostMapping("/{id}/refund")
    public Result<Void> processRefund(
            @PathVariable Long id,
            @Valid @RequestBody ProcessRefundRequest request) {
        returnService.processRefund(id, request);
        return Result.success();
    }

    /**
     * 完成退货
     */
    @PostMapping("/{id}/complete")
    public Result<Void> completeReturn(@PathVariable Long id) {
        returnService.completeReturn(id);
        return Result.success();
    }

    /**
     * 查询退货单详情
     */
    @GetMapping("/{id}")
    public Result<PurchaseReturn> getReturn(@PathVariable Long id) {
        PurchaseReturn purchaseReturn = returnService.getById(id);
        return Result.success(purchaseReturn);
    }

    /**
     * 分页查询退货单
     */
    @GetMapping
    public Result<PageResult<PurchaseReturn>> queryReturns(
            @Valid ReturnQueryRequest request) {
        PageResult<PurchaseReturn> result = returnService.queryReturns(request);
        return Result.success(result);
    }
}
```

### 4.6 数据传输对象

```java
/**
 * 创建退货申请请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateReturnRequest {

    @NotNull(message = "采购订单ID不能为空")
    private Long purchaseOrderId;

    @NotNull(message = "采购入库单ID不能为空")
    private Long purchaseReceiptId;

    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;

    @NotNull(message = "退货类型不能为空")
    private ReturnType returnType;

    @NotNull(message = "退货原因不能为空")
    private ReturnReason returnReason;

    @NotNull(message = "退货日期不能为空")
    private LocalDate returnDate;

    private LocalDate expectedPickupDate;

    @Size(max = 500, message = "描述长度不能超过500字符")
    private String description;

    @NotEmpty(message = "退货明细不能为空")
    @Valid
    private List<CreateReturnItemRequest> items;
}

/**
 * 创建退货明细请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateReturnItemRequest {

    @NotNull(message = "采购订单明细ID不能为空")
    private Long purchaseOrderItemId;

    @NotNull(message = "采购入库明细ID不能为空")
    private Long purchaseReceiptItemId;

    @NotNull(message = "商品ID不能为空")
    private Long productId;

    @NotNull(message = "退货数量不能为空")
    @DecimalMin(value = "0.001", message = "退货数量必须大于0")
    private BigDecimal returnQuantity;

    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0", message = "单价不能为负数")
    private BigDecimal unitPrice;

    private String batchNumber;

    private LocalDate productionDate;

    private LocalDate expiryDate;

    @Size(max = 200, message = "退货原因长度不能超过200字符")
    private String returnReason;

    @Size(max = 200, message = "备注长度不能超过200字符")
    private String remarks;
}

/**
 * 处理退货请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessReturnRequest {

    @NotNull(message = "处理人不能为空")
    private Long processedBy;

    @Size(max = 1000, message = "质量报告长度不能超过1000字符")
    private String qualityReport;

    @Valid
    private List<ReturnItemQualityRequest> itemQualities;
}

/**
 * 退货明细质量状态请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnItemQualityRequest {

    @NotNull(message = "退货明细ID不能为空")
    private Long returnItemId;

    @NotNull(message = "质量状态不能为空")
    private QualityStatus qualityStatus;

    @Size(max = 200, message = "质量备注长度不能超过200字符")
    private String qualityRemarks;
}

/**
 * 处理退款请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessRefundRequest {

    @DecimalMin(value = "0", message = "手续费不能为负数")
    private BigDecimal handlingFee = BigDecimal.ZERO;

    @Size(max = 200, message = "退款备注长度不能超过200字符")
    private String refundRemarks;

    @Valid
    private List<ReturnItemRefundRequest> itemRefunds;
}

/**
 * 退货明细退款请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnItemRefundRequest {

    @NotNull(message = "退货明细ID不能为空")
    private Long returnItemId;

    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0", message = "退款金额不能为负数")
    private BigDecimal refundAmount;

    @Size(max = 200, message = "退款备注长度不能超过200字符")
    private String refundRemarks;
}
```

### 4.7 退货事件处理

```java
/**
 * 退货事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnEvent {
    private String eventType;
    private Long returnId;
    private String returnNumber;
    private Long supplierId;
    private Long warehouseId;
    private ReturnStatus status;
    private BigDecimal totalAmount;
    private BigDecimal refundAmount;
    private LocalDateTime eventTime;
}

/**
 * 退货事件监听器
 */
@Component
@RocketMQMessageListener(
    topic = "purchase-return-topic",
    consumerGroup = "purchase-return-consumer"
)
public class ReturnEventListener implements RocketMQListener<ReturnEvent> {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private ReportService reportService;

    @Override
    public void onMessage(ReturnEvent event) {
        try {
            switch (event.getEventType()) {
                case "RETURN_CREATED":
                    handleReturnCreated(event);
                    break;
                case "RETURN_SUBMITTED":
                    handleReturnSubmitted(event);
                    break;
                case "RETURN_APPROVED":
                    handleReturnApproved(event);
                    break;
                case "RETURN_REJECTED":
                    handleReturnRejected(event);
                    break;
                case "RETURN_PROCESSED":
                    handleReturnProcessed(event);
                    break;
                case "RETURN_PICKED_UP":
                    handleReturnPickedUp(event);
                    break;
                case "RETURN_REFUNDED":
                    handleReturnRefunded(event);
                    break;
                case "RETURN_COMPLETED":
                    handleReturnCompleted(event);
                    break;
                default:
                    log.warn("Unknown return event type: {}", event.getEventType());
            }
        } catch (Exception e) {
            log.error("Failed to handle return event: {}", event, e);
        }
    }

    /**
     * 处理退货创建事件
     */
    private void handleReturnCreated(ReturnEvent event) {
        // 发送创建通知
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_CREATED")
                .title("退货申请已创建")
                .content(String.format("退货单 %s 已创建，等待提交审批", event.getReturnNumber()))
                .recipients(getReturnStakeholders(event.getReturnId()))
                .build()
        );

        // 更新统计数据
        reportService.updateReturnStatistics("CREATED", event);
    }

    /**
     * 处理退货提交事件
     */
    private void handleReturnSubmitted(ReturnEvent event) {
        // 通知审批人
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_APPROVAL_REQUIRED")
                .title("退货申请待审批")
                .content(String.format("退货单 %s 已提交，请及时审批", event.getReturnNumber()))
                .recipients(getApprovers())
                .priority(NotificationPriority.HIGH)
                .build()
        );
    }

    /**
     * 处理退货审批通过事件
     */
    private void handleReturnApproved(ReturnEvent event) {
        // 通知申请人
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_APPROVED")
                .title("退货申请已审批通过")
                .content(String.format("退货单 %s 已审批通过，可以开始处理", event.getReturnNumber()))
                .recipients(getReturnStakeholders(event.getReturnId()))
                .build()
        );

        // 通知供应商
        supplierService.notifySupplier(
            event.getSupplierId(),
            "退货通知",
            String.format("您的退货申请 %s 已审批通过，请安排取货", event.getReturnNumber())
        );
    }

    /**
     * 处理退货完成事件
     */
    private void handleReturnCompleted(ReturnEvent event) {
        // 发送完成通知
        notificationService.sendNotification(
            NotificationRequest.builder()
                .type("RETURN_COMPLETED")
                .title("退货流程已完成")
                .content(String.format("退货单 %s 已完成，退款金额：%s",
                    event.getReturnNumber(),
                    event.getRefundAmount()))
                .recipients(getReturnStakeholders(event.getReturnId()))
                .build()
        );

        // 更新供应商评价
        supplierService.updateSupplierRating(event.getSupplierId(), "RETURN_COMPLETED");

        // 生成退货报告
        reportService.generateReturnReport(event.getReturnId());
    }

    private List<String> getReturnStakeholders(Long returnId) {
        // 获取退货相关人员列表
        return Arrays.asList("<EMAIL>", "<EMAIL>");
    }

    private List<String> getApprovers() {
        // 获取审批人列表
        return Arrays.asList("<EMAIL>");
    }
}
```

## 5. 总结

采购管理模块通过完整的退货流程设计，为PISP系统提供了全面的采购退货管理能力：

### 5.1 核心功能特性

1. **完整的退货流程**：
   - 退货申请创建和验证
   - 多级审批工作流
   - 库存出库处理
   - 供应商取货确认
   - 退款处理和完成

2. **灵活的退货类型支持**：
   - 质量问题退货
   - 数量不符退货
   - 规格不匹配退货
   - 运输损坏退货
   - 商业退货等

3. **智能的业务验证**：
   - 退货数量验证
   - 退货时间限制
   - 库存可用性检查
   - 重复退货防护

4. **完善的财务处理**：
   - 自动退款金额计算
   - 手续费扣除
   - 财务记录生成
   - 供应商账务处理

### 5.2 技术亮点

- **事件驱动架构**：使用RocketMQ实现异步事件处理
- **工作流集成**：支持灵活的审批流程配置
- **数据一致性**：确保库存和财务数据的一致性
- **可扩展设计**：支持不同类型的退货业务场景

### 5.3 业务价值

通过完善的退货流程设计，PISP系统能够：
- 提高退货处理效率
- 降低退货管理成本
- 增强供应商关系管理
- 提供完整的退货数据分析
- 确保财务数据准确性

这些设计使得采购管理模块具备了企业级的退货管理能力，能够满足复杂的业务需求和合规要求。

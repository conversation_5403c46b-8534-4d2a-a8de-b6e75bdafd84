# DDD-010 零售管理模块详细设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | DDD-010 |
| 文档名称 | 零售管理模块详细设计 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 正式 |
| 作者 | 系统架构师 |

## 1. 模块概述

零售管理模块负责管理企业的B2C零售业务，包括POS销售、会员管理、促销活动等功能，与现有的B2B销售管理模块形成互补。门店管理已迁移至基础数据管理模块。

### 1.1 模块架构

```mermaid
graph TB
    subgraph "零售管理模块"
        A[POS销售服务]
        B[会员管理服务]
        C[促销管理服务]
        D[零售订单服务]
    end

    subgraph "基础数据模块"
        E[门店管理服务]
    end
    
    subgraph "零售业务流程"
        F[商品选择] --> G[价格计算]
        G --> H[促销优惠]
        H --> I[会员积分]
        I --> J[支付处理]
        J --> K[小票打印]
        K --> L[库存扣减]
    end
    
    A --> J
    B --> I
    C --> H
    E --> A
    D --> F
```

### 1.2 业务特点

**零售业务 vs B2B销售业务对比：**

| 特性 | 零售业务 (B2C) | B2B销售业务 |
|------|----------------|-------------|
| **客户类型** | 个人消费者 | 企业客户 |
| **交易频次** | 高频、小额 | 低频、大额 |
| **支付方式** | 现金、刷卡、移动支付 | 赊销、银行转账 |
| **库存管理** | 实时扣减 | 预留后出库 |
| **价格策略** | 统一零售价、促销价 | 客户专属价格 |
| **会员体系** | 积分、等级、优惠券 | 信用额度管理 |
| **订单流程** | 即时成交 | 审批流程 |
| **发票管理** | 简化发票、电子小票 | 正式发票 |

## 2. POS销售管理

### 2.1 POS销售实体设计

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("retail_pos_sales")
public class RetailPosSale extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("sale_number")
    private String saleNumber;

    @TableField("store_id")
    private Long storeId;

    @TableField("cashier_id")
    private Long cashierId;

    @TableField("member_id")
    private Long memberId;

    @TableField("sale_date")
    private LocalDateTime saleDate;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("discount_amount")
    private BigDecimal discountAmount;

    @TableField("final_amount")
    private BigDecimal finalAmount;

    @TableField("payment_method")
    @EnumValue
    private PaymentMethod paymentMethod;

    @TableField("payment_status")
    @EnumValue
    private PaymentStatus paymentStatus;

    @TableField("points_earned")
    private Integer pointsEarned;

    @TableField("points_used")
    private Integer pointsUsed;

    @TableField("receipt_number")
    private String receiptNumber;

    @TableField("remarks")
    private String remarks;

    // 业务方法
    public void calculateFinalAmount() {
        this.finalAmount = this.totalAmount.subtract(this.discountAmount);
    }

    public void applyMemberDiscount(BigDecimal discountRate) {
        BigDecimal discount = this.totalAmount.multiply(discountRate);
        this.discountAmount = this.discountAmount.add(discount);
        calculateFinalAmount();
    }
}
```

### 2.2 POS销售明细实体

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("retail_pos_sale_items")
public class RetailPosSaleItem extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("pos_sale_id")
    private Long posSaleId;

    @TableField("product_id")
    private Long productId;

    @TableField("product_name")
    private String productName;

    @TableField("product_code")
    private String productCode;

    @TableField("quantity")
    private BigDecimal quantity;

    @TableField("unit_price")
    private BigDecimal unitPrice;

    @TableField("original_price")
    private BigDecimal originalPrice;

    @TableField("discount_amount")
    private BigDecimal discountAmount;

    @TableField("total_amount")
    private BigDecimal totalAmount;

    @TableField("promotion_id")
    private Long promotionId;

    // 业务方法
    public void calculateTotalAmount() {
        this.totalAmount = this.unitPrice.multiply(this.quantity);
    }

    public void applyDiscount(BigDecimal discount) {
        this.discountAmount = discount;
        this.unitPrice = this.originalPrice.subtract(discount);
        calculateTotalAmount();
    }
}
```

## 3. 会员管理

### 3.1 会员实体设计

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("retail_members")
public class RetailMember extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("member_number")
    private String memberNumber;

    @TableField("member_name")
    private String memberName;

    @TableField("phone")
    private String phone;

    @TableField("email")
    private String email;

    @TableField("gender")
    @EnumValue
    private Gender gender;

    @TableField("birthday")
    private LocalDate birthday;

    @TableField("member_level")
    @EnumValue
    private MemberLevel memberLevel;

    @TableField("total_points")
    private Integer totalPoints;

    @TableField("available_points")
    private Integer availablePoints;

    @TableField("total_consumption")
    private BigDecimal totalConsumption;

    @TableField("registration_date")
    private LocalDate registrationDate;

    @TableField("last_visit_date")
    private LocalDate lastVisitDate;

    @TableField("status")
    @EnumValue
    private MemberStatus status;

    // 业务方法
    public void earnPoints(Integer points) {
        this.totalPoints += points;
        this.availablePoints += points;
    }

    public boolean usePoints(Integer points) {
        if (this.availablePoints >= points) {
            this.availablePoints -= points;
            return true;
        }
        return false;
    }

    public void updateConsumption(BigDecimal amount) {
        this.totalConsumption = this.totalConsumption.add(amount);
        this.lastVisitDate = LocalDate.now();
        updateMemberLevel();
    }

    private void updateMemberLevel() {
        // 根据消费金额自动升级会员等级
        if (this.totalConsumption.compareTo(new BigDecimal("10000")) >= 0) {
            this.memberLevel = MemberLevel.GOLD;
        } else if (this.totalConsumption.compareTo(new BigDecimal("5000")) >= 0) {
            this.memberLevel = MemberLevel.SILVER;
        } else {
            this.memberLevel = MemberLevel.BRONZE;
        }
    }
}
```

## 4. 促销管理

### 4.1 促销活动实体设计

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("retail_promotions")
public class RetailPromotion extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("promotion_name")
    private String promotionName;

    @TableField("promotion_code")
    private String promotionCode;

    @TableField("promotion_type")
    @EnumValue
    private PromotionType promotionType;

    @TableField("start_date")
    private LocalDateTime startDate;

    @TableField("end_date")
    private LocalDateTime endDate;

    @TableField("discount_rate")
    private BigDecimal discountRate;

    @TableField("discount_amount")
    private BigDecimal discountAmount;

    @TableField("min_purchase_amount")
    private BigDecimal minPurchaseAmount;

    @TableField("max_discount_amount")
    private BigDecimal maxDiscountAmount;

    @TableField("applicable_products")
    private String applicableProducts; // JSON格式存储商品ID列表

    @TableField("applicable_member_levels")
    private String applicableMemberLevels; // JSON格式存储会员等级

    @TableField("usage_limit")
    private Integer usageLimit;

    @TableField("used_count")
    private Integer usedCount;

    @TableField("status")
    @EnumValue
    private PromotionStatus status;

    // 业务方法
    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return this.status == PromotionStatus.ACTIVE 
            && now.isAfter(this.startDate) 
            && now.isBefore(this.endDate)
            && (this.usageLimit == null || this.usedCount < this.usageLimit);
    }

    public BigDecimal calculateDiscount(BigDecimal amount) {
        if (!isActive() || amount.compareTo(this.minPurchaseAmount) < 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal discount = BigDecimal.ZERO;
        if (this.promotionType == PromotionType.PERCENTAGE) {
            discount = amount.multiply(this.discountRate);
            if (this.maxDiscountAmount != null && discount.compareTo(this.maxDiscountAmount) > 0) {
                discount = this.maxDiscountAmount;
            }
        } else if (this.promotionType == PromotionType.FIXED_AMOUNT) {
            discount = this.discountAmount;
        }

        return discount;
    }
}
```

## 5. 服务层设计

### 5.1 POS销售服务

```java
@Service
@Transactional
public class RetailPosSaleServiceImpl extends ServiceImpl<RetailPosSaleMapper, RetailPosSale>
    implements RetailPosSaleService {

    @Autowired
    private RetailMemberService memberService;

    @Autowired
    private RetailPromotionService promotionService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    public RetailPosSaleDTO createPosSale(CreatePosSaleRequest request) {
        // 1. 验证门店和收银员
        validateStoreAndCashier(request.getStoreId(), request.getCashierId());

        // 2. 创建POS销售单
        RetailPosSale posSale = new RetailPosSale();
        posSale.setSaleNumber(generateSaleNumber());
        posSale.setStoreId(request.getStoreId());
        posSale.setCashierId(request.getCashierId());
        posSale.setMemberId(request.getMemberId());
        posSale.setSaleDate(LocalDateTime.now());
        posSale.setPaymentMethod(request.getPaymentMethod());

        // 3. 处理销售明细
        List<RetailPosSaleItem> saleItems = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (CreatePosSaleItemRequest itemRequest : request.getItems()) {
            RetailPosSaleItem item = createSaleItem(itemRequest);
            saleItems.add(item);
            totalAmount = totalAmount.add(item.getTotalAmount());
        }

        posSale.setTotalAmount(totalAmount);

        // 4. 应用促销优惠
        BigDecimal discountAmount = applyPromotions(posSale, saleItems, request.getMemberId());
        posSale.setDiscountAmount(discountAmount);
        posSale.calculateFinalAmount();

        // 5. 处理会员积分
        if (request.getMemberId() != null) {
            processMemberPoints(posSale, request.getMemberId());
        }

        // 6. 保存销售单
        this.save(posSale);

        // 7. 保存销售明细
        for (RetailPosSaleItem item : saleItems) {
            item.setPosSaleId(posSale.getId());
        }
        retailPosSaleItemService.saveBatch(saleItems);

        // 8. 扣减库存
        deductInventory(saleItems, request.getStoreId());

        // 9. 生成小票号
        posSale.setReceiptNumber(generateReceiptNumber(posSale.getId()));
        this.updateById(posSale);

        // 10. 发布销售完成事件
        eventPublisher.publishEvent(new PosSaleCompletedEvent(posSale.getId()));

        return convertToDTO(posSale);
    }

    private BigDecimal applyPromotions(RetailPosSale posSale, List<RetailPosSaleItem> items, Long memberId) {
        BigDecimal totalDiscount = BigDecimal.ZERO;

        // 获取适用的促销活动
        List<RetailPromotion> promotions = promotionService.getApplicablePromotions(
            posSale.getTotalAmount(), memberId);

        for (RetailPromotion promotion : promotions) {
            BigDecimal discount = promotion.calculateDiscount(posSale.getTotalAmount());
            totalDiscount = totalDiscount.add(discount);
        }

        return totalDiscount;
    }

    private void processMemberPoints(RetailPosSale posSale, Long memberId) {
        RetailMember member = memberService.getById(memberId);
        if (member != null) {
            // 计算积分（1元=1积分）
            Integer pointsEarned = posSale.getFinalAmount().intValue();
            member.earnPoints(pointsEarned);
            member.updateConsumption(posSale.getFinalAmount());

            posSale.setPointsEarned(pointsEarned);
            memberService.updateById(member);
        }
    }

    @Override
    public void processPayment(Long saleId, PaymentRequest request) {
        RetailPosSale posSale = this.getById(saleId);
        if (posSale == null) {
            throw new BusinessException("销售单不存在");
        }

        // 处理支付
        PaymentResult result = paymentService.processPayment(request);
        if (result.isSuccess()) {
            posSale.setPaymentStatus(PaymentStatus.PAID);
            this.updateById(posSale);

            // 发布支付完成事件
            eventPublisher.publishEvent(new PaymentCompletedEvent(saleId, result));
        } else {
            throw new BusinessException("支付失败：" + result.getErrorMessage());
        }
    }
}
```

### 5.2 会员管理服务

```java
@Service
@Transactional
public class RetailMemberServiceImpl extends ServiceImpl<RetailMemberMapper, RetailMember>
    implements RetailMemberService {

    @Override
    public RetailMemberDTO registerMember(RegisterMemberRequest request) {
        // 1. 验证手机号唯一性
        if (existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已注册");
        }

        // 2. 创建会员
        RetailMember member = new RetailMember();
        member.setMemberNumber(generateMemberNumber());
        member.setMemberName(request.getMemberName());
        member.setPhone(request.getPhone());
        member.setEmail(request.getEmail());
        member.setGender(request.getGender());
        member.setBirthday(request.getBirthday());
        member.setMemberLevel(MemberLevel.BRONZE);
        member.setTotalPoints(0);
        member.setAvailablePoints(0);
        member.setTotalConsumption(BigDecimal.ZERO);
        member.setRegistrationDate(LocalDate.now());
        member.setStatus(MemberStatus.ACTIVE);

        this.save(member);

        // 3. 赠送注册积分
        member.earnPoints(100); // 注册赠送100积分
        this.updateById(member);

        return convertToDTO(member);
    }

    @Override
    public List<RetailCouponDTO> getMemberCoupons(Long memberId) {
        return retailCouponService.getMemberCoupons(memberId);
    }

    @Override
    public MemberStatisticsDTO getMemberStatistics(Long memberId) {
        RetailMember member = this.getById(memberId);
        if (member == null) {
            throw new BusinessException("会员不存在");
        }

        MemberStatisticsDTO statistics = new MemberStatisticsDTO();
        statistics.setMemberId(memberId);
        statistics.setMemberLevel(member.getMemberLevel());
        statistics.setTotalPoints(member.getTotalPoints());
        statistics.setAvailablePoints(member.getAvailablePoints());
        statistics.setTotalConsumption(member.getTotalConsumption());

        // 计算本月消费
        BigDecimal monthlyConsumption = calculateMonthlyConsumption(memberId);
        statistics.setMonthlyConsumption(monthlyConsumption);

        // 计算消费排名
        Integer ranking = calculateMemberRanking(memberId);
        statistics.setRanking(ranking);

        return statistics;
    }
}
```

## 6. 枚举定义

### 6.1 支付方式枚举

```java
public enum PaymentMethod {
    CASH("现金"),
    CARD("银行卡"),
    ALIPAY("支付宝"),
    WECHAT("微信支付"),
    POINTS("积分支付"),
    MIXED("混合支付");

    private final String description;

    PaymentMethod(String description) {
        this.description = description;
    }
}
```

### 6.2 会员等级枚举

```java
public enum MemberLevel {
    BRONZE("铜牌会员", new BigDecimal("0.95")),
    SILVER("银牌会员", new BigDecimal("0.90")),
    GOLD("金牌会员", new BigDecimal("0.85")),
    PLATINUM("白金会员", new BigDecimal("0.80"));

    private final String description;
    private final BigDecimal discountRate;

    MemberLevel(String description, BigDecimal discountRate) {
        this.description = description;
        this.discountRate = discountRate;
    }
}
```

### 6.3 促销类型枚举

```java
public enum PromotionType {
    PERCENTAGE("百分比折扣"),
    FIXED_AMOUNT("固定金额减免"),
    BUY_X_GET_Y("买X送Y"),
    FULL_REDUCTION("满减优惠"),
    MEMBER_EXCLUSIVE("会员专享");

    private final String description;

    PromotionType(String description) {
        this.description = description;
    }
}
```

## 7. 事件设计

### 7.1 POS销售完成事件

```java
@Data
@AllArgsConstructor
public class PosSaleCompletedEvent {
    private Long saleId;
    private LocalDateTime eventTime = LocalDateTime.now();
}
```

### 7.2 会员积分变更事件

```java
@Data
@AllArgsConstructor
public class MemberPointsChangedEvent {
    private Long memberId;
    private Integer pointsChange;
    private String reason;
    private LocalDateTime eventTime = LocalDateTime.now();
}
```

## 8. 类图设计

```mermaid
classDiagram
    class RetailPosSale {
        +Long id
        +String saleNumber
        +Long storeId
        +Long cashierId
        +Long memberId
        +LocalDateTime saleDate
        +BigDecimal totalAmount
        +BigDecimal discountAmount
        +BigDecimal finalAmount
        +PaymentMethod paymentMethod
        +PaymentStatus paymentStatus
        +calculateFinalAmount()
        +applyMemberDiscount()
    }

    class RetailPosSaleItem {
        +Long id
        +Long posSaleId
        +Long productId
        +BigDecimal quantity
        +BigDecimal unitPrice
        +BigDecimal totalAmount
        +calculateTotalAmount()
        +applyDiscount()
    }

    class RetailMember {
        +Long id
        +String memberNumber
        +String memberName
        +String phone
        +MemberLevel memberLevel
        +Integer totalPoints
        +Integer availablePoints
        +BigDecimal totalConsumption
        +earnPoints()
        +usePoints()
        +updateConsumption()
    }

    class RetailPromotion {
        +Long id
        +String promotionName
        +PromotionType promotionType
        +LocalDateTime startDate
        +LocalDateTime endDate
        +BigDecimal discountRate
        +isActive()
        +calculateDiscount()
    }

    class RetailStore {
        +Long id
        +String storeCode
        +String storeName
        +StoreType storeType
        +String address
        +StoreStatus status
        +isOperating()
    }

    RetailPosSale --o RetailPosSaleItem
    RetailPosSale --> RetailMember
    RetailPosSale --> RetailStore
    RetailPosSaleItem --> RetailPromotion
```

## 9. 时序图设计

### 9.1 POS销售完整流程时序图

```mermaid
sequenceDiagram
    participant C as 收银员
    participant POS as POS终端
    participant G as ShenYu网关
    participant RS as 零售服务
    participant MS as 会员服务
    participant PS as 促销服务
    participant IS as 库存服务
    participant FS as 财务服务
    participant PayS as 支付服务
    participant K as Kafka
    participant DB as 数据库
    participant Printer as 小票打印机

    Note over C,Printer: POS销售完整流程

    %% 1. 商品扫码阶段
    C->>POS: 扫描商品条码
    POS->>G: 获取商品信息
    G->>RS: 转发商品查询请求
    RS->>DB: 查询商品基础信息
    DB-->>RS: 返回商品信息
    RS-->>G: 返回商品详情
    G-->>POS: 返回商品信息
    POS-->>C: 显示商品信息和价格

    %% 2. 会员识别阶段
    C->>POS: 输入会员手机号/扫码
    POS->>G: 会员身份验证
    G->>RS: 转发会员查询
    RS->>MS: 查询会员信息
    MS->>DB: 查询会员详情
    DB-->>MS: 返回会员信息
    MS-->>RS: 返回会员详情
    RS-->>G: 返回会员信息
    G-->>POS: 返回会员信息
    POS-->>C: 显示会员等级和积分

    %% 3. 促销计算阶段
    C->>POS: 确认购买商品
    POS->>G: 计算订单金额
    G->>RS: 转发计算请求
    RS->>PS: 查询适用促销活动
    PS->>DB: 查询促销规则
    DB-->>PS: 返回促销信息
    PS-->>RS: 返回适用促销
    RS->>RS: 计算促销优惠金额
    RS->>RS: 计算会员折扣
    RS->>RS: 计算最终金额
    RS-->>G: 返回计算结果
    G-->>POS: 返回订单金额
    POS-->>C: 显示优惠明细和应付金额

    %% 4. 支付处理阶段
    C->>POS: 选择支付方式
    POS->>G: 发起支付请求
    G->>RS: 转发支付请求
    RS->>PayS: 调用支付服务
    PayS->>PayS: 处理支付
    PayS-->>RS: 返回支付结果

    alt 支付成功
        RS->>DB: 保存POS销售记录
        DB-->>RS: 确认保存成功

        %% 5. 库存扣减
        RS->>IS: 扣减商品库存
        IS->>DB: 更新库存数量
        DB-->>IS: 确认库存更新
        IS-->>RS: 确认库存扣减

        %% 6. 会员积分处理
        RS->>MS: 更新会员积分
        MS->>DB: 增加会员积分
        DB-->>MS: 确认积分更新
        MS-->>RS: 确认积分处理

        %% 7. 财务记录
        RS->>FS: 记录销售收入
        FS->>DB: 保存财务记录
        DB-->>FS: 确认财务记录
        FS-->>RS: 确认财务处理

        %% 8. 小票打印
        RS->>Printer: 发送打印指令
        Printer-->>RS: 确认打印完成

        %% 9. 事件发布
        RS->>K: 发布销售完成事件
        K-->>RS: 确认事件发布

        RS-->>G: 返回销售成功
        G-->>POS: 返回成功结果
        POS-->>C: 显示交易成功，打印小票

    else 支付失败
        PayS-->>RS: 返回支付失败
        RS-->>G: 返回支付失败
        G-->>POS: 返回失败结果
        POS-->>C: 显示支付失败，请重试
    end

    %% 10. 异步事件处理
    Note over K: 异步事件处理
    K->>IS: 库存变动事件
    K->>FS: 销售收入事件
    K->>MS: 会员消费事件
```

### 9.2 会员注册流程时序图

```mermaid
sequenceDiagram
    participant C as 客户
    participant Staff as 店员
    participant POS as POS终端
    participant G as ShenYu网关
    participant RS as 零售服务
    participant MS as 会员服务
    participant SMS as 短信服务
    participant DB as 数据库

    Note over C,DB: 会员注册流程

    C->>Staff: 申请注册会员
    Staff->>POS: 启动会员注册
    POS->>Staff: 显示注册表单

    Staff->>POS: 输入客户基本信息
    POS->>G: 提交注册请求
    G->>RS: 转发注册请求
    RS->>MS: 处理会员注册

    %% 验证手机号唯一性
    MS->>DB: 检查手机号是否已注册
    DB-->>MS: 返回检查结果

    alt 手机号未注册
        %% 生成会员号
        MS->>MS: 生成唯一会员号

        %% 保存会员信息
        MS->>DB: 保存会员基本信息
        DB-->>MS: 确认保存成功

        %% 赠送注册积分
        MS->>MS: 赠送注册积分(100分)
        MS->>DB: 更新会员积分
        DB-->>MS: 确认积分更新

        %% 发送欢迎短信
        MS->>SMS: 发送注册成功短信
        SMS-->>MS: 确认短信发送

        MS-->>RS: 返回注册成功
        RS-->>G: 返回成功结果
        G-->>POS: 返回会员信息
        POS-->>Staff: 显示注册成功
        Staff-->>C: 告知注册成功，赠送积分

    else 手机号已注册
        MS-->>RS: 返回手机号已存在错误
        RS-->>G: 返回错误信息
        G-->>POS: 返回错误结果
        POS-->>Staff: 显示手机号已注册
        Staff-->>C: 告知手机号已注册
    end
```

### 9.3 促销活动应用时序图

```mermaid
sequenceDiagram
    participant POS as POS终端
    participant RS as 零售服务
    participant PS as 促销服务
    participant MS as 会员服务
    participant DB as 数据库

    Note over POS,DB: 促销活动应用流程

    POS->>RS: 请求计算订单优惠
    RS->>PS: 查询适用促销活动

    %% 查询促销活动
    PS->>DB: 查询有效促销活动
    DB-->>PS: 返回促销活动列表

    %% 筛选适用促销
    PS->>PS: 筛选时间范围内的活动
    PS->>PS: 筛选适用商品的活动

    %% 会员等级验证
    alt 有会员信息
        PS->>MS: 获取会员等级信息
        MS->>DB: 查询会员等级
        DB-->>MS: 返回会员等级
        MS-->>PS: 返回会员等级
        PS->>PS: 筛选适用会员等级的活动
    end

    %% 计算优惠金额
    loop 每个适用的促销活动
        PS->>PS: 计算促销优惠金额
        PS->>PS: 检查最大优惠限制
        PS->>PS: 检查使用次数限制
    end

    %% 选择最优促销
    PS->>PS: 选择最优惠的促销组合

    %% 更新使用次数
    PS->>DB: 更新促销活动使用次数
    DB-->>PS: 确认更新成功

    PS-->>RS: 返回最优促销方案
    RS->>RS: 应用促销优惠
    RS-->>POS: 返回优惠计算结果
```

### 9.4 库存实时扣减时序图

```mermaid
sequenceDiagram
    participant RS as 零售服务
    participant IS as 库存服务
    participant Cache as Redis缓存
    participant DB as PostgreSQL
    participant K as Kafka

    Note over RS,K: 库存实时扣减流程

    RS->>IS: 请求扣减库存

    %% 检查缓存库存
    IS->>Cache: 检查商品库存缓存
    Cache-->>IS: 返回缓存库存数量

    alt 缓存库存充足
        %% 扣减缓存库存
        IS->>Cache: 扣减缓存库存
        Cache-->>IS: 确认缓存扣减

        %% 异步更新数据库
        IS->>DB: 扣减数据库库存
        DB-->>IS: 确认数据库扣减

        %% 发布库存变动事件
        IS->>K: 发布库存扣减事件
        K-->>IS: 确认事件发布

        IS-->>RS: 返回扣减成功

    else 缓存库存不足
        %% 查询数据库实时库存
        IS->>DB: 查询实时库存数量
        DB-->>IS: 返回实时库存

        alt 数据库库存充足
            %% 扣减数据库库存
            IS->>DB: 扣减数据库库存
            DB-->>IS: 确认数据库扣减

            %% 更新缓存
            IS->>Cache: 更新库存缓存
            Cache-->>IS: 确认缓存更新

            %% 发布库存变动事件
            IS->>K: 发布库存扣减事件
            K-->>IS: 确认事件发布

            IS-->>RS: 返回扣减成功

        else 数据库库存不足
            IS-->>RS: 返回库存不足错误
        end
    end
```

## 10. 接口设计

### 10.1 POS销售接口

```java
@RestController
@RequestMapping("/api/v1/retail/pos-sales")
@Tag(name = "POS销售管理", description = "POS销售相关接口")
public class RetailPosSaleController {

    @Autowired
    private RetailPosSaleService posSaleService;

    @PostMapping
    @Operation(summary = "创建POS销售单")
    public Result<RetailPosSaleDTO> createPosSale(@RequestBody @Valid CreatePosSaleRequest request) {
        return Result.success(posSaleService.createPosSale(request));
    }

    @PostMapping("/{saleId}/payment")
    @Operation(summary = "处理支付")
    public Result<PaymentResultDTO> processPayment(
            @PathVariable Long saleId,
            @RequestBody @Valid PaymentRequest request) {
        return Result.success(posSaleService.processPayment(saleId, request));
    }

    @GetMapping("/{saleId}")
    @Operation(summary = "查询POS销售单详情")
    public Result<RetailPosSaleDTO> getPosSale(@PathVariable Long saleId) {
        return Result.success(posSaleService.getPosSaleById(saleId));
    }

    @GetMapping
    @Operation(summary = "查询POS销售单列表")
    public Result<PageResult<RetailPosSaleDTO>> getPosSales(
            @Valid PosSaleQueryRequest request) {
        return Result.success(posSaleService.getPosSales(request));
    }

    @PostMapping("/{saleId}/print-receipt")
    @Operation(summary = "重新打印小票")
    public Result<Void> printReceipt(@PathVariable Long saleId) {
        posSaleService.printReceipt(saleId);
        return Result.success();
    }
}
```

### 10.2 会员管理接口

```java
@RestController
@RequestMapping("/api/v1/retail/members")
@Tag(name = "会员管理", description = "会员相关接口")
public class RetailMemberController {

    @Autowired
    private RetailMemberService memberService;

    @PostMapping
    @Operation(summary = "注册会员")
    public Result<RetailMemberDTO> registerMember(@RequestBody @Valid RegisterMemberRequest request) {
        return Result.success(memberService.registerMember(request));
    }

    @GetMapping("/{memberId}")
    @Operation(summary = "查询会员详情")
    public Result<RetailMemberDTO> getMember(@PathVariable Long memberId) {
        return Result.success(memberService.getMemberById(memberId));
    }

    @GetMapping("/phone/{phone}")
    @Operation(summary = "根据手机号查询会员")
    public Result<RetailMemberDTO> getMemberByPhone(@PathVariable String phone) {
        return Result.success(memberService.getMemberByPhone(phone));
    }

    @GetMapping("/{memberId}/statistics")
    @Operation(summary = "查询会员统计信息")
    public Result<MemberStatisticsDTO> getMemberStatistics(@PathVariable Long memberId) {
        return Result.success(memberService.getMemberStatistics(memberId));
    }

    @PostMapping("/{memberId}/points/use")
    @Operation(summary = "使用会员积分")
    public Result<Void> usePoints(
            @PathVariable Long memberId,
            @RequestBody @Valid UsePointsRequest request) {
        memberService.usePoints(memberId, request.getPoints(), request.getReason());
        return Result.success();
    }

    @GetMapping("/{memberId}/points/log")
    @Operation(summary = "查询积分变动记录")
    public Result<PageResult<MemberPointsLogDTO>> getPointsLog(
            @PathVariable Long memberId,
            @Valid PageRequest pageRequest) {
        return Result.success(memberService.getPointsLog(memberId, pageRequest));
    }

    @PutMapping("/{memberId}")
    @Operation(summary = "更新会员信息")
    public Result<RetailMemberDTO> updateMember(
            @PathVariable Long memberId,
            @RequestBody @Valid UpdateMemberRequest request) {
        return Result.success(memberService.updateMember(memberId, request));
    }
}
```

### 10.3 促销管理接口

```java
@RestController
@RequestMapping("/api/v1/retail/promotions")
@Tag(name = "促销管理", description = "促销活动相关接口")
public class RetailPromotionController {

    @Autowired
    private RetailPromotionService promotionService;

    @PostMapping
    @Operation(summary = "创建促销活动")
    public Result<RetailPromotionDTO> createPromotion(@RequestBody @Valid CreatePromotionRequest request) {
        return Result.success(promotionService.createPromotion(request));
    }

    @GetMapping("/{promotionId}")
    @Operation(summary = "查询促销活动详情")
    public Result<RetailPromotionDTO> getPromotion(@PathVariable Long promotionId) {
        return Result.success(promotionService.getPromotionById(promotionId));
    }

    @GetMapping
    @Operation(summary = "查询促销活动列表")
    public Result<PageResult<RetailPromotionDTO>> getPromotions(
            @Valid PromotionQueryRequest request) {
        return Result.success(promotionService.getPromotions(request));
    }

    @PostMapping("/calculate")
    @Operation(summary = "计算促销优惠")
    public Result<PromotionCalculationDTO> calculatePromotion(
            @RequestBody @Valid PromotionCalculationRequest request) {
        return Result.success(promotionService.calculatePromotion(request));
    }

    @PutMapping("/{promotionId}/status")
    @Operation(summary = "更新促销活动状态")
    public Result<Void> updatePromotionStatus(
            @PathVariable Long promotionId,
            @RequestBody @Valid UpdatePromotionStatusRequest request) {
        promotionService.updatePromotionStatus(promotionId, request.getStatus());
        return Result.success();
    }

    @GetMapping("/active")
    @Operation(summary = "查询有效促销活动")
    public Result<List<RetailPromotionDTO>> getActivePromotions() {
        return Result.success(promotionService.getActivePromotions());
    }
}
```


```

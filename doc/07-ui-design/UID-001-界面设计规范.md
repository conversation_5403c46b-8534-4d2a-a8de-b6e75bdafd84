# UID-001 进销存管理系统界面设计规范

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | UID-001 |
| 文档名称 | 用户界面设计规范 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 草稿 |
| 作者 | UI/UX设计师 |

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-25 | 初始版本创建 | UI/UX设计师 |

## 1. 设计原则

### 1.1 核心设计理念

- **简洁高效：** 界面简洁明了，操作高效便捷
- **一致性：** 保持整体设计风格和交互模式的一致性
- **易用性：** 符合用户习惯，降低学习成本
- **响应式：** 适配不同设备和屏幕尺寸
- **可访问性：** 支持无障碍访问

### 1.2 设计目标

- 提升用户操作效率50%以上
- 降低新用户学习成本
- 减少操作错误率
- 提高用户满意度

## 2. 视觉设计规范

### 2.1 色彩规范

**主色调：**
- 主色：#1890FF (蓝色) - 用于主要按钮、链接
- 辅助色：#52C41A (绿色) - 用于成功状态
- 警告色：#FAAD14 (橙色) - 用于警告提示
- 错误色：#F5222D (红色) - 用于错误状态
- 中性色：#8C8C8C (灰色) - 用于次要文本

**背景色：**
- 主背景：#FFFFFF (白色)
- 次背景：#FAFAFA (浅灰)
- 分割线：#F0F0F0 (浅灰)

### 2.2 字体规范

**字体族：**
- 中文：PingFang SC, Microsoft YaHei
- 英文：-apple-system, BlinkMacSystemFont, Segoe UI
- 数字：Roboto Mono (等宽字体)

**字体大小：**
- 标题1：24px (页面主标题)
- 标题2：20px (模块标题)
- 标题3：16px (卡片标题)
- 正文：14px (主要内容)
- 辅助文本：12px (次要信息)
- 小字：10px (标签、状态)

### 2.3 间距规范

**基础间距单位：** 8px

**常用间距：**
- 4px：最小间距
- 8px：基础间距
- 16px：中等间距
- 24px：大间距
- 32px：特大间距

## 3. 布局设计

### 3.1 整体布局结构

```mermaid
graph TB
    subgraph "页面布局"
        A[顶部导航栏 - 60px]
        B[主体内容区域]
        C[底部信息栏 - 40px]
    end
    
    subgraph "主体内容区域"
        D[左侧菜单 - 240px]
        E[内容区域]
    end
    
    subgraph "内容区域"
        F[面包屑导航 - 40px]
        G[页面标题区 - 60px]
        H[工具栏区域 - 48px]
        I[数据展示区域]
        J[分页区域 - 48px]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
```

### 3.2 响应式布局

**断点设置：**
- 超小屏：< 576px (手机竖屏)
- 小屏：576px - 768px (手机横屏)
- 中屏：768px - 992px (平板)
- 大屏：992px - 1200px (桌面)
- 超大屏：> 1200px (大桌面)

**布局适配：**
- 移动端：隐藏左侧菜单，使用抽屉式导航
- 平板：缩小左侧菜单宽度至180px
- 桌面：标准240px左侧菜单

## 4. Vue3组件设计规范

### 4.1 技术栈配置

**项目初始化：**
```bash
# 使用Vite创建Vue3项目
npm create vue@latest inventory-frontend
cd inventory-frontend

# 安装依赖
npm install element-plus
npm install @element-plus/icons-vue
npm install pinia
npm install vue-router@4
npm install axios
npm install @vueuse/core
```

**主要依赖：**
```json
{
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "axios": "^1.6.0",
    "@vueuse/core": "^10.7.0",
    "dayjs": "^1.11.0",
    "echarts": "^5.4.0",
    "vue-echarts": "^6.6.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "vite": "^5.0.0",
    "typescript": "^5.3.0",
    "@types/node": "^20.10.0",
    "unplugin-auto-import": "^0.17.0",
    "unplugin-vue-components": "^0.26.0"
  }
}
```

**Vite配置：**
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia'],
      dts: true
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

### 4.2 导航组件

**Vue3顶部导航组件：**
```vue
<template>
  <el-header class="app-header">
    <div class="header-left">
      <el-icon @click="toggleSidebar" class="menu-toggle">
        <Fold v-if="!collapsed" />
        <Expand v-else />
      </el-icon>
      <img src="/logo.png" alt="Logo" class="logo" />
      <span class="title">进销存管理系统</span>
    </div>

    <div class="header-right">
      <el-badge :value="notificationCount" class="notification">
        <el-icon><Bell /></el-icon>
      </el-badge>

      <el-dropdown @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :src="userInfo.avatar" />
          <span>{{ userInfo.name }}</span>
          <el-icon><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人资料</el-dropdown-item>
            <el-dropdown-item command="settings">系统设置</el-dropdown-item>
            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { Fold, Expand, Bell, ArrowDown } from '@element-plus/icons-vue'

const userStore = useUserStore()
const collapsed = ref(false)

const userInfo = computed(() => userStore.userInfo)
const notificationCount = computed(() => userStore.notificationCount)

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      break
    case 'settings':
      // 跳转到设置页面
      break
    case 'logout':
      userStore.logout()
      break
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-toggle {
  cursor: pointer;
  font-size: 18px;
}

.logo {
  height: 32px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}
</style>
```

**左侧菜单组件：**
```vue
<template>
  <el-aside :width="collapsed ? '64px' : '240px'" class="app-sidebar">
    <el-menu
      :default-active="activeMenu"
      :collapse="collapsed"
      :unique-opened="true"
      router
      background-color="#001529"
      text-color="#ffffff"
      active-text-color="#1890ff"
    >
      <el-menu-item index="/dashboard">
        <el-icon><House /></el-icon>
        <template #title>仪表板</template>
      </el-menu-item>

      <el-sub-menu index="product">
        <template #title>
          <el-icon><Goods /></el-icon>
          <span>商品管理</span>
        </template>
        <el-menu-item index="/products">商品列表</el-menu-item>
        <el-menu-item index="/categories">商品分类</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="inventory">
        <template #title>
          <el-icon><Box /></el-icon>
          <span>库存管理</span>
        </template>
        <el-menu-item index="/inventory/list">库存查询</el-menu-item>
        <el-menu-item index="/inventory/adjust">库存调整</el-menu-item>
        <el-menu-item index="/inventory/check">库存盘点</el-menu-item>
      </el-sub-menu>
    </el-menu>
  </el-aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { House, Goods, Box } from '@element-plus/icons-vue'

interface Props {
  collapsed: boolean
}

defineProps<Props>()

const route = useRoute()
const activeMenu = computed(() => route.path)
</script>

<style scoped>
.app-sidebar {
  background-color: #001529;
  transition: width 0.3s;
}

.el-menu {
  border-right: none;
}
</style>
```

### 4.2 表单组件

**输入框：**
- 高度：32px
- 边框：1px solid #D9D9D9
- 圆角：6px
- 聚焦色：#1890FF

**按钮：**
- 主按钮：背景#1890FF，文字#FFFFFF
- 次按钮：背景#FFFFFF，边框#D9D9D9
- 危险按钮：背景#F5222D，文字#FFFFFF
- 高度：32px，圆角6px

**选择器：**
- 下拉框：与输入框样式保持一致
- 多选框：16px × 16px
- 单选框：16px 圆形

### 4.4 Pinia状态管理

**用户状态管理：**
```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginRequest } from '@/types/user'
import { login, logout, getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])

  const isLoggedIn = computed(() => !!token.value)
  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission) || permissions.value.includes('*')
  })

  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const clearToken = () => {
    token.value = ''
    localStorage.removeItem('token')
  }

  const loginAction = async (loginData: LoginRequest) => {
    try {
      const response = await login(loginData)
      setToken(response.data.token)
      await getUserInfoAction()
      return response
    } catch (error) {
      throw error
    }
  }

  const logoutAction = async () => {
    try {
      await logout()
    } finally {
      clearToken()
      userInfo.value = null
      permissions.value = []
    }
  }

  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      userInfo.value = response.data.user
      permissions.value = response.data.permissions
    } catch (error) {
      clearToken()
      throw error
    }
  }

  return {
    token,
    userInfo,
    permissions,
    isLoggedIn,
    hasPermission,
    login: loginAction,
    logout: logoutAction,
    getUserInfo: getUserInfoAction
  }
})
```

### 4.5 数据展示组件

**Vue3表格组件：**
```vue
<template>
  <div class="data-table">
    <el-table
      :data="tableData"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :formatter="column.formatter"
        :sortable="column.sortable"
      >
        <template #default="{ row }" v-if="column.slot">
          <slot :name="column.slot" :row="row" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

interface Column {
  prop: string
  label: string
  width?: number
  formatter?: (row: any, column: any, cellValue: any) => string
  sortable?: boolean
  slot?: string
}

interface Props {
  columns: Column[]
  data: any[]
  loading?: boolean
  total?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  total: 0
})

const emit = defineEmits<{
  edit: [row: any]
  delete: [row: any]
  selectionChange: [selection: any[]]
  pageChange: [page: number, pageSize: number]
}>()

const tableData = computed(() => props.data)
const selectedRows = ref<any[]>([])

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: props.total
})

const handleEdit = (row: any) => {
  emit('edit', row)
}

const handleDelete = (row: any) => {
  emit('delete', row)
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
  emit('selectionChange', selection)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  emit('pageChange', pagination.page, pagination.pageSize)
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  emit('pageChange', pagination.page, pagination.pageSize)
}
</script>

<style scoped>
.data-table {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
```

## 5. 页面设计

### 5.1 登录页面

**布局设计：**
```mermaid
graph TB
    subgraph "登录页面布局"
        A[页面背景 - 渐变色]
        B[登录卡片 - 居中]
        C[Logo和标题]
        D[登录表单]
        E[记住密码/忘记密码]
        F[登录按钮]
        G[版权信息]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    A --> G
```

**设计要点：**
- 卡片宽度：400px
- 表单字段：用户名、密码
- 验证码：图形验证码
- 错误提示：表单下方红色文字

### 5.2 仪表板页面

**布局设计：**
```mermaid
graph TB
    subgraph "仪表板布局"
        A[统计卡片区 - 4列]
        B[图表区域 - 2列]
        C[快捷操作区 - 1列]
        D[待办事项区 - 1列]
        E[最新动态区 - 全宽]
    end
    
    A --> B
    A --> C
    B --> D
    C --> D
    B --> E
    D --> E
```

**统计卡片：**
- 今日销售额
- 库存总值
- 待处理订单
- 库存预警

**图表组件：**
- 销售趋势图 (折线图)
- 商品分类占比 (饼图)
- 库存周转率 (柱状图)

### 5.3 商品管理页面

**页面结构：**
```mermaid
graph TB
    subgraph "商品管理页面"
        A[页面标题和操作按钮]
        B[搜索和筛选区域]
        C[商品列表表格]
        D[分页组件]
    end
    
    A --> B
    B --> C
    C --> D
```

**表格列设计：**
- 商品图片 (60px缩略图)
- 商品编码
- 商品名称
- 分类
- 库存数量
- 成本价格
- 销售价格
- 状态
- 操作 (编辑/删除/查看)

### 5.4 订单管理页面

**订单列表：**
- 订单号
- 客户/供应商
- 订单日期
- 订单金额
- 订单状态
- 操作按钮

**订单详情：**
- 基本信息卡片
- 商品明细表格
- 金额汇总
- 操作历史

## 6. 交互设计

### 6.1 操作反馈

**加载状态：**
- 按钮加载：显示loading图标
- 页面加载：骨架屏或进度条
- 数据加载：表格loading状态

**成功反馈：**
- 操作成功：绿色通知消息
- 保存成功：按钮变绿2秒
- 提交成功：页面跳转或刷新

**错误处理：**
- 表单验证：字段下方红色提示
- 操作失败：红色通知消息
- 网络错误：重试按钮

### 6.2 确认操作

**删除确认：**
```
标题：确认删除
内容：确定要删除这个商品吗？删除后无法恢复。
按钮：取消 | 确定删除
```

**批量操作：**
```
标题：批量操作确认
内容：已选择 X 个项目，确定要执行批量删除吗？
按钮：取消 | 确定执行
```

## 7. 移动端设计

### 7.1 移动端适配

**导航设计：**
- 底部Tab导航 (4-5个主要功能)
- 顶部标题栏 + 汉堡菜单
- 侧滑抽屉菜单

**表格适配：**
- 卡片式布局替代表格
- 重要信息优先显示
- 滑动查看更多信息

**表单优化：**
- 大号输入框 (44px高度)
- 数字键盘适配
- 日期选择器优化

### 7.2 手势交互

**常用手势：**
- 下拉刷新
- 上拉加载更多
- 左滑删除 (列表项)
- 长按多选

## 8. 无障碍设计

### 8.1 可访问性要求

**键盘导航：**
- Tab键顺序合理
- 焦点状态明显
- 快捷键支持

**屏幕阅读器：**
- 语义化HTML标签
- alt属性完整
- aria标签支持

**视觉辅助：**
- 高对比度模式
- 字体大小调节
- 色盲友好设计

## 9. 设计规范检查清单

### 9.1 视觉检查

- [ ] 色彩使用符合规范
- [ ] 字体大小和行高合适
- [ ] 间距使用统一标准
- [ ] 图标风格一致
- [ ] 对比度满足要求

### 9.2 交互检查

- [ ] 操作反馈及时明确
- [ ] 错误提示友好
- [ ] 加载状态清晰
- [ ] 确认操作安全
- [ ] 快捷操作便利

### 9.3 响应式检查

- [ ] 各断点布局正常
- [ ] 移动端操作便利
- [ ] 触摸目标大小合适
- [ ] 横竖屏适配良好
- [ ] 性能表现优秀

## 10. 设计交付物

### 10.1 设计文件

- Figma设计稿
- 组件库文件
- 图标资源包
- 设计规范文档

### 10.2 开发资源

- CSS样式指南
- 组件代码示例
- 切图资源
- 字体文件

### 10.3 测试用例

- 界面一致性测试
- 交互功能测试
- 响应式测试
- 无障碍测试

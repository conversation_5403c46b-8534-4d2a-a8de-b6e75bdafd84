# 数据备份策略优化设计

**文档版本：** v1.0  
**创建日期：** 2025-07-02  
**最后更新：** 2025-07-02  
**文档状态：** 草稿

## 1. 概述

### 1.1 优化目标

针对PISP系统采用物理删除策略，设计完善的数据备份和恢复机制，确保数据安全性和业务连续性。

### 1.2 备份策略分层

```mermaid
graph TB
    subgraph "备份策略分层"
        A[实时备份层] --> A1[操作前备份]
        A[实时备份层] --> A2[关键数据镜像]
        A[实时备份层] --> A3[事务日志备份]
        
        B[定期备份层] --> B1[全量备份]
        B[定期备份层] --> B2[增量备份]
        B[定期备份层] --> B3[差异备份]
        
        C[长期归档层] --> C1[历史数据归档]
        C[长期归档层] --> C2[合规性备份]
        C[长期归档层] --> C3[冷存储备份]
    end
```

## 2. 实时备份机制

### 2.1 操作前自动备份

```java
@Component
public class PreOperationBackupService {
    
    @Autowired
    private BackupRepository backupRepository;
    
    @Autowired
    private CompressionService compressionService;
    
    /**
     * 删除操作前自动备份
     */
    @EventListener
    public void handlePreDeleteEvent(PreDeleteEvent event) {
        try {
            // 1. 获取要删除的数据
            Object entityData = event.getEntityData();
            String tableName = event.getTableName();
            String primaryKey = event.getPrimaryKey();
            
            // 2. 序列化数据
            String jsonData = JsonUtils.toJson(entityData);
            
            // 3. 压缩数据（大于1KB的数据进行压缩）
            byte[] backupData = jsonData.length() > 1024 ? 
                compressionService.compress(jsonData) : jsonData.getBytes();
            
            // 4. 创建备份记录
            OperationBackup backup = OperationBackup.builder()
                .backupId(generateBackupId())
                .tableName(tableName)
                .primaryKey(primaryKey)
                .operationType(OperationType.DELETE)
                .originalData(backupData)
                .isCompressed(jsonData.length() > 1024)
                .dataSize(backupData.length)
                .backupTime(LocalDateTime.now())
                .operatorId(getCurrentUserId())
                .retentionDays(getRetentionDays(tableName))
                .build();
            
            backupRepository.save(backup);
            
            // 5. 记录备份日志
            log.info("Pre-delete backup created: table={}, primaryKey={}, backupId={}", 
                tableName, primaryKey, backup.getBackupId());
                
        } catch (Exception e) {
            log.error("Failed to create pre-delete backup", e);
            // 备份失败时阻止删除操作
            throw new BackupException("数据备份失败，删除操作已取消", e);
        }
    }
    
    /**
     * 根据表名获取数据保留天数
     */
    private int getRetentionDays(String tableName) {
        Map<String, Integer> retentionPolicy = Map.of(
            "users", 365,           // 用户数据保留1年
            "products", 180,        // 商品数据保留6个月
            "orders", 2555,         // 订单数据保留7年（合规要求）
            "inventory", 90,        // 库存数据保留3个月
            "financial", 2555       // 财务数据保留7年
        );
        
        return retentionPolicy.getOrDefault(tableName, 90); // 默认90天
    }
}
```

### 2.2 关键数据实时镜像

```java
@Service
public class DataMirrorService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private MirrorRepository mirrorRepository;
    
    /**
     * 关键数据变更时创建镜像
     */
    @EventListener
    public void handleDataChangeEvent(DataChangeEvent event) {
        if (isCriticalTable(event.getTableName())) {
            createDataMirror(event);
        }
    }
    
    private void createDataMirror(DataChangeEvent event) {
        String mirrorKey = String.format("mirror:%s:%s:%d", 
            event.getTableName(), 
            event.getPrimaryKey(),
            System.currentTimeMillis());
        
        DataMirror mirror = DataMirror.builder()
            .mirrorKey(mirrorKey)
            .tableName(event.getTableName())
            .primaryKey(event.getPrimaryKey())
            .beforeData(event.getBeforeData())
            .afterData(event.getAfterData())
            .operationType(event.getOperationType())
            .operatorId(event.getOperatorId())
            .mirrorTime(LocalDateTime.now())
            .build();
        
        // 存储到Redis（快速访问）
        redisTemplate.opsForValue().set(mirrorKey, mirror, Duration.ofDays(7));
        
        // 存储到数据库（持久化）
        mirrorRepository.save(mirror);
    }
    
    private boolean isCriticalTable(String tableName) {
        Set<String> criticalTables = Set.of(
            "users", "roles", "permissions", 
            "products", "customers", "suppliers",
            "orders", "order_items", "payments",
            "inventory", "warehouses"
        );
        return criticalTables.contains(tableName);
    }
}
```

## 3. 定期备份策略

### 3.1 全量备份

```bash
#!/bin/bash
# full_backup.sh - 全量备份脚本

BACKUP_DIR="/data/backups/full"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="pisp_system"
RETENTION_DAYS=30

# 创建备份目录
mkdir -p ${BACKUP_DIR}/${DATE}

# 执行全量备份
echo "开始全量备份: $(date)"
pg_dump -h localhost -U pisp_user -d ${DB_NAME} \
    --format=custom \
    --compress=9 \
    --verbose \
    --file=${BACKUP_DIR}/${DATE}/full_backup_${DATE}.dump

# 备份配置文件
cp -r /app/config ${BACKUP_DIR}/${DATE}/
cp -r /app/logs ${BACKUP_DIR}/${DATE}/

# 创建备份清单
cat > ${BACKUP_DIR}/${DATE}/backup_manifest.txt << EOF
备份类型: 全量备份
备份时间: $(date)
数据库: ${DB_NAME}
备份文件: full_backup_${DATE}.dump
配置文件: config/
日志文件: logs/
备份大小: $(du -sh ${BACKUP_DIR}/${DATE} | cut -f1)
EOF

# 验证备份完整性
pg_restore --list ${BACKUP_DIR}/${DATE}/full_backup_${DATE}.dump > /dev/null
if [ $? -eq 0 ]; then
    echo "备份验证成功: $(date)"
else
    echo "备份验证失败: $(date)"
    exit 1
fi

# 清理过期备份
find ${BACKUP_DIR} -type d -mtime +${RETENTION_DAYS} -exec rm -rf {} \;

echo "全量备份完成: $(date)"
```

### 3.2 增量备份

```bash
#!/bin/bash
# incremental_backup.sh - 增量备份脚本

BACKUP_DIR="/data/backups/incremental"
DATE=$(date +%Y%m%d_%H%M%S)
WAL_DIR="/var/lib/postgresql/data/pg_wal"
LAST_BACKUP_FILE="/tmp/last_backup_lsn"

# 创建备份目录
mkdir -p ${BACKUP_DIR}/${DATE}

# 获取上次备份的LSN
if [ -f ${LAST_BACKUP_FILE} ]; then
    LAST_LSN=$(cat ${LAST_BACKUP_FILE})
else
    LAST_LSN="0/0"
fi

# 获取当前LSN
CURRENT_LSN=$(psql -h localhost -U pisp_user -d pisp_system -t -c "SELECT pg_current_wal_lsn();")

echo "开始增量备份: $(date)"
echo "上次备份LSN: ${LAST_LSN}"
echo "当前LSN: ${CURRENT_LSN}"

# 备份WAL文件
pg_receivewal -h localhost -U pisp_user -D ${BACKUP_DIR}/${DATE}/wal --synchronous

# 创建增量备份清单
cat > ${BACKUP_DIR}/${DATE}/incremental_manifest.txt << EOF
备份类型: 增量备份
备份时间: $(date)
起始LSN: ${LAST_LSN}
结束LSN: ${CURRENT_LSN}
WAL文件: wal/
备份大小: $(du -sh ${BACKUP_DIR}/${DATE} | cut -f1)
EOF

# 更新最后备份LSN
echo ${CURRENT_LSN} > ${LAST_BACKUP_FILE}

echo "增量备份完成: $(date)"
```

## 4. 数据恢复机制

### 4.1 单条记录恢复

```java
@Service
public class DataRecoveryService {
    
    @Autowired
    private BackupRepository backupRepository;
    
    @Autowired
    private CompressionService compressionService;
    
    /**
     * 恢复单条删除的记录
     */
    public void recoverDeletedRecord(String backupId) {
        // 1. 查找备份记录
        OperationBackup backup = backupRepository.findByBackupId(backupId);
        if (backup == null) {
            throw new RecoveryException("备份记录不存在: " + backupId);
        }
        
        // 2. 检查是否已过期
        if (backup.isExpired()) {
            throw new RecoveryException("备份记录已过期: " + backupId);
        }
        
        // 3. 解压数据
        String jsonData = backup.isCompressed() ? 
            compressionService.decompress(backup.getOriginalData()) :
            new String(backup.getOriginalData());
        
        // 4. 根据表名恢复数据
        switch (backup.getTableName()) {
            case "products":
                recoverProduct(jsonData);
                break;
            case "users":
                recoverUser(jsonData);
                break;
            case "orders":
                recoverOrder(jsonData);
                break;
            default:
                throw new RecoveryException("不支持的表类型: " + backup.getTableName());
        }
        
        // 5. 标记为已恢复
        backup.setRecoveredAt(LocalDateTime.now());
        backup.setRecoveredBy(getCurrentUserId());
        backupRepository.updateById(backup);
        
        // 6. 记录恢复日志
        auditService.logDataRecovery(backupId, backup.getTableName(), 
            backup.getPrimaryKey(), getCurrentUserId());
    }
    
    private void recoverProduct(String jsonData) {
        Product product = JsonUtils.fromJson(jsonData, Product.class);
        
        // 检查是否存在冲突
        if (productService.existsByProductCode(product.getProductCode())) {
            throw new RecoveryException("商品编码已存在，无法恢复: " + product.getProductCode());
        }
        
        // 恢复商品数据
        productService.save(product);
        
        // 清理缓存
        cacheService.evictProductCache(product.getId());
    }
}
```

### 4.2 批量数据恢复

```java
@Service
public class BatchRecoveryService {
    
    /**
     * 按时间范围批量恢复
     */
    public BatchRecoveryResult recoverByTimeRange(LocalDateTime startTime, 
                                                 LocalDateTime endTime,
                                                 String tableName) {
        
        List<OperationBackup> backups = backupRepository.findByTimeRange(
            startTime, endTime, tableName);
        
        BatchRecoveryResult result = new BatchRecoveryResult();
        
        for (OperationBackup backup : backups) {
            try {
                dataRecoveryService.recoverDeletedRecord(backup.getBackupId());
                result.addSuccess(backup.getBackupId());
            } catch (Exception e) {
                result.addFailure(backup.getBackupId(), e.getMessage());
                log.error("Failed to recover backup: " + backup.getBackupId(), e);
            }
        }
        
        return result;
    }
    
    /**
     * 按操作员批量恢复
     */
    public BatchRecoveryResult recoverByOperator(Long operatorId, 
                                               LocalDateTime startTime,
                                               LocalDateTime endTime) {
        
        List<OperationBackup> backups = backupRepository.findByOperator(
            operatorId, startTime, endTime);
        
        // 按依赖关系排序（先恢复主表，再恢复从表）
        backups.sort(this::compareByDependency);
        
        BatchRecoveryResult result = new BatchRecoveryResult();
        
        for (OperationBackup backup : backups) {
            try {
                dataRecoveryService.recoverDeletedRecord(backup.getBackupId());
                result.addSuccess(backup.getBackupId());
            } catch (Exception e) {
                result.addFailure(backup.getBackupId(), e.getMessage());
            }
        }
        
        return result;
    }
    
    private int compareByDependency(OperationBackup a, OperationBackup b) {
        // 定义表的依赖关系优先级
        Map<String, Integer> priority = Map.of(
            "users", 1,
            "roles", 1,
            "products", 2,
            "customers", 2,
            "orders", 3,
            "order_items", 4
        );
        
        return Integer.compare(
            priority.getOrDefault(a.getTableName(), 999),
            priority.getOrDefault(b.getTableName(), 999)
        );
    }
}
```

## 5. 备份数据表设计

### 5.1 操作备份表

```sql
-- 操作备份表
CREATE TABLE operation_backups (
    id BIGSERIAL PRIMARY KEY,
    backup_id VARCHAR(36) UNIQUE NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    primary_key VARCHAR(100) NOT NULL,
    operation_type VARCHAR(20) NOT NULL, -- DELETE, UPDATE, INSERT
    original_data BYTEA NOT NULL,
    is_compressed BOOLEAN DEFAULT FALSE,
    data_size INTEGER NOT NULL,
    backup_time TIMESTAMP NOT NULL,
    operator_id BIGINT NOT NULL,
    retention_days INTEGER NOT NULL,
    recovered_at TIMESTAMP,
    recovered_by BIGINT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_operation_backups_table ON operation_backups(table_name);
CREATE INDEX idx_operation_backups_time ON operation_backups(backup_time);
CREATE INDEX idx_operation_backups_operator ON operation_backups(operator_id);
CREATE INDEX idx_operation_backups_retention ON operation_backups(backup_time, retention_days);

-- 分区（按月分区）
CREATE TABLE operation_backups_y2025m01 PARTITION OF operation_backups
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 5.2 数据镜像表

```sql
-- 数据镜像表
CREATE TABLE data_mirrors (
    id BIGSERIAL PRIMARY KEY,
    mirror_key VARCHAR(200) UNIQUE NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    primary_key VARCHAR(100) NOT NULL,
    before_data JSONB,
    after_data JSONB,
    operation_type VARCHAR(20) NOT NULL,
    operator_id BIGINT NOT NULL,
    mirror_time TIMESTAMP NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_data_mirrors_table ON data_mirrors(table_name);
CREATE INDEX idx_data_mirrors_time ON data_mirrors(mirror_time);
CREATE INDEX idx_data_mirrors_key ON data_mirrors(primary_key);
```

## 6. 备份监控与告警

### 6.1 备份状态监控

```java
@Component
public class BackupMonitor {
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void monitorBackupHealth() {
        // 1. 检查备份失败率
        checkBackupFailureRate();
        
        // 2. 检查备份存储空间
        checkBackupStorageSpace();
        
        // 3. 检查备份数据完整性
        checkBackupIntegrity();
        
        // 4. 检查过期数据清理
        checkExpiredDataCleanup();
    }
    
    private void checkBackupFailureRate() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        
        long totalBackups = backupRepository.countByTimeRange(oneHourAgo, LocalDateTime.now());
        long failedBackups = backupRepository.countFailedByTimeRange(oneHourAgo, LocalDateTime.now());
        
        if (totalBackups > 0) {
            double failureRate = (double) failedBackups / totalBackups;
            
            if (failureRate > 0.1) { // 失败率超过10%
                alertService.sendAlert(AlertType.BACKUP_FAILURE_RATE_HIGH, 
                    String.format("备份失败率过高: %.2f%% (%d/%d)", 
                        failureRate * 100, failedBackups, totalBackups));
            }
        }
    }
    
    private void checkBackupStorageSpace() {
        long usedSpace = backupStorageService.getUsedSpace();
        long totalSpace = backupStorageService.getTotalSpace();
        double usageRate = (double) usedSpace / totalSpace;
        
        if (usageRate > 0.9) { // 使用率超过90%
            alertService.sendAlert(AlertType.BACKUP_STORAGE_FULL,
                String.format("备份存储空间不足: %.2f%% (%s/%s)",
                    usageRate * 100,
                    FileUtils.humanReadableByteCount(usedSpace),
                    FileUtils.humanReadableByteCount(totalSpace)));
        }
    }
}
```

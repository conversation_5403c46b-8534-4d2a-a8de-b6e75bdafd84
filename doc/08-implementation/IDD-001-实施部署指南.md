# IDD-001 进销存管理系统实施部署指南

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | IDD-001 |
| 文档名称 | 实施部署指南 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 草稿 |
| 作者 | 运维工程师 |

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-25 | 初始版本创建 | 运维工程师 |

## 1. 部署概述

### 1.1 部署目标

- **高可用性：** 确保系统7×24小时稳定运行
- **可扩展性：** 支持业务增长的扩容需求
- **安全性：** 建立多层次安全防护体系
- **可维护性：** 便于日常运维和故障处理
- **性能优化：** 满足业务性能要求

### 1.2 部署架构

```mermaid
graph TB
    subgraph "负载均衡层"
        A1[Nginx LB 1]
        A2[Nginx LB 2]
    end
    
    subgraph "Web服务层"
        B1[Web Server 1]
        B2[Web Server 2]
        B3[Web Server 3]
    end
    
    subgraph "应用服务层"
        C1[App Server 1]
        C2[App Server 2]
        C3[App Server 3]
    end
    
    subgraph "缓存层"
        D1[Redis Master]
        D2[Redis Slave]
    end
    
    subgraph "数据库层"
        E1[PostgreSQL Master]
        E2[PostgreSQL Slave]
    end
    
    subgraph "存储层"
        F1[NFS/Object Storage]
    end
    
    Internet --> A1
    Internet --> A2
    A1 --> B1
    A1 --> B2
    A2 --> B2
    A2 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D1
    C3 --> D1
    D1 --> D2
    
    C1 --> E1
    C2 --> E1
    C3 --> E1
    E1 --> E2
    
    C1 --> F1
    C2 --> F1
    C3 --> F1
```

### 1.3 环境规划

**生产环境配置：**
- **负载均衡器：** 2台 (4C8G)
- **Web服务器：** 3台 (8C16G)
- **应用服务器：** 3台 (16C32G)
- **数据库服务器：** 2台 (32C64G)
- **缓存服务器：** 2台 (8C16G)

## 2. 环境准备

### 2.1 系统要求

**操作系统：**
- CentOS 8+ / Ubuntu 20.04+
- 内核版本 4.18+
- 时区设置为 Asia/Shanghai

**软件依赖：**
```bash
# 基础软件包
yum install -y wget curl vim git unzip

# Java 21安装
wget https://download.oracle.com/java/21/latest/jdk-21_linux-x64_bin.tar.gz
tar -xzf jdk-21_linux-x64_bin.tar.gz -C /opt/
ln -s /opt/jdk-21.0.1 /opt/java

# 设置环境变量
echo 'export JAVA_HOME=/opt/java' >> /etc/profile
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> /etc/profile
source /etc/profile

# Maven安装
wget https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.tar.gz
tar -xzf apache-maven-3.9.6-bin.tar.gz -C /opt/
ln -s /opt/apache-maven-3.9.6 /opt/maven

echo 'export MAVEN_HOME=/opt/maven' >> /etc/profile
echo 'export PATH=$MAVEN_HOME/bin:$PATH' >> /etc/profile
source /etc/profile

# 网络工具
yum install -y net-tools telnet nc

# 监控工具
yum install -y htop iotop nethogs
```

### 2.2 Docker环境

**Docker安装：**
```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash

# 启动Docker服务
systemctl enable docker
systemctl start docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

**Docker配置优化：**
```json
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "data-root": "/var/lib/docker"
}
```

### 2.3 网络配置

**防火墙配置：**
```bash
# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=3000/tcp
firewall-cmd --permanent --add-port=5432/tcp
firewall-cmd --permanent --add-port=6379/tcp
firewall-cmd --reload
```

**内核参数优化：**
```bash
# /etc/sysctl.conf
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 10
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_tw_recycle = 1
vm.swappiness = 10
vm.overcommit_memory = 1

# 应用配置
sysctl -p
```

## 3. 数据库部署

### 3.1 PostgreSQL主从部署

**主库配置：**
```bash
# docker-compose-postgres-master.yml
version: '3.8'
services:
  postgres-master:
    image: postgres:14
    container_name: postgres-master
    environment:
      POSTGRES_DB: inventory_db
      POSTGRES_USER: inventory_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: ${REPLICATION_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
      - ./pg_hba.conf:/etc/postgresql/pg_hba.conf
    ports:
      - "5432:5432"
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    restart: unless-stopped

volumes:
  postgres_data:
```

**主库配置文件 (postgresql.conf)：**
```ini
# 基础配置
listen_addresses = '*'
port = 5432
max_connections = 200

# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL配置
wal_level = replica
max_wal_senders = 3
wal_keep_segments = 64
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/archive/%f'

# 复制配置
hot_standby = on
max_standby_streaming_delay = 30s
```

**从库配置：**
```bash
# 创建从库
pg_basebackup -h master_ip -D /var/lib/postgresql/data -U replicator -v -P -W

# recovery.conf
standby_mode = 'on'
primary_conninfo = 'host=master_ip port=5432 user=replicator password=replication_password'
trigger_file = '/tmp/postgresql.trigger'
```

### 3.2 Redis集群部署

**Redis主从配置：**
```bash
# docker-compose-redis.yml
version: '3.8'
services:
  redis-master:
    image: redis:7-alpine
    container_name: redis-master
    command: redis-server /etc/redis/redis.conf
    volumes:
      - redis_data:/data
      - ./redis-master.conf:/etc/redis/redis.conf
    ports:
      - "6379:6379"
    restart: unless-stopped

  redis-slave:
    image: redis:7-alpine
    container_name: redis-slave
    command: redis-server /etc/redis/redis.conf
    volumes:
      - redis_slave_data:/data
      - ./redis-slave.conf:/etc/redis/redis.conf
    ports:
      - "6380:6379"
    depends_on:
      - redis-master
    restart: unless-stopped

volumes:
  redis_data:
  redis_slave_data:
```

**Redis配置文件：**
```ini
# redis-master.conf
bind 0.0.0.0
port 6379
requirepass ${REDIS_PASSWORD}
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
```

## 4. 应用部署

### 4.1 应用容器化

**Java应用Dockerfile：**
```dockerfile
# 多阶段构建
FROM maven:3.9.6-openjdk-21-slim AS builder

WORKDIR /app
COPY pom.xml .
COPY */pom.xml ./*/
RUN mvn dependency:go-offline

COPY . .
RUN mvn clean package -DskipTests

# 生产镜像
FROM openjdk:21-jre-slim

RUN addgroup --system spring && adduser --system spring --ingroup spring

WORKDIR /app

COPY --from=builder --chown=spring:spring /app/target/*.jar app.jar

USER spring

EXPOSE 8080

ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport"
ENV SPRING_PROFILES_ACTIVE=prod

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

**Maven多模块构建：**
```dockerfile
# 父项目Dockerfile
FROM maven:3.9.6-openjdk-21-slim AS builder

WORKDIR /app

# 复制所有pom.xml文件
COPY pom.xml .
COPY inventory-common/pom.xml inventory-common/
COPY inventory-gateway/pom.xml inventory-gateway/
COPY inventory-user/pom.xml inventory-user/
COPY inventory-product/pom.xml inventory-product/
COPY inventory-order/pom.xml inventory-order/
COPY inventory-inventory/pom.xml inventory-inventory/

# 下载依赖
RUN mvn dependency:go-offline

# 复制源码并构建
COPY . .
RUN mvn clean package -DskipTests

# 各服务的运行时镜像
FROM openjdk:21-jre-slim AS shenyu-gateway
COPY --from=builder /app/inventory-gateway/target/*.jar app.jar
EXPOSE 9195
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"
CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

FROM openjdk:21-jre-slim AS user-service
COPY --from=builder /app/inventory-user/target/*.jar app.jar
EXPOSE 8081
CMD ["java", "-jar", "app.jar"]

FROM openjdk:21-jre-slim AS product-service
COPY --from=builder /app/inventory-product/target/*.jar app.jar
EXPOSE 8082
CMD ["java", "-jar", "app.jar"]
```

**ShenYu网关部署配置：**
```yaml
# docker-compose-shenyu.yml
version: '3.8'
services:
  shenyu-admin:
    image: apache/shenyu-admin:*******
    container_name: shenyu-admin
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************************
      SPRING_DATASOURCE_USERNAME: ${DB_USERNAME}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD}
    ports:
      - "9095:9095"
    depends_on:
      - postgres-master
    restart: unless-stopped
    volumes:
      - shenyu_admin_logs:/opt/shenyu-admin/logs

  shenyu-gateway:
    build:
      context: .
      target: shenyu-gateway
    container_name: shenyu-gateway
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SHENYU_REGISTER_SERVERLIST: http://eureka-server:8761/eureka
      SHENYU_ADMIN_URL: http://shenyu-admin:9095
    ports:
      - "9195:9195"
    depends_on:
      - shenyu-admin
      - eureka-server
    restart: unless-stopped
    volumes:
      - shenyu_gateway_logs:/opt/shenyu-bootstrap/logs

  eureka-server:
    build:
      context: .
      target: eureka-server
    container_name: eureka-server
    environment:
      SPRING_PROFILES_ACTIVE: docker
    ports:
      - "8761:8761"
    restart: unless-stopped

  user-service:
    build:
      context: .
      target: user-service
    container_name: user-service-1
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ***************************************************
      SPRING_DATASOURCE_USERNAME: ${DB_USERNAME}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD}
      SPRING_REDIS_HOST: redis-master
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka-server:8761/eureka
      JWT_SECRET: ${JWT_SECRET}
    volumes:
      - app_logs:/app/logs
    ports:
      - "8081:8081"
    depends_on:
      - postgres-master
      - redis-master
      - eureka-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  product-service:
    build:
      context: .
      target: product-service
    container_name: product-service-1
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ***************************************************
      SPRING_DATASOURCE_USERNAME: ${DB_USERNAME}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD}
      SPRING_REDIS_HOST: redis-master
      EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE: http://eureka-server:8761/eureka
    ports:
      - "8082:8082"
    depends_on:
      - postgres-master
      - redis-master
      - eureka-server
    restart: unless-stopped

volumes:
  app_logs:
```

**Spring Boot配置文件：**
```yaml
# application.yml (公共配置)
spring:
  application:
    name: inventory-system
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: ${SPRING_DATASOURCE_URL:*********************************************}
    username: ${SPRING_DATASOURCE_USERNAME:inventory_user}
    password: ${SPRING_DATASOURCE_PASSWORD:inventory_pass}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: InventoryHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  redis:
    host: ${SPRING_REDIS_HOST:localhost}
    port: ${SPRING_REDIS_PORT:6379}
    password: ${SPRING_REDIS_PASSWORD:}
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # ShenYu网关配置 (仅在网关服务中使用)
  shenyu:
    register:
      registerType: eureka
      serverLists: ${EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE:http://localhost:8761/eureka}
    cross:
      enabled: true
      allowedHeaders: "*"
      allowedMethods: "*"
      allowedOrigin: "*"
      allowCredentials: true
      maxAge: 18000
    switchConfig:
      local: true
    file:
      enabled: true
    exclude:
      enabled: false
      paths:
        - /favicon.ico
    extPlugin:
      path: ""
      enabled: true
      threads: 1
      scheduleTime: 300
      scheduleDelay: 30

eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE:http://localhost:8761/eureka}
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*Mapper.xml

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.inventory: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/application.log
    max-size: 100MB
    max-history: 30

jwt:
  secret: ${JWT_SECRET:mySecretKey}
  expiration: 86400
```

### 4.2 负载均衡配置

**Nginx配置：**
```nginx
# /etc/nginx/conf.d/inventory.conf
upstream app_servers {
    least_conn;
    server app-server-1:3000 weight=1 max_fails=3 fail_timeout=30s;
    server app-server-2:3000 weight=1 max_fails=3 fail_timeout=30s;
    server app-server-3:3000 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name inventory.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name inventory.example.com;

    # SSL配置
    ssl_certificate /etc/ssl/certs/inventory.crt;
    ssl_certificate_key /etc/ssl/private/inventory.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers on;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;

    # 静态文件
    location /static/ {
        alias /var/www/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理到ShenYu网关
    location /api/ {
        proxy_pass http://shenyu-gateway:9195;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # ShenYu Admin管理界面
    location /shenyu-admin/ {
        proxy_pass http://shenyu-admin:9095/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://app_servers/health;
        access_log off;
    }

    # 主应用
    location / {
        proxy_pass http://app_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 5. 监控部署

### 5.1 Prometheus监控

**Prometheus配置：**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'app-servers'
    static_configs:
      - targets: ['app-server-1:3000', 'app-server-2:3000']
    metrics_path: '/metrics'
```

**告警规则：**
```yaml
# alert_rules.yml
groups:
  - name: system
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"

  - name: database
    rules:
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"

      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
```

### 5.2 日志收集

**ELK Stack部署：**
```yaml
# docker-compose-elk.yml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
    volumes:
      - es_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: logstash
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

volumes:
  es_data:
```

## 6. 部署脚本

### 6.1 自动化部署脚本

**部署脚本：**
```bash
#!/bin/bash
# deploy.sh

set -e

# 配置变量
APP_NAME="inventory-system"
VERSION=${1:-latest}
ENVIRONMENT=${2:-production}

echo "开始部署 $APP_NAME 版本 $VERSION 到 $ENVIRONMENT 环境"

# 1. 拉取最新代码
git pull origin main

# 2. 构建镜像
docker build -t $APP_NAME:$VERSION .

# 3. 停止旧容器
docker-compose -f docker-compose-$ENVIRONMENT.yml down

# 4. 数据库迁移
docker run --rm \
  --network inventory_network \
  -e DATABASE_URL=$DATABASE_URL \
  $APP_NAME:$VERSION \
  npm run migrate

# 5. 启动新容器
docker-compose -f docker-compose-$ENVIRONMENT.yml up -d

# 6. 健康检查
echo "等待服务启动..."
sleep 30

for i in {1..10}; do
  if curl -f http://localhost:3000/health; then
    echo "服务启动成功"
    break
  fi
  echo "等待服务启动... ($i/10)"
  sleep 10
done

echo "部署完成"
```

### 6.2 前置仓服务部署脚本

**前置仓服务部署配置：**
```bash
#!/bin/bash
# deploy-front-warehouse.sh

set -e

echo "开始部署前置仓管理服务"

# 1. 构建前置仓服务镜像
docker build -t pisp/front-warehouse-service:latest ./pisp-front-warehouse-service/

# 2. 部署前置仓服务
docker-compose -f docker-compose-front-warehouse.yml up -d

# 3. 健康检查
echo "等待前置仓服务启动..."
sleep 30

for i in {1..10}; do
  if curl -f http://localhost:8010/actuator/health; then
    echo "前置仓服务启动成功"
    break
  fi
  echo "等待前置仓服务启动... ($i/10)"
  sleep 10
done

echo "前置仓服务部署完成"
```

**前置仓服务Docker Compose配置：**
```yaml
# docker-compose-front-warehouse.yml
version: '3.8'

services:
  pisp-front-warehouse-service:
    image: pisp/front-warehouse-service:latest
    container_name: pisp-front-warehouse-service
    environment:
      SPRING_PROFILES_ACTIVE: production
      DATABASE_URL: ****************************************************
      REDIS_URL: redis://redis:6379
      ROCKETMQ_NAMESERVER: rocketmq-nameserver:9876
      EUREKA_CLIENT_SERVICE_URL: http://eureka-server:8761/eureka
    ports:
      - "8010:8010"
    depends_on:
      - postgres
      - redis
      - rocketmq-nameserver
      - eureka-server
    restart: unless-stopped
    volumes:
      - front_warehouse_logs:/opt/app/logs
    networks:
      - pisp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8010/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  front_warehouse_logs:

networks:
  pisp-network:
    external: true
```

### 6.3 监控组件部署脚本

**监控栈部署脚本：**
```bash
#!/bin/bash
# deploy-monitoring.sh

set -e

echo "开始部署监控告警组件"

# 1. 创建监控配置目录
mkdir -p ./monitoring/{prometheus,grafana,alertmanager}

# 2. 复制配置文件
cp ./config/prometheus.yml ./monitoring/prometheus/
cp ./config/alertmanager.yml ./monitoring/alertmanager/
cp -r ./config/grafana/* ./monitoring/grafana/

# 3. 部署监控栈
docker-compose -f docker-compose-monitoring.yml up -d

# 4. 等待服务启动
echo "等待监控服务启动..."
sleep 60

# 5. 健康检查
echo "检查Prometheus..."
curl -f http://localhost:9090/-/healthy || echo "Prometheus启动失败"

echo "检查Grafana..."
curl -f http://localhost:3000/api/health || echo "Grafana启动失败"

echo "检查AlertManager..."
curl -f http://localhost:9093/-/healthy || echo "AlertManager启动失败"

echo "监控组件部署完成"
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3000 (admin/admin123)"
echo "AlertManager: http://localhost:9093"
```

## 7. 运维手册补充

### 7.1 前置仓服务运维

**服务启停操作：**
```bash
# 启动前置仓服务
docker-compose -f docker-compose-front-warehouse.yml up -d

# 停止前置仓服务
docker-compose -f docker-compose-front-warehouse.yml down

# 重启前置仓服务
docker-compose -f docker-compose-front-warehouse.yml restart

# 查看服务状态
docker-compose -f docker-compose-front-warehouse.yml ps

# 查看服务日志
docker-compose -f docker-compose-front-warehouse.yml logs -f pisp-front-warehouse-service
```

**常见故障处理：**

1. **库存同步失败**
   ```bash
   # 检查RocketMQ连接
   curl http://localhost:8010/actuator/health/rocketmq

   # 检查数据库连接
   curl http://localhost:8010/actuator/health/db

   # 重启库存同步任务
   curl -X POST http://localhost:8010/api/v1/admin/sync/restart
   ```

2. **订单分配异常**
   ```bash
   # 检查订单分配队列
   curl http://localhost:8010/api/v1/admin/queues/order-allocation

   # 清理异常订单
   curl -X POST http://localhost:8010/api/v1/admin/orders/cleanup
   ```

### 7.2 监控告警运维

**告警规则管理：**
```bash
# 重新加载Prometheus配置
curl -X POST http://localhost:9090/-/reload

# 检查告警规则
curl http://localhost:9090/api/v1/rules

# 查看活跃告警
curl http://localhost:9090/api/v1/alerts

# 静默告警
curl -X POST http://localhost:9093/api/v1/silences \
  -H "Content-Type: application/json" \
  -d '{"matchers":[{"name":"alertname","value":"HighErrorRate"}],"startsAt":"2025-07-02T10:00:00Z","endsAt":"2025-07-02T12:00:00Z","comment":"维护期间静默"}'
```

**监控面板管理：**
```bash
# 导入Grafana面板
curl -X POST ************************************/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @./monitoring/grafana/dashboards/front-warehouse.json

# 备份Grafana配置
curl -X GET ************************************/api/dashboards/export \
  > grafana-backup-$(date +%Y%m%d).json
```

### 7.3 高危操作运维

**高危操作审批流程：**

1. **操作申请**
   ```bash
   # 提交高危操作申请
   curl -X POST http://localhost:8010/api/v1/admin/high-risk-operations \
     -H "Content-Type: application/json" \
     -d '{
       "operationType": "DELETE_FRONT_WAREHOUSE",
       "targetId": "warehouse-001",
       "reason": "前置仓关闭",
       "applicant": "<EMAIL>"
     }'
   ```

2. **操作审批**
   ```bash
   # 审批高危操作
   curl -X PUT http://localhost:8010/api/v1/admin/high-risk-operations/{id}/approve \
     -H "Content-Type: application/json" \
     -d '{
       "approver": "<EMAIL>",
       "comment": "已确认业务需求"
     }'
   ```

3. **操作执行**
   ```bash
   # 执行高危操作
   curl -X POST http://localhost:8010/api/v1/admin/high-risk-operations/{id}/execute \
     -H "Content-Type: application/json" \
     -d '{
       "confirmationToken": "token-from-email",
       "executor": "<EMAIL>"
     }'
   ```

**数据备份和恢复：**
```bash
# 创建数据备份
pg_dump -h localhost -U pisp_user -d pisp_front_warehouse > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据备份
psql -h localhost -U pisp_user -d pisp_front_warehouse < backup_20250702_100000.sql

# 验证数据完整性
curl http://localhost:8010/api/v1/admin/data/integrity-check
```

# 7. 清理旧镜像
docker image prune -f

echo "部署完成"
```

### 6.2 回滚脚本

**回滚脚本：**
```bash
#!/bin/bash
# rollback.sh

set -e

PREVIOUS_VERSION=${1:-previous}
ENVIRONMENT=${2:-production}

echo "开始回滚到版本 $PREVIOUS_VERSION"

# 1. 停止当前服务
docker-compose -f docker-compose-$ENVIRONMENT.yml down

# 2. 启动之前版本
docker-compose -f docker-compose-$ENVIRONMENT.yml up -d

# 3. 健康检查
sleep 30
if curl -f http://localhost:3000/health; then
  echo "回滚成功"
else
  echo "回滚失败，请检查日志"
  exit 1
fi
```

## 7. 运维手册

### 7.1 日常维护

**系统监控检查：**
```bash
# 检查系统资源
htop
df -h
free -h

# 检查服务状态
docker ps
systemctl status docker
systemctl status nginx

# 检查日志
docker logs app-server-1
tail -f /var/log/nginx/access.log
```

**数据库维护：**
```sql
-- 检查数据库连接
SELECT count(*) FROM pg_stat_activity;

-- 检查表大小
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 重建索引
REINDEX DATABASE inventory_db;

-- 更新统计信息
ANALYZE;
```

### 7.2 故障处理

**常见故障及解决方案：**

| 故障现象 | 可能原因 | 解决方案 |
|----------|----------|----------|
| 服务无响应 | 内存不足 | 重启服务，增加内存 |
| 数据库连接失败 | 连接池耗尽 | 重启应用，优化连接池配置 |
| 页面加载慢 | 缓存失效 | 重启Redis，预热缓存 |
| 磁盘空间不足 | 日志文件过大 | 清理日志，配置日志轮转 |

**紧急处理流程：**
```bash
# 1. 快速诊断
./scripts/health-check.sh

# 2. 查看错误日志
docker logs --tail=100 app-server-1

# 3. 重启服务
docker-compose restart app-server-1

# 4. 如果问题严重，执行回滚
./scripts/rollback.sh previous production
```

### 7.3 备份恢复

**自动备份脚本：**
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h postgres-master -U inventory_user inventory_db | gzip > $BACKUP_DIR/database.sql.gz

# 文件备份
tar -czf $BACKUP_DIR/uploads.tar.gz /app/uploads

# 配置备份
tar -czf $BACKUP_DIR/config.tar.gz /etc/nginx /etc/ssl

# 清理7天前的备份
find /backup -type d -mtime +7 -exec rm -rf {} \;

echo "备份完成: $BACKUP_DIR"
```

**恢复脚本：**
```bash
#!/bin/bash
# restore.sh

BACKUP_DATE=${1:-$(date +%Y%m%d)}
BACKUP_DIR="/backup/$BACKUP_DATE"

if [ ! -d "$BACKUP_DIR" ]; then
  echo "备份目录不存在: $BACKUP_DIR"
  exit 1
fi

# 停止服务
docker-compose down

# 恢复数据库
gunzip -c $BACKUP_DIR/database.sql.gz | psql -h postgres-master -U inventory_user inventory_db

# 恢复文件
tar -xzf $BACKUP_DIR/uploads.tar.gz -C /

# 启动服务
docker-compose up -d

echo "恢复完成"
```

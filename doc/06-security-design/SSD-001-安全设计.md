# SSD-001 进销存管理系统安全设计

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档编号 | SSD-001 |
| 文档名称 | 安全设计文档 |
| 版本号 | v1.0 |
| 创建日期 | 2025-06-25 |
| 最后修改 | 2025-06-25 |
| 文档状态 | 草稿 |
| 作者 | 安全架构师 |

## 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-25 | 初始版本创建 | 安全架构师 |

## 1. 安全设计概述

### 1.1 安全目标

- **机密性 (Confidentiality)：** 确保敏感数据不被未授权访问
- **完整性 (Integrity)：** 保证数据的准确性和完整性
- **可用性 (Availability)：** 确保系统的持续可用性
- **可审计性 (Auditability)：** 提供完整的操作审计追踪
- **不可否认性 (Non-repudiation)：** 确保操作的不可否认性

### 1.2 安全威胁模型

```mermaid
graph TB
    subgraph "外部威胁"
        A1[网络攻击]
        A2[恶意软件]
        A3[社会工程]
        A4[DDoS攻击]
    end
    
    subgraph "内部威胁"
        B1[权限滥用]
        B2[数据泄露]
        B3[误操作]
        B4[内部攻击]
    end
    
    subgraph "系统威胁"
        C1[软件漏洞]
        C2[配置错误]
        C3[系统故障]
        C4[数据损坏]
    end
    
    subgraph "防护措施"
        D1[网络防护]
        D2[身份认证]
        D3[权限控制]
        D4[数据加密]
        D5[审计监控]
    end
    
    A1 --> D1
    A2 --> D1
    B1 --> D2
    B2 --> D3
    C1 --> D4
    C2 --> D5
```

### 1.3 安全合规要求

- **等保2.0三级：** 符合网络安全等级保护要求
- **ISO 27001：** 信息安全管理体系标准
- **GDPR：** 通用数据保护条例（如适用）
- **SOX：** 萨班斯-奥克斯利法案（如适用）

## 2. 安全架构设计

### 2.1 分层安全架构

```mermaid
graph TB
    subgraph "网络安全层"
        A1[防火墙]
        A2[WAF]
        A3[IDS/IPS]
        A4[DDoS防护]
    end
    
    subgraph "应用安全层"
        B1[身份认证]
        B2[授权控制]
        B3[会话管理]
        B4[输入验证]
        B5[输出编码]
    end
    
    subgraph "数据安全层"
        C1[数据加密]
        C2[数据脱敏]
        C3[数据备份]
        C4[数据销毁]
    end
    
    subgraph "基础设施安全层"
        D1[主机加固]
        D2[网络隔离]
        D3[安全监控]
        D4[漏洞管理]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    B5 --> C1
    B1 --> C2
    C3 --> D1
    C4 --> D2
```

### 2.2 零信任安全模型

```mermaid
graph LR
    subgraph "用户层"
        A1[内部用户]
        A2[外部用户]
        A3[系统用户]
    end
    
    subgraph "身份验证"
        B1[多因子认证]
        B2[设备认证]
        B3[行为分析]
    end
    
    subgraph "访问控制"
        C1[最小权限]
        C2[动态授权]
        C3[上下文感知]
    end
    
    subgraph "持续监控"
        D1[实时监控]
        D2[异常检测]
        D3[风险评估]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
```

## 3. 身份认证与授权

### 3.1 多因子认证 (MFA)

**认证因子：**
- **知识因子：** 密码、PIN码
- **持有因子：** 手机短信、硬件令牌
- **生物因子：** 指纹、面部识别（可选）

**Spring Security实现：**
```java
@Service
public class MFAService {

    @Autowired
    private UserService userService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public MFAChallenge initiateLogin(String username, String password) {
        // 1. 验证用户名密码
        UserDetails user = userService.loadUserByUsername(username);
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new BadCredentialsException("Invalid credentials");
        }

        // 2. 检查MFA设置
        MFASettings mfaSettings = getMFASettings(user.getUsername());
        if (!mfaSettings.isEnabled()) {
            return completeLogin(user);
        }

        // 3. 发送MFA挑战
        String challengeId = UUID.randomUUID().toString();
        String code = generateMFACode();

        // 存储到Redis，5分钟过期
        redisTemplate.opsForValue().set(
            "mfa:challenge:" + challengeId,
            new MFAChallenge(user.getUsername(), code),
            Duration.ofMinutes(5)
        );

        // 发送短信验证码
        smsService.sendVerificationCode(user.getPhone(), code);

        return MFAChallenge.builder()
                .challengeId(challengeId)
                .method(mfaSettings.getMethod())
                .expiresAt(LocalDateTime.now().plusMinutes(5))
                .build();
    }

    public AuthResult verifyMFA(String challengeId, String code) {
        String key = "mfa:challenge:" + challengeId;
        MFAChallenge challenge = (MFAChallenge) redisTemplate.opsForValue().get(key);

        if (challenge == null) {
            throw new BadCredentialsException("Invalid or expired challenge");
        }

        if (!challenge.getCode().equals(code)) {
            throw new BadCredentialsException("Invalid MFA code");
        }

        // 删除挑战
        redisTemplate.delete(key);

        // 完成登录
        UserDetails user = userService.loadUserByUsername(challenge.getUsername());
        return completeLogin(user);
    }

    private String generateMFACode() {
        return String.format("%06d", new Random().nextInt(999999));
    }
}
```

### 3.2 基于角色的访问控制 (RBAC)

**权限模型：**
```mermaid
erDiagram
    USER {
        uuid id PK
        string username
        string email
        enum status
    }
    
    ROLE {
        uuid id PK
        string name
        string description
        json permissions
    }
    
    PERMISSION {
        uuid id PK
        string resource
        string action
        json conditions
    }
    
    USER_ROLE {
        uuid user_id FK
        uuid role_id FK
        datetime assigned_at
        datetime expires_at
    }
    
    ROLE_PERMISSION {
        uuid role_id FK
        uuid permission_id FK
    }
    
    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : assigned_to
    ROLE ||--o{ ROLE_PERMISSION : has
    PERMISSION ||--o{ ROLE_PERMISSION : granted_by
```

**Spring Security权限检查：**
```java
@Service
public class AuthorizationService {

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private RolePermissionService rolePermissionService;

    @PreAuthorize("hasPermission(#resource, #action)")
    public boolean checkPermission(Long userId, String resource, String action) {
        return checkPermission(userId, resource, action, null);
    }

    public boolean checkPermission(Long userId, String resource, String action, Object context) {
        // 1. 获取用户角色
        List<Role> userRoles = userRoleService.getUserRoles(userId);

        // 2. 获取角色权限
        List<Permission> permissions = rolePermissionService.getRolePermissions(
            userRoles.stream().map(Role::getId).collect(Collectors.toList())
        );

        // 3. 检查权限匹配
        for (Permission permission : permissions) {
            if (matchesPermission(permission, resource, action)) {
                // 4. 检查条件约束
                if (evaluateConditions(permission.getConditions(), context)) {
                    return true;
                }
            }
        }

        return false;
    }

    private boolean matchesPermission(Permission permission, String resource, String action) {
        boolean resourceMatch = "*".equals(permission.getResource()) ||
                               resource.equals(permission.getResource()) ||
                               resource.startsWith(permission.getResource() + ":");

        boolean actionMatch = "*".equals(permission.getAction()) ||
                             action.equals(permission.getAction());

        return resourceMatch && actionMatch;
    }

    private boolean evaluateConditions(String conditions, Object context) {
        if (StringUtils.isEmpty(conditions)) {
            return true;
        }

        // 使用SpEL表达式评估条件
        SpelExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(conditions);

        StandardEvaluationContext evalContext = new StandardEvaluationContext();
        evalContext.setVariable("context", context);
        evalContext.setVariable("user", SecurityUtils.getCurrentUser());

        return Boolean.TRUE.equals(expression.getValue(evalContext, Boolean.class));
    }
}

@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {

    @Autowired
    private AuthorizationService authorizationService;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        Long userId = ((UserPrincipal) authentication.getPrincipal()).getId();
        String resource = targetDomainObject.toString();
        String action = permission.toString();

        return authorizationService.checkPermission(userId, resource, action);
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        return hasPermission(authentication, targetType, permission);
    }
}
```

### 3.3 Spring Security配置

**安全配置类：**
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private JwtAccessDeniedHandler jwtAccessDeniedHandler;

    @Autowired
    private UserDetailsService userDetailsService;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }

    @Bean
    public JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter() {
        return new JwtAuthenticationTokenFilter();
    }

    @Bean
    public PermissionEvaluator permissionEvaluator() {
        return new CustomPermissionEvaluator();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/public/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .anyRequest().authenticated()
            )
            .exceptionHandling(ex -> ex
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                .accessDeniedHandler(jwtAccessDeniedHandler)
            )
            .addFilterBefore(jwtAuthenticationTokenFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
}
```

### 3.4 JWT Token安全

**Token结构：**
```java
public class JwtPayload {
    private String sub;           // 用户ID
    private Long iat;            // 签发时间
    private Long exp;            // 过期时间
    private String jti;          // Token ID
    private List<String> roles;  // 用户角色
    private List<String> permissions; // 权限列表
    private String sessionId;    // 会话ID
}
```

**JWT Token服务：**
```java
@Service
public class JwtTokenService {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration:3600}")
    private int jwtExpiration;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public String generateToken(UserPrincipal user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("username", user.getUsername());
        claims.put("roles", user.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList()));

        String jti = UUID.randomUUID().toString();
        String sessionId = generateSessionId();

        claims.put("jti", jti);
        claims.put("sessionId", sessionId);

        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(user.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration * 1000L))
                .setIssuer("inventory-system")
                .setAudience("inventory-users")
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();

        // 存储会话信息到Redis
        redisTemplate.opsForValue().set(
            "session:" + sessionId,
            user.getId().toString(),
            Duration.ofSeconds(jwtExpiration)
        );

        return token;
    }

    public Claims getClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(jwtSecret)
                .parseClaimsJws(token)
                .getBody();
    }

    public boolean validateToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);

            // 1. 检查黑名单
            String jti = claims.get("jti", String.class);
            if (isTokenBlacklisted(jti)) {
                return false;
            }

            // 2. 检查会话有效性
            String sessionId = claims.get("sessionId", String.class);
            return isValidSession(sessionId);

        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }

    public void revokeToken(String token) {
        Claims claims = getClaimsFromToken(token);
        String jti = claims.get("jti", String.class);
        String sessionId = claims.get("sessionId", String.class);

        // 添加到黑名单
        redisTemplate.opsForValue().set(
            "blacklist:" + jti,
            "revoked",
            Duration.ofSeconds(jwtExpiration)
        );

        // 删除会话
        redisTemplate.delete("session:" + sessionId);
    }

    private boolean isTokenBlacklisted(String jti) {
        return redisTemplate.hasKey("blacklist:" + jti);
    }

    private boolean isValidSession(String sessionId) {
        return redisTemplate.hasKey("session:" + sessionId);
    }

    private String generateSessionId() {
        return UUID.randomUUID().toString();
    }
}
```

## 4. 数据安全

### 4.1 数据加密

**加密策略：**
- **传输加密：** TLS 1.3
- **存储加密：** AES-256-GCM
- **字段级加密：** 敏感字段单独加密
- **密钥管理：** 密钥轮换和托管

**实现示例：**
```typescript
class DataEncryption {
    private readonly algorithm = 'aes-256-gcm';
    
    encrypt(data: string, key: Buffer): EncryptedData {
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(this.algorithm, key, iv);
        
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const authTag = cipher.getAuthTag();
        
        return {
            encrypted,
            iv: iv.toString('hex'),
            authTag: authTag.toString('hex')
        };
    }
    
    decrypt(encryptedData: EncryptedData, key: Buffer): string {
        const decipher = crypto.createDecipher(
            this.algorithm, 
            key, 
            Buffer.from(encryptedData.iv, 'hex')
        );
        
        decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
        
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }
}
```

### 4.2 数据脱敏

**脱敏规则：**
```typescript
class DataMasking {
    private maskingRules: Map<string, MaskingRule> = new Map([
        ['phone', { type: 'partial', keepStart: 3, keepEnd: 4, maskChar: '*' }],
        ['email', { type: 'email', maskChar: '*' }],
        ['idCard', { type: 'partial', keepStart: 6, keepEnd: 4, maskChar: '*' }],
        ['bankAccount', { type: 'partial', keepStart: 4, keepEnd: 4, maskChar: '*' }]
    ]);
    
    maskData(data: any, context: string = 'default'): any {
        if (typeof data !== 'object') return data;
        
        const masked = { ...data };
        
        for (const [field, value] of Object.entries(masked)) {
            if (this.shouldMask(field, context)) {
                masked[field] = this.applyMasking(field, value);
            }
        }
        
        return masked;
    }
    
    private applyMasking(field: string, value: string): string {
        const rule = this.maskingRules.get(field);
        if (!rule) return value;
        
        switch (rule.type) {
            case 'partial':
                return this.partialMask(value, rule);
            case 'email':
                return this.emailMask(value, rule.maskChar);
            case 'full':
                return rule.maskChar.repeat(value.length);
            default:
                return value;
        }
    }
}
```

### 4.3 数据备份与恢复

**备份策略：**
```mermaid
graph TB
    subgraph "备份类型"
        A1[全量备份]
        A2[增量备份]
        A3[差异备份]
    end
    
    subgraph "备份频率"
        B1[实时备份]
        B2[每日备份]
        B3[每周备份]
        B4[每月备份]
    end
    
    subgraph "存储位置"
        C1[本地存储]
        C2[异地存储]
        C3[云端存储]
    end
    
    subgraph "恢复策略"
        D1[Point-in-Time]
        D2[完整恢复]
        D3[部分恢复]
    end
    
    A1 --> B3
    A2 --> B2
    A3 --> B1
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
```

## 5. 网络安全

### 5.1 网络架构安全

**网络分区：**
```mermaid
graph TB
    subgraph "DMZ区"
        A1[负载均衡器]
        A2[Web服务器]
        A3[反向代理]
    end
    
    subgraph "应用区"
        B1[应用服务器]
        B2[API网关]
        B3[缓存服务器]
    end
    
    subgraph "数据区"
        C1[数据库服务器]
        C2[文件服务器]
        C3[备份服务器]
    end
    
    subgraph "管理区"
        D1[监控服务器]
        D2[日志服务器]
        D3[跳板机]
    end
    
    Internet --> A1
    A1 --> A2
    A2 --> B1
    B1 --> C1
    D3 --> D1
    D1 --> D2
```

### 5.2 API安全

**API安全措施：**
```typescript
class APISecurityMiddleware {
    // 1. 请求限流
    async rateLimiting(req: Request, res: Response, next: NextFunction) {
        const key = `rate_limit:${req.ip}:${req.path}`;
        const current = await this.redis.incr(key);
        
        if (current === 1) {
            await this.redis.expire(key, 60); // 1分钟窗口
        }
        
        if (current > 100) { // 每分钟最多100次请求
            return res.status(429).json({
                error: 'Too Many Requests',
                retryAfter: 60
            });
        }
        
        next();
    }
    
    // 2. 输入验证
    validateInput(schema: any) {
        return (req: Request, res: Response, next: NextFunction) => {
            const { error } = schema.validate(req.body);
            if (error) {
                return res.status(400).json({
                    error: 'Validation Error',
                    details: error.details
                });
            }
            next();
        };
    }
    
    // 3. SQL注入防护
    sanitizeInput(req: Request, res: Response, next: NextFunction) {
        const sanitize = (obj: any): any => {
            if (typeof obj === 'string') {
                return obj.replace(/['"\\;]/g, ''); // 简单示例
            }
            if (typeof obj === 'object' && obj !== null) {
                for (const key in obj) {
                    obj[key] = sanitize(obj[key]);
                }
            }
            return obj;
        };
        
        req.body = sanitize(req.body);
        req.query = sanitize(req.query);
        next();
    }
}
```

### 5.3 HTTPS配置

**TLS配置：**
```nginx
server {
    listen 443 ssl http2;
    server_name inventory.example.com;
    
    # SSL证书配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Content-Security-Policy "default-src 'self'";
}
```

## 6. 安全监控与审计

### 6.1 安全事件监控

**监控指标：**
```typescript
class SecurityMonitor {
    private readonly alertThresholds = {
        failedLogins: 5,        // 5次失败登录
        suspiciousIPs: 10,      // 可疑IP访问
        privilegeEscalation: 1, // 权限提升尝试
        dataExfiltration: 100   // 大量数据下载
    };
    
    async monitorSecurityEvents(): Promise<void> {
        // 1. 监控失败登录
        await this.checkFailedLogins();
        
        // 2. 监控异常访问
        await this.checkSuspiciousAccess();
        
        // 3. 监控权限变更
        await this.checkPrivilegeChanges();
        
        // 4. 监控数据访问
        await this.checkDataAccess();
    }
    
    private async checkFailedLogins(): Promise<void> {
        const recentFailures = await this.auditService.getFailedLogins(
            new Date(Date.now() - 15 * 60 * 1000) // 最近15分钟
        );
        
        const ipFailures = this.groupByIP(recentFailures);
        
        for (const [ip, failures] of ipFailures) {
            if (failures.length >= this.alertThresholds.failedLogins) {
                await this.alertService.sendSecurityAlert({
                    type: 'FAILED_LOGIN_THRESHOLD',
                    ip,
                    count: failures.length,
                    severity: 'HIGH'
                });
                
                // 自动封禁IP
                await this.firewallService.blockIP(ip, 3600); // 封禁1小时
            }
        }
    }
}
```

### 6.2 审计日志

**审计日志格式：**
```typescript
interface AuditLog {
    id: string;
    timestamp: Date;
    userId: string;
    sessionId: string;
    action: string;
    resource: string;
    resourceId?: string;
    oldValue?: any;
    newValue?: any;
    ipAddress: string;
    userAgent: string;
    result: 'SUCCESS' | 'FAILURE';
    errorMessage?: string;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

class AuditLogger {
    async logAction(
        userId: string,
        action: string,
        resource: string,
        details: any,
        request: Request
    ): Promise<void> {
        const auditLog: AuditLog = {
            id: this.generateId(),
            timestamp: new Date(),
            userId,
            sessionId: this.extractSessionId(request),
            action,
            resource,
            resourceId: details.resourceId,
            oldValue: details.oldValue,
            newValue: details.newValue,
            ipAddress: this.getClientIP(request),
            userAgent: request.headers['user-agent'] || '',
            result: details.result || 'SUCCESS',
            errorMessage: details.error,
            riskLevel: this.calculateRiskLevel(action, resource)
        };
        
        // 存储到审计数据库
        await this.auditRepository.save(auditLog);
        
        // 高风险操作实时告警
        if (auditLog.riskLevel === 'CRITICAL') {
            await this.alertService.sendCriticalAlert(auditLog);
        }
    }
    
    private calculateRiskLevel(action: string, resource: string): string {
        const highRiskActions = ['DELETE', 'BULK_DELETE', 'EXPORT'];
        const criticalResources = ['USER', 'ROLE', 'PERMISSION'];
        
        if (highRiskActions.includes(action) && criticalResources.includes(resource)) {
            return 'CRITICAL';
        }
        
        if (highRiskActions.includes(action) || criticalResources.includes(resource)) {
            return 'HIGH';
        }
        
        return 'MEDIUM';
    }
}
```

## 7. 安全测试

### 7.1 安全测试类型

**测试分类：**
```mermaid
graph TB
    subgraph "静态安全测试"
        A1[代码审计]
        A2[依赖扫描]
        A3[配置检查]
    end
    
    subgraph "动态安全测试"
        B1[渗透测试]
        B2[漏洞扫描]
        B3[模糊测试]
    end
    
    subgraph "交互式测试"
        C1[IAST测试]
        C2[运行时保护]
        C3[实时监控]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3
```

### 7.2 安全测试用例

**SQL注入测试：**
```typescript
describe('SQL Injection Protection', () => {
    test('should prevent SQL injection in login', async () => {
        const maliciousInput = "admin'; DROP TABLE users; --";
        
        const response = await request(app)
            .post('/api/v1/auth/login')
            .send({
                username: maliciousInput,
                password: 'password'
            });
        
        expect(response.status).toBe(400);
        expect(response.body.error).toContain('Invalid input');
        
        // 验证数据库表仍然存在
        const userCount = await User.count();
        expect(userCount).toBeGreaterThan(0);
    });
});
```

**XSS防护测试：**
```typescript
describe('XSS Protection', () => {
    test('should sanitize script tags in product description', async () => {
        const maliciousScript = '<script>alert("XSS")</script>';
        
        const response = await request(app)
            .post('/api/v1/products')
            .set('Authorization', `Bearer ${validToken}`)
            .send({
                name: 'Test Product',
                description: maliciousScript
            });
        
        expect(response.status).toBe(201);
        expect(response.body.data.description).not.toContain('<script>');
    });
});
```

## 8. 安全运维

### 8.1 安全配置管理

**配置基线：**
```yaml
# 安全配置基线
security_baseline:
  password_policy:
    min_length: 12
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_symbols: true
    max_age_days: 90
    history_count: 12
  
  session_management:
    timeout_minutes: 30
    max_concurrent_sessions: 3
    secure_cookies: true
    httponly_cookies: true
  
  encryption:
    algorithm: "AES-256-GCM"
    key_rotation_days: 90
    tls_version: "1.3"
  
  audit:
    log_all_actions: true
    retention_days: 2555  # 7年
    real_time_alerts: true
```

### 8.2 安全事件响应

**事件响应流程：**
```mermaid
flowchart TD
    A[安全事件检测] --> B{事件严重程度}
    B -->|低| C[记录日志]
    B -->|中| D[自动响应]
    B -->|高| E[人工介入]
    B -->|严重| F[紧急响应]
    
    C --> G[定期审查]
    D --> H[通知管理员]
    E --> I[隔离威胁]
    F --> J[启动应急预案]
    
    H --> K[事件分析]
    I --> K
    J --> K
    
    K --> L[修复措施]
    L --> M[事后总结]
    M --> N[更新安全策略]
```

### 8.3 漏洞管理

**漏洞处理流程：**
```typescript
class VulnerabilityManager {
    async scanVulnerabilities(): Promise<VulnerabilityReport[]> {
        const reports: VulnerabilityReport[] = [];
        
        // 1. 依赖漏洞扫描
        const depVulns = await this.scanDependencies();
        reports.push(...depVulns);
        
        // 2. 代码漏洞扫描
        const codeVulns = await this.scanCode();
        reports.push(...codeVulns);
        
        // 3. 配置漏洞扫描
        const configVulns = await this.scanConfiguration();
        reports.push(...configVulns);
        
        // 4. 基础设施漏洞扫描
        const infraVulns = await this.scanInfrastructure();
        reports.push(...infraVulns);
        
        return this.prioritizeVulnerabilities(reports);
    }
    
    private prioritizeVulnerabilities(vulns: VulnerabilityReport[]): VulnerabilityReport[] {
        return vulns.sort((a, b) => {
            // CVSS评分排序
            const scoreA = a.cvssScore || 0;
            const scoreB = b.cvssScore || 0;
            
            if (scoreA !== scoreB) {
                return scoreB - scoreA; // 高分优先
            }
            
            // 可利用性排序
            const exploitabilityOrder = ['HIGH', 'MEDIUM', 'LOW'];
            const exploitA = exploitabilityOrder.indexOf(a.exploitability);
            const exploitB = exploitabilityOrder.indexOf(b.exploitability);
            
            return exploitA - exploitB;
        });
    }
}
```

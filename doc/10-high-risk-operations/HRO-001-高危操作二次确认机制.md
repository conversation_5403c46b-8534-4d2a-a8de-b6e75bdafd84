# 高危操作二次确认机制设计

**文档版本：** v1.0  
**创建日期：** 2025-07-02  
**最后更新：** 2025-07-02  
**文档状态：** 草稿

## 1. 概述

### 1.1 设计目标

为PISP进销存管理系统设计完善的高危操作二次确认机制，确保关键业务数据的安全性，防止误操作导致的数据丢失或业务中断。

### 1.2 适用范围

本设计适用于PISP系统中所有涉及数据删除、批量操作、权限变更等高风险操作的场景。

## 2. 高危操作定义与分类

### 2.1 高危操作分类

```mermaid
graph TB
    subgraph "高危操作分类"
        A[数据删除类] --> A1[单条记录删除]
        A[数据删除类] --> A2[批量删除]
        A[数据删除类] --> A3[级联删除]
        
        B[权限管理类] --> B1[用户权限变更]
        B[权限管理类] --> B2[角色权限修改]
        B[权限管理类] --> B3[系统配置变更]
        
        C[财务操作类] --> C1[订单取消]
        C[财务操作类] --> C2[退款处理]
        C[财务操作类] --> C3[价格调整]
        
        D[库存操作类] --> D1[库存清零]
        D[库存操作类] --> D2[批量调整]
        D[库存操作类] --> D3[盘点确认]
        
        E[系统管理类] --> E1[数据导出]
        E[系统操作类] --> E2[系统重置]
        E[系统管理类] --> E3[备份恢复]
    end
```

### 2.2 风险等级定义

| 风险等级 | 描述 | 确认方式 | 示例操作 |
|----------|------|----------|----------|
| **CRITICAL** | 极高风险，不可逆操作 | 三重确认 + 管理员授权 | 删除用户、清空库存、系统重置 |
| **HIGH** | 高风险，影响业务连续性 | 双重确认 + 密码验证 | 批量删除、订单取消、权限变更 |
| **MEDIUM** | 中等风险，可能影响数据完整性 | 单次确认 + 操作说明 | 单条删除、价格调整、状态变更 |
| **LOW** | 低风险，常规操作 | 简单确认 | 查询、导出、查看 |

## 3. 二次确认机制设计

### 3.1 确认流程设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端界面
    participant B as 后端服务
    participant A as 审计服务
    participant N as 通知服务

    U->>F: 执行高危操作
    F->>F: 检查操作风险等级
    
    alt CRITICAL级别
        F->>U: 显示三重确认对话框
        U->>F: 第一次确认
        F->>U: 输入操作原因
        U->>F: 第二次确认
        F->>U: 输入当前用户密码
        U->>F: 第三次确认
        F->>B: 发送操作请求
        B->>B: 验证用户权限
        B->>B: 验证密码
        B->>A: 记录审计日志
        B->>N: 发送管理员通知
        B->>F: 返回操作结果
    else HIGH级别
        F->>U: 显示双重确认对话框
        U->>F: 第一次确认
        F->>U: 输入操作原因
        U->>F: 输入密码确认
        F->>B: 发送操作请求
        B->>A: 记录审计日志
        B->>F: 返回操作结果
    else MEDIUM级别
        F->>U: 显示确认对话框
        U->>F: 确认操作
        F->>B: 发送操作请求
        B->>A: 记录审计日志
        B->>F: 返回操作结果
    end
```

### 3.2 前端确认组件设计

#### 3.2.1 CRITICAL级别确认组件

```typescript
interface CriticalConfirmProps {
  title: string;
  content: string;
  operationType: string;
  resourceInfo: any;
  onConfirm: (reason: string) => Promise<void>;
  onCancel: () => void;
}

const CriticalConfirmDialog: React.FC<CriticalConfirmProps> = ({
  title,
  content,
  operationType,
  resourceInfo,
  onConfirm,
  onCancel
}) => {
  const [step, setStep] = useState(1);
  const [reason, setReason] = useState('');
  const [password, setPassword] = useState('');
  const [confirmText, setConfirmText] = useState('');

  const handleFinalConfirm = async () => {
    if (confirmText !== '确认删除') {
      message.error('请输入正确的确认文本');
      return;
    }
    
    try {
      await onConfirm(reason);
      message.success('操作执行成功');
    } catch (error) {
      message.error('操作执行失败：' + error.message);
    }
  };

  return (
    <Modal
      title={<span style={{ color: '#ff4d4f' }}>⚠️ {title}</span>}
      open={true}
      footer={null}
      closable={false}
      width={600}
    >
      {step === 1 && (
        <div>
          <Alert
            message="危险操作警告"
            description={content}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <p><strong>操作类型：</strong>{operationType}</p>
          <p><strong>影响资源：</strong>{JSON.stringify(resourceInfo)}</p>
          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Button onClick={onCancel} style={{ marginRight: 8 }}>取消</Button>
            <Button type="primary" danger onClick={() => setStep(2)}>
              我了解风险，继续操作
            </Button>
          </div>
        </div>
      )}
      
      {step === 2 && (
        <div>
          <p><strong>请说明操作原因：</strong></p>
          <TextArea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="请详细说明执行此操作的原因..."
            rows={4}
            maxLength={500}
          />
          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Button onClick={() => setStep(1)} style={{ marginRight: 8 }}>上一步</Button>
            <Button 
              type="primary" 
              danger 
              disabled={reason.length < 10}
              onClick={() => setStep(3)}
            >
              下一步
            </Button>
          </div>
        </div>
      )}
      
      {step === 3 && (
        <div>
          <p><strong>请输入当前用户密码：</strong></p>
          <Input.Password
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="请输入密码"
          />
          <p style={{ marginTop: 16 }}><strong>请输入"确认删除"以最终确认：</strong></p>
          <Input
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder="请输入：确认删除"
          />
          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Button onClick={() => setStep(2)} style={{ marginRight: 8 }}>上一步</Button>
            <Button 
              type="primary" 
              danger 
              disabled={!password || confirmText !== '确认删除'}
              onClick={handleFinalConfirm}
            >
              最终确认执行
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};
```

## 4. 后端验证机制

### 4.1 高危操作注解

```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface HighRiskOperation {
    
    /**
     * 风险等级
     */
    RiskLevel riskLevel() default RiskLevel.MEDIUM;
    
    /**
     * 操作描述
     */
    String description() default "";
    
    /**
     * 资源类型
     */
    String resourceType() default "";
    
    /**
     * 是否需要管理员授权
     */
    boolean requireAdminAuth() default false;
    
    /**
     * 是否需要密码验证
     */
    boolean requirePasswordVerify() default false;
}

public enum RiskLevel {
    LOW("低风险"),
    MEDIUM("中等风险"), 
    HIGH("高风险"),
    CRITICAL("极高风险");
    
    private final String description;
    
    RiskLevel(String description) {
        this.description = description;
    }
}
```

### 4.2 高危操作切面处理

```java
@Aspect
@Component
@Slf4j
public class HighRiskOperationAspect {
    
    @Autowired
    private AuditService auditService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private NotificationService notificationService;
    
    @Around("@annotation(highRiskOperation)")
    public Object handleHighRiskOperation(ProceedingJoinPoint joinPoint, 
                                        HighRiskOperation highRiskOperation) throws Throwable {
        
        // 1. 获取当前用户信息
        UserDetails currentUser = SecurityContextHolder.getContext()
            .getAuthentication().getPrincipal();
        
        // 2. 验证用户权限
        validateUserPermission(currentUser, highRiskOperation);
        
        // 3. 验证密码（如果需要）
        if (highRiskOperation.requirePasswordVerify()) {
            validatePassword(currentUser);
        }
        
        // 4. 记录操作前审计日志
        String operationId = UUID.randomUUID().toString();
        auditService.logHighRiskOperationStart(operationId, highRiskOperation, 
            joinPoint.getArgs(), currentUser);
        
        try {
            // 5. 执行原方法
            Object result = joinPoint.proceed();
            
            // 6. 记录成功审计日志
            auditService.logHighRiskOperationSuccess(operationId, result);
            
            // 7. 发送通知（CRITICAL级别）
            if (highRiskOperation.riskLevel() == RiskLevel.CRITICAL) {
                notificationService.notifyAdmins(
                    "高危操作执行", 
                    String.format("用户 %s 执行了 %s 操作", 
                        currentUser.getUsername(), 
                        highRiskOperation.description())
                );
            }
            
            return result;
            
        } catch (Exception e) {
            // 8. 记录失败审计日志
            auditService.logHighRiskOperationFailure(operationId, e);
            throw e;
        }
    }
    
    private void validateUserPermission(UserDetails user, HighRiskOperation operation) {
        // 权限验证逻辑
        if (operation.requireAdminAuth() && !userService.isAdmin(user)) {
            throw new SecurityException("此操作需要管理员权限");
        }
    }
    
    private void validatePassword(UserDetails user) {
        // 从请求头或参数中获取密码进行验证
        String password = getCurrentRequestPassword();
        if (!userService.validatePassword(user.getUsername(), password)) {
            throw new SecurityException("密码验证失败");
        }
    }
}
```

## 5. 具体业务场景应用

### 5.1 商品删除操作

```java
@Service
public class ProductService {

    @HighRiskOperation(
        riskLevel = RiskLevel.HIGH,
        description = "删除商品信息",
        resourceType = "PRODUCT",
        requirePasswordVerify = true
    )
    public void deleteProduct(Long productId, String reason) {
        // 1. 检查商品是否存在关联数据
        if (hasRelatedData(productId)) {
            throw new BusinessException("商品存在关联数据，无法删除");
        }

        // 2. 备份商品数据
        Product product = getById(productId);
        backupService.backupProduct(product);

        // 3. 执行物理删除
        removeById(productId);

        // 4. 清理相关缓存
        cacheService.evictProductCache(productId);
    }

    @HighRiskOperation(
        riskLevel = RiskLevel.CRITICAL,
        description = "批量删除商品",
        resourceType = "PRODUCT",
        requireAdminAuth = true,
        requirePasswordVerify = true
    )
    public void batchDeleteProducts(List<Long> productIds, String reason) {
        // 批量删除逻辑
        for (Long productId : productIds) {
            deleteProduct(productId, reason);
        }
    }
}
```

### 5.2 用户权限变更

```java
@Service
public class UserService {

    @HighRiskOperation(
        riskLevel = RiskLevel.CRITICAL,
        description = "变更用户角色权限",
        resourceType = "USER_ROLE",
        requireAdminAuth = true,
        requirePasswordVerify = true
    )
    public void changeUserRole(Long userId, Long newRoleId, String reason) {
        User user = getById(userId);
        Role oldRole = user.getRole();
        Role newRole = roleService.getById(newRoleId);

        // 记录权限变更历史
        permissionHistoryService.recordRoleChange(userId, oldRole, newRole, reason);

        // 执行权限变更
        user.setRoleId(newRoleId);
        updateById(user);

        // 清理用户权限缓存
        cacheService.evictUserPermissionCache(userId);
    }
}
```

### 5.3 库存清零操作

```java
@Service
public class InventoryService {

    @HighRiskOperation(
        riskLevel = RiskLevel.CRITICAL,
        description = "清零库存",
        resourceType = "INVENTORY",
        requireAdminAuth = true,
        requirePasswordVerify = true
    )
    public void clearInventory(Long warehouseId, Long productId, String reason) {
        // 1. 获取当前库存
        Inventory inventory = getInventory(warehouseId, productId);

        // 2. 备份库存数据
        backupService.backupInventory(inventory);

        // 3. 创建库存变动记录
        InventoryTransaction transaction = InventoryTransaction.builder()
            .warehouseId(warehouseId)
            .productId(productId)
            .transactionType(TransactionType.CLEAR)
            .quantityBefore(inventory.getQuantity())
            .quantityAfter(BigDecimal.ZERO)
            .reason(reason)
            .build();

        inventoryTransactionService.save(transaction);

        // 4. 执行清零操作
        inventory.setQuantity(BigDecimal.ZERO);
        updateById(inventory);
    }
}
```

## 6. 数据备份与恢复机制

### 6.1 操作前数据备份

```java
@Service
public class DataBackupService {

    public void backupBeforeDelete(String tableName, Object entity) {
        DeletedDataBackup backup = DeletedDataBackup.builder()
            .backupId(UUID.randomUUID().toString())
            .tableName(tableName)
            .originalData(JsonUtils.toJson(entity))
            .deletedAt(LocalDateTime.now())
            .deletedBy(getCurrentUserId())
            .retentionDays(90) // 保留90天
            .build();

        deletedDataBackupRepository.save(backup);
    }

    public void restoreDeletedData(String backupId) {
        DeletedDataBackup backup = deletedDataBackupRepository.findByBackupId(backupId);
        if (backup == null) {
            throw new BusinessException("备份数据不存在");
        }

        // 根据表名和数据恢复
        switch (backup.getTableName()) {
            case "products":
                Product product = JsonUtils.fromJson(backup.getOriginalData(), Product.class);
                productService.save(product);
                break;
            case "users":
                User user = JsonUtils.fromJson(backup.getOriginalData(), User.class);
                userService.save(user);
                break;
            // 其他表的恢复逻辑
        }

        // 标记为已恢复
        backup.setRestoredAt(LocalDateTime.now());
        backup.setRestoredBy(getCurrentUserId());
        deletedDataBackupRepository.updateById(backup);
    }
}
```
